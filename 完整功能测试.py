#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整功能测试脚本
测试所有主要功能是否正常工作
"""

import os
import sys

def test_config_loading():
    """测试配置文件加载功能"""
    print("🧪 测试1: 配置文件加载功能")
    print("-" * 40)
    
    try:
        from tencent_meeting_downloader import TencentMeetingDownloader
        
        # 测试从配置文件加载
        downloader = TencentMeetingDownloader()
        print("✅ 配置文件加载成功")
        print(f"📊 Cookie长度: {len(downloader.cookie)} 字符")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件加载失败: {str(e)}")
        return False

def test_aria2_functionality():
    """测试aria2功能"""
    print("\n🧪 测试2: aria2功能")
    print("-" * 40)
    
    try:
        from aria2_downloader import start_aria2_if_needed, Aria2Downloader
        
        # 测试aria2启动
        if start_aria2_if_needed():
            print("✅ aria2启动/连接成功")
            
            # 测试aria2状态
            aria2_test = Aria2Downloader()
            if aria2_test.check_aria2_status():
                print("✅ aria2状态检查正常")
                return True
            else:
                print("❌ aria2状态检查失败")
                return False
        else:
            print("❌ aria2启动失败")
            return False
            
    except Exception as e:
        print(f"❌ aria2功能测试失败: {str(e)}")
        return False

def test_aria2_downloader_init():
    """测试aria2下载器初始化"""
    print("\n🧪 测试3: aria2下载器初始化")
    print("-" * 40)
    
    try:
        from aria2_downloader import TencentMeetingAria2Downloader
        
        # 测试aria2下载器初始化
        downloader = TencentMeetingAria2Downloader()
        print("✅ aria2下载器初始化成功")
        print(f"📊 Cookie长度: {len(downloader.cookie)} 字符")
        print(f"🔗 aria2 RPC地址: {downloader.aria2.rpc_url}")
        return True
        
    except Exception as e:
        print(f"❌ aria2下载器初始化失败: {str(e)}")
        return False

def test_file_structure():
    """测试文件结构完整性"""
    print("\n🧪 测试4: 文件结构完整性")
    print("-" * 40)
    
    required_files = [
        "tencent_meeting_downloader.py",
        "aria2_downloader.py", 
        "download_with_aria2.py",
        "update_cookie.py",
        "config.json",
        "config.example.json",
        "README.md",
        "启动下载器.bat"
    ]
    
    aria2_path = os.path.join("AriaNg-DailyBuild-master", "Aria2", "aria2.exe")
    required_files.append(aria2_path)
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"\n❌ 缺少文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("\n✅ 所有必要文件都存在")
        return True

def test_import_modules():
    """测试模块导入"""
    print("\n🧪 测试5: 模块导入")
    print("-" * 40)
    
    modules_to_test = [
        "tencent_meeting_downloader",
        "aria2_downloader",
        "json",
        "requests",
        "subprocess"
    ]
    
    failed_imports = []
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name}")
        except ImportError as e:
            print(f"❌ {module_name}: {str(e)}")
            failed_imports.append(module_name)
    
    if failed_imports:
        print(f"\n❌ 导入失败的模块: {', '.join(failed_imports)}")
        return False
    else:
        print("\n✅ 所有模块导入成功")
        return True

def main():
    """主测试函数"""
    print("🧪 腾讯会议下载器 - 完整功能测试")
    print("=" * 60)
    
    tests = [
        ("文件结构检查", test_file_structure),
        ("模块导入测试", test_import_modules),
        ("配置文件加载", test_config_loading),
        ("aria2功能测试", test_aria2_functionality),
        ("aria2下载器初始化", test_aria2_downloader_init)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("-" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统功能完整")
        print("\n💡 使用建议:")
        print("   1. 运行 python update_cookie.py 更新cookie")
        print("   2. 运行 python download_with_aria2.py 开始下载")
        print("   3. 或者双击 启动下载器.bat 一键启动")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查配置")
        
        if not any(name == "配置文件加载" and result for name, result in results):
            print("\n💡 如果配置文件加载失败，请:")
            print("   1. 运行 python update_cookie.py")
            print("   2. 按提示配置有效的cookie")

if __name__ == "__main__":
    main()
