#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie更新工具
用于快速更新config.json中的cookie
"""

import json
import os

def update_cookie():
    """
    交互式更新cookie
    """
    config_file = "config.json"
    
    print("🍪 腾讯会议Cookie更新工具")
    print("=" * 50)
    
    # 检查配置文件是否存在
    if not os.path.exists(config_file):
        print(f"❌ 配置文件 {config_file} 不存在")
        print("💡 正在创建新的配置文件...")
        
        # 创建新的配置文件
        config = {
            "cookie": "",
            "description": "腾讯会议下载器配置文件",
            "note": "更新cookie时，请替换上面的cookie字段内容"
        }
    else:
        # 读取现有配置文件
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 已读取现有配置文件")
            
            # 显示当前cookie的前100个字符
            current_cookie = config.get('cookie', '')
            if current_cookie:
                print(f"📋 当前Cookie前100字符: {current_cookie[:100]}...")
            else:
                print("📋 当前配置文件中没有Cookie")
                
        except Exception as e:
            print(f"❌ 读取配置文件失败: {str(e)}")
            return
    
    print("\n📝 请按照以下步骤获取新的Cookie:")
    print("1. 打开浏览器，访问 https://meeting.tencent.com")
    print("2. 登录您的腾讯会议账号")
    print("3. 按F12打开开发者工具")
    print("4. 切换到Network(网络)标签页")
    print("5. 刷新页面或进行任意操作")
    print("6. 在请求列表中选择任意一个请求")
    print("7. 在Request Headers中找到Cookie字段")
    print("8. 复制Cookie的完整值")
    print("\n" + "=" * 50)
    
    # 获取用户输入的新cookie
    print("🔑 请粘贴新的Cookie (按Enter确认):")
    new_cookie = input().strip()
    
    if not new_cookie:
        print("❌ Cookie不能为空")
        return
    
    # 验证cookie格式（简单检查）
    if len(new_cookie) < 100:
        print("⚠️  警告: Cookie长度似乎太短，请确认是否完整")
        confirm = input("是否继续? (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 操作已取消")
            return
    
    # 更新配置
    config['cookie'] = new_cookie
    
    # 保存配置文件
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        
        print(f"✅ Cookie已成功更新到 {config_file}")
        print(f"📊 新Cookie长度: {len(new_cookie)} 字符")
        print(f"📋 新Cookie前100字符: {new_cookie[:100]}...")
        print("\n🚀 现在可以运行主程序了:")
        print("   python tencent_meeting_downloader.py")
        
    except Exception as e:
        print(f"❌ 保存配置文件失败: {str(e)}")

def show_current_cookie():
    """
    显示当前配置的cookie信息
    """
    config_file = "config.json"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件 {config_file} 不存在")
        return
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        cookie = config.get('cookie', '')
        if cookie:
            print(f"📊 当前Cookie长度: {len(cookie)} 字符")
            print(f"📋 Cookie前100字符: {cookie[:100]}...")
            print(f"📋 Cookie后100字符: ...{cookie[-100:]}")
        else:
            print("📋 当前配置文件中没有Cookie")
            
    except Exception as e:
        print(f"❌ 读取配置文件失败: {str(e)}")

def main():
    """
    主函数
    """
    print("🍪 腾讯会议Cookie管理工具")
    print("=" * 50)
    print("1. 更新Cookie")
    print("2. 查看当前Cookie")
    print("3. 退出")
    print("=" * 50)
    
    while True:
        choice = input("请选择操作 (1-3): ").strip()
        
        if choice == '1':
            update_cookie()
            break
        elif choice == '2':
            show_current_cookie()
            break
        elif choice == '3':
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请输入1-3")

if __name__ == "__main__":
    main()
