# 腾讯会议文件下载器 - 完整使用指南

## 📋 项目概述

这是一个功能完整的腾讯会议文件下载器，支持：
- ✅ 下载会议视频（MP4格式）
- ✅ 下载会议音频（M4A格式）
- ✅ 下载会议文档（Word、PDF、TXT格式）
- ✅ 使用aria2进行高速下载
- ✅ 按会议开始时间自动创建目录
- ✅ 规范化文件命名

## 📁 文件说明

- `tencent_meeting_downloader.py` - 核心下载器类
- `aria2_downloader.py` - aria2集成下载器
- `download_cli.py` - 命令行界面（推荐使用）
- `config.py` - 配置文件
- `test_api_direct.py` - API测试脚本
- `test_aria2_only.py` - aria2测试脚本
- `start_aria2.bat` - aria2启动脚本

## 🚀 快速开始

### 第一步：安装aria2（推荐）

#### Windows系统：
1. 下载aria2：https://github.com/aria2/aria2/releases
2. 下载最新的Windows版本（如：aria2-1.36.0-win-64bit-build1.zip）
3. 解压到任意目录（如：`C:\aria2\`）
4. 将aria2c.exe所在目录添加到系统PATH环境变量
5. 或者直接将aria2c.exe复制到项目目录

#### 验证安装：
```bash
aria2c --version
```

### 第二步：获取Cookie

**重要：Cookie会过期，需要定期更新**

1. 打开浏览器，登录腾讯会议网页版
2. 进入会议记录页面：https://meeting.tencent.com/user-center/meeting-record
3. 按 `F12` 打开开发者工具
4. 切换到 `Network`（网络）标签
5. 刷新页面，找到任意一个请求
6. 在请求头中找到 `Cookie` 字段
7. 复制完整的Cookie值

### 第三步：配置Cookie

编辑 `config.py` 文件，将Cookie值替换：

```python
TENCENT_MEETING_COOKIE = "您的Cookie值"
```

### 第四步：启动aria2

#### 方法1：使用批处理文件
双击运行 `start_aria2.bat`

#### 方法2：命令行启动
```bash
aria2c --enable-rpc --rpc-listen-port=6800
```

### 第五步：开始下载

```bash
python download_cli.py
```

## 🔧 详细使用说明

### 命令行界面使用

运行 `python download_cli.py` 后，您将看到：

1. **会议列表**：显示所有可用的会议记录
2. **选择操作**：
   - 选项1：下载指定会议（输入序号）
   - 选项2：下载指定开始时间的会议
   - 选项3：下载所有会议

### 下载指定会议

如果您知道会议的开始时间（如：`1748570479000`），可以：

1. 选择选项2
2. 输入开始时间：`1748570479000`
3. 程序会自动找到并下载该会议的所有文件

### 文件保存结构

程序会自动创建目录，格式为：`YYYYMMDD_HHMMSS_会议标题/`

示例：
```
20250530_100116_AI-诺岚的快速会议/
├── TM-20250530100116-546898734-录制1.mp4
├── 音频_录制1.m4a
├── TM-20250530100116-546898734-录制2.mp4
├── 音频_录制2.m4a
├── AI-诺岚的快速会议_word.docx
├── AI-诺岚的快速会议_pdf.pdf
└── AI-诺岚的快速会议_txt.txt
```

## 🧪 测试功能

### 测试API连接
```bash
python test_api_direct.py
```

### 测试aria2功能
```bash
python test_aria2_only.py
```

## ⚙️ 高级配置

### aria2配置优化

编辑 `config.py` 中的 `ARIA2_CONFIG` 部分：

```python
ARIA2_CONFIG = {
    "download_options": {
        "max-connection-per-server": "16",  # 每服务器最大连接数
        "split": "16",                      # 分段下载数
        "min-split-size": "1M",             # 最小分段大小
        "max-download-limit": "0"           # 下载限速（0=不限速）
    }
}
```

### 下载配置

```python
DOWNLOAD_CONFIG = {
    "download_dir": "./downloads",          # 下载目录
    "retry_count": 3,                       # 重试次数
    "request_interval": 1,                  # 请求间隔（秒）
    "download_timeout": 300                 # 下载超时（秒）
}
```

## 🐛 常见问题解决

### Q: 提示"未找到aria2程序"
**A:** 
1. 确认aria2已正确安装
2. 检查aria2c.exe是否在PATH中
3. 或将aria2c.exe复制到项目目录

### Q: API返回401错误
**A:** Cookie已过期，需要重新获取并更新config.py

### Q: 下载速度慢
**A:** 
1. 检查网络连接
2. 调整aria2配置中的连接数和分段数
3. 确认aria2正常运行

### Q: 某些文件下载失败
**A:** 
1. 检查文件链接是否有效
2. 确认有下载权限
3. 检查存储空间是否充足

### Q: 程序提示"获取会议列表失败"
**A:** 
1. 检查Cookie是否正确
2. 检查网络连接
3. 确认腾讯会议账号正常

## 🔒 注意事项

1. **Cookie安全**：不要分享您的Cookie，它包含登录信息
2. **使用频率**：避免频繁请求，程序已内置请求间隔
3. **存储空间**：视频文件较大，确保有足够空间
4. **网络稳定**：大文件下载需要稳定的网络连接
5. **合法使用**：仅下载您有权限访问的会议文件

## 📞 技术支持

### 检查清单

遇到问题时，请检查：

- [ ] Python版本是否为3.6+
- [ ] aria2是否正确安装
- [ ] Cookie是否最新有效
- [ ] 网络连接是否正常
- [ ] 存储空间是否充足
- [ ] 腾讯会议账号是否正常

### 日志查看

程序运行时会显示详细的状态信息，包括：
- API连接状态
- aria2连接状态
- 下载进度
- 错误信息

### 手动模式

如果自动下载失败，您可以：
1. 运行测试脚本获取下载链接
2. 手动使用aria2或其他下载工具
3. 检查具体的错误信息

## 🎉 使用示例

### 示例1：下载特定会议
```bash
python download_cli.py
# 选择选项2
# 输入：1748570479000
```

### 示例2：批量下载
```bash
python download_cli.py
# 选择选项3
# 确认下载所有会议
```

### 示例3：仅测试连接
```bash
python test_api_direct.py
python test_aria2_only.py
```

---

**祝您使用愉快！** 🎉

如有问题，请检查上述常见问题解决方案或查看程序输出的详细错误信息。
