[global]
AriaNg Version=Версия AriaNg
Operation Result=Результат операции
Operation Succeeded=Операция завершена успешно
is connected=Подключено
Error=Ошибка
OK=OK
Confirm=Подтвердить
Cancel=Отмена
Close=Закрыть
True=Истина
False=Ложь
DEBUG=Отладка
INFO=Информация
WARN=Предупреждение
ERROR=Ошибка
Connecting=Подключение
Connected=Подключено
Disconnected=Отключено
Reconnecting=Повторное подключение
Waiting to reconnect=Ожидание повторного подключения
Global=Глобально
New=Новый
Start=Запуск
Pause=Пауза
Retry=Повтор
Retry Selected Tasks=Повторить выбранные задачи
Delete=Удалить
Select All=Выбрать все
Select None=Отменить выбор всех
Select Invert=Инвертировать выбор
Select All Failed Tasks=Выбрать все неудавшиеся задачи
Select All Completed Tasks=Выбрать все завершенные задачи
Select All Tasks=Выбрать все задачи
Display Order=Порядок отображения
Copy Download Url=Копировать URL загрузки
Copy Magnet Link=Копировать магнитную ссылку
Help=Помощь
Search=Поиск
Default=По умолчанию
Expand=Развернуть
Collapse=Свернуть
Expand All=Развернуть все
Collapse All=Свернуть все
Open=Открыть
Save=Сохранить
Import=Импорт
Remove Task=Удалить задачу
Remove Selected Task=Удалить выбранную задачу
Clear Stopped Tasks=Очистить остановленные задачи
Click to view task detail=Нажмите для просмотра деталей задачи
By File Name=По имени файла
By File Size=По размеру файла
By Progress=По прогрессу
By Selected Status=По выбранному статусу
By Remaining=По оставшемуся времени
By Download Speed=По скорости загрузки
By Upload Speed=По скорости отдачи
By Peer Address=По адресу пира
By Client Name=По имени клиента
Filters=Фильтры
Download=Загрузка
Upload=Отдача
Downloading=Загрузка
Pending Verification=Ожидание проверки
Verifying=Проверка
Seeding=Раздача
Waiting=Ожидание
Paused=На паузе
Completed=Завершено
Error Occurred=Произошла ошибка
Removed=Удалено
Finished / Stopped=Завершено / Остановлено
Uncompleted=Не завершено
Click to pin=Нажмите, чтобы закрепить
Settings=Настройки
AriaNg Settings=Настройки AriaNg
Aria2 Settings=Настройки Aria2
Basic Settings=Базовые настройки
HTTP/FTP/SFTP Settings=Настройки HTTP/FTP/SFTP
HTTP Settings=Настройки HTTP
FTP/SFTP Settings=Настройки FTP/SFTP
BitTorrent Settings=Настройки BitTorrent
Metalink Settings=Настройки Metalink
RPC Settings=Настройки RPC
Advanced Settings=Расширенные настройки
AriaNg Debug Console=Консоль отладки AriaNg
Aria2 Status=Статус Aria2
File Name=Имя файла
File Size=Размер файла
Progress=Прогресс
Share Ratio=Коэффициент раздачи
Remaining=Оставшееся время
Download Speed=Скорость загрузки
Upload Speed=Скорость отдачи
Links=Ссылки
Torrent File=Файл торрент
Metalink File=Файл Metalink
File Name:=Имя файла:
Options=Опции
Overview=Обзор
Pieces=Информация о блоках
Files=Список файлов
Peers=Состояние соединения
Task Name=Имя задачи
Task Size=Размер задачи
Task Status=Статус задачи
Error Description=Описание ошибки
Health Percentage=Процент целостности
Info Hash=Хэш информация
Seeders=Количество сидов
Connections=Количество соединений
Seed Creation Time=Время создания сида
Download Url=URL загрузки
Download Dir=Каталог загрузки
BT Tracker Servers=Серверы трекеров BT
Copy=Копировать
(Choose Files)=(Выбрать файлы)
Videos=Видео
Audios=Аудио
Pictures=Изображения
Documents=Документы
Applications=Приложения
Archives=Архивы
Other=Другое
Custom=Пользовательский
Custom Choose File=Выбрать пользовательский файл
Address=Адрес
Client=Клиент
Status=Статус
Speed=Скорость
(local)=(Локально)
No Data=Нет данных
No connected peers=Нет подключенных пиров
Failed to change some tasks state.=Не удалось изменить состояние некоторых задач.
Confirm Retry=Подтвердить повтор
Are you sure you want to retry the selected task? AriaNg will create same task after clicking OK.=Вы уверены, что хотите повторить выбранную задачу? AriaNg создаст ту же задачу после нажатия OK.
Failed to retry this task.=Не удалось повторить эту задачу.
{successCount} tasks have been retried and {failedCount} tasks are failed.={{successCount}} задачи были повторены, и {{failedCount}} задачи не удалось повторить.
Confirm Remove=Подтвердить удаление
Are you sure you want to remove the selected task?=Вы уверены, что хотите удалить выбранную задачу?
Failed to remove some task(s).=Не удалось удалить некоторые задачи.
Confirm Clear=Подтвердить очистку
Are you sure you want to clear stopped tasks?=Вы уверены, что хотите очистить остановленные задачи?
Download Links:=Ссылки для загрузки:
Download Now=Скачать сейчас
Download Later=Скачать позже
Open Torrent File=Открыть файл торрент
Open Metalink File=Открыть файл Metalink
Support multiple URLs, one URL per line.=Поддержка нескольких URL, один URL на строку.
Your browser does not support loading file!=Ваш браузер не поддерживает загрузку файла!
The selected file type is invalid!=Выбранный тип файла недействителен!
Failed to load file!=Не удалось загрузить файл!
Download Completed=Загрузка завершена
BT Download Completed=BT загрузка завершена
Download Error=Ошибка загрузки
AriaNg Url=URL AriaNg
Command API Url=URL API команд
Export Command API=Экспорт API команд
Export=Экспорт
Copied=Скопировано
Pause After Task Created=Пауза после создания задачи
Language=Язык
Theme=Тема
Light=Светлый
Dark=Темный
Follow system settings=Следовать настройкам системы
Debug Mode=Режим отладки
Page Title=Заголовок страницы
Preview=Предпросмотр
Tips: You can use the "noprefix" tag to ignore the prefix, "nosuffix" tag to ignore the suffix, and "scale\=n" tag to set the decimal precision.=Советы: вы можете использовать тег "noprefix" для игнорирования префикса, тег "nosuffix" для игнорирования суффикса и тег "scale\=n" для установки точности десятичных знаков.
Example: ${downspeed:noprefix:nosuffix:scale\=1}=Пример: ${downspeed:noprefix:nosuffix:scale\=1}
Updating Page Title Interval=Интервал обновления заголовка страницы
Enable Browser Notification=Включить уведомление браузера
Browser Notification Sound=Звук уведомления браузера
Browser Notification Frequency=Частота уведомления браузера
Unlimited=Неограниченно
High (Up to 10 Notifications / 1 Minute)=Высокая (до 10 уведомлений / 1 минута)
Middle (Up to 1 Notification / 1 Minute)=Средняя (до 1 уведомления / 1 минута)
Low (Up to 1 Notification / 5 Minutes)=Низкая (до 1 уведомления / 5 минут)
WebSocket Auto Reconnect Interval=Интервал автоматического переподключения WebSocket
Aria2 RPC Alias=Псевдоним Aria2 RPC
Aria2 RPC Address=Адрес Aria2 RPC
Aria2 RPC Protocol=Протокол Aria2 RPC
Aria2 RPC Http Request Method=Метод HTTP-запроса Aria2 RPC
POST method only supports aria2 v1.15.2 and above.=Метод POST поддерживается только в Aria2 версии 1.15.2 и выше.
Aria2 RPC Request Headers=Заголовки HTTP-запросов Aria2 RPC
Support multiple request headers, one header per line, each line containing "header name: header value".=Поддержка нескольких заголовков запроса, один заголовок на строку, каждая строка содержит "имя заголовка: значение".
Aria2 RPC Secret Token=Секретный токен Aria2 RPC
Activate=Активировать
Reset Settings=Сбросить настройки
Confirm Reset=Подтвердить сброс
Are you sure you want to reset all settings?=Вы уверены, что хотите сбросить все настройки?
Clear Settings History=Очистить историю настроек
Are you sure you want to clear all settings history?=Вы уверены, что хотите очистить всю историю настроек?
Delete RPC Setting=Удалить настройку RPC
Add New RPC Setting=Добавить новую настройку RPC
Are you sure you want to remove rpc setting "{rpcName}"?=Вы уверены, что хотите удалить настройку rpc "{{rpcName}}"?
Updating Global Stat Interval=Интервал обновления глобальной статистики
Updating Task Information Interval=Интервал обновления информации о задаче
Keyboard Shortcuts=Горячие клавиши
Supported Keyboard Shortcuts=Поддерживаемые горячие клавиши
Set Focus On Search Box=Установить фокус на поисковое поле
Swipe Gesture=Жест смахивания
Change Tasks Order by Drag-and-drop=Изменить порядок задач перетаскиванием
Action After Creating New Tasks=Действие после создания новых задач
Navigate to Task List Page=Перейти на страницу списка задач
Navigate to Task Detail Page=Перейти на страницу деталей задачи
Action After Retrying Task=Действие после повторной попытки задачи
Navigate to Downloading Tasks Page=Перейти на страницу загрузки задач
Stay on Current Page=Остаться на текущей странице
Remove Old Tasks After Retrying=Удалить старые задачи после повторной попытки
Confirm Task Removal=Подтвердить удаление задачи
Include Prefix When Copying From Task Details=Включить префикс при копировании из деталей задачи
Show Pieces Info In Task Detail Page=Показать информацию о частях на странице деталей задачи
Pieces Amount is Less than or Equal to {value}=Количество частей меньше или равно {{value}}
RPC List Display Order=Порядок отображения списка RPC
Each Task List Page Uses Independent Display Order=Каждая страница списка задач использует независимый порядок отображения
Recently Used=Недавно использованные
RPC Alias=Псевдоним RPC
Import / Export AriaNg Settings=Импорт / Экспорт настроек AriaNg
Import Settings=Импорт настроек
Export Settings=Экспорт настроек
AriaNg settings data=Данные настроек AriaNg
Confirm Import=Подтвердить импорт
Are you sure you want to import all settings?=Вы уверены, что хотите импортировать все настройки?
Invalid settings data format!=Недопустимый формат данных настроек!
Data has been copied to clipboard.=Данные были скопированы в буфер обмена.
Supported Placeholder=Поддерживаемое заполнение
AriaNg Title=Название AriaNg
Current RPC Alias=Текущий псевдоним RPC
Downloading Count=Количество загрузок
Waiting Count=Количество ожиданий
Stopped Count=Количество остановок
You have disabled notification in your browser. You should change your browser's settings before you enable this function.=Вы отключили уведомления в вашем браузере. Вам следует изменить настройки браузера, прежде чем включить эту функцию.
Language resource has been updated, please reload the page for the changes to take effect.=Языковой ресурс был обновлен, пожалуйста, перезагрузите страницу, чтобы изменения вступили в силу.
Configuration has been modified, please reload the page for the changes to take effect.=Конфигурация была изменена, пожалуйста, перезагрузите страницу, чтобы изменения вступили в силу.
Reload AriaNg=Перезагрузить AriaNg
Show Secret=Показать секрет
Hide Secret=Скрыть секрет
Aria2 Version=Версия Aria2
Enabled Features=Включенные функции
Operations=Операции
Reconnect=Переподключение
Save Session=Сохранить сессию
Shutdown Aria2=Выключить Aria2
Confirm Shutdown=Подтвердить завершение работы
Are you sure you want to shutdown aria2?=Вы уверены, что хотите выключить aria2?
Session has been saved successfully.=Сессия успешно сохранена.
Aria2 has been shutdown successfully.=Aria2 успешно завершил работу.
Toggle Navigation=Переключить навигацию
Shortcut=Ярлык
Global Rate Limit=Глобальное ограничение скорости
Loading=Загрузка
More Than One Day=Более одного дня
Unknown=Неизвестно
Bytes=Байты
Hours=Часы
Minutes=Минуты
Seconds=Секунды
Milliseconds=Миллисекунды
Http=Http
Http (Disabled)=Http (Отключено)
Https=Https
WebSocket=WebSocket
WebSocket (Disabled)=WebSocket (Отключено)
WebSocket (Security)=WebSocket (Безопасность)
Http and WebSocket would be disabled when accessing AriaNg via Https.=Http и WebSocket будут отключены при доступе к AriaNg через Https.
POST=POST
GET=GET
Enabled=Включено
Disabled=Отключено
Always=Всегда
Never=Никогда
BitTorrent=BitTorrent
Changes to the settings take effect after refreshing page.=Изменения в настройках вступят в силу после обновления страницы.
Logging Time=Время регистрации
Log Level=Уровень логов
Auto Refresh=Автоматическое обновление
Refresh Now=Обновить сейчас
Clear Logs=Очистить логи
Are you sure you want to clear debug logs?=Вы уверены, что хотите очистить логи отладки?
Show Detail=Показать подробности
Log Detail=Детали логов
Aria2 RPC Debug=Отладка Aria2 RPC
Aria2 RPC Request Method=Метод запроса Aria2 RPC
Aria2 RPC Request Parameters=Параметры запроса Aria2 RPC
Aria2 RPC Response=Ответ Aria2 RPC
Execute=Выполнить
RPC method is illegal!=Метод RPC недопустим!
AriaNg does not support this RPC method!=AriaNg не поддерживает этот метод RPC!
RPC request parameters are invalid!=Параметры запроса RPC недействительны!
Type is illegal!=Тип недопустим!
Parameter is invalid!=Параметр недействителен!
Option value cannot be empty!=Значение параметра не может быть пустым!
Input number is invalid!=Введенный номер недействителен!
Input number is below min value!=Введенное значение ниже минимального {{value}}!
Input number is above max value!=Введенное значение выше максимального {{value}}!
Input value is invalid!=Введенное значение недействительно!
Protocol is invalid!=Протокол недействителен!
RPC host cannot be empty!=RPC хост не может быть пустым!
RPC secret is not base64 encoded!=RPC секрет не закодирован в base64!
URL is not base64 encoded!=URL не закодирован в base64!
Tap to configure and get started with AriaNg.=Нажмите, чтобы настроить и начать использовать AriaNg.
Cannot initialize WebSocket!=Не удается инициализировать WebSocket!
Cannot connect to aria2!=Не удается подключиться к aria2!
Access Denied!=Доступ запрещен!
You cannot use AriaNg because this browser does not meet the minimum requirements for data storage.=Вы не можете использовать AriaNg, потому что этот браузер не соответствует минимальным требованиям для хранения данных.

[error]
unknown=Неизвестная ошибка.
operation.timeout=Операция завершилась по таймауту.
resource.notfound=Не удалось найти указанный ресурс.
resource.notfound.max-file-not-found=Не удалось найти указанный ресурс. См. опцию --max-file-not-found.
download.aborted.lowest-speed-limit=Загрузка прервана из-за слишком низкой скорости загрузки. См. опцию --lowest-speed-limit.
network.problem=Проблема с сетью.
resume.notsupported=Сервер не поддерживает возобновление загрузки.
space.notenough=Недостаточно свободного места на диске.
piece.length.different=Длина части отличается от указанной в управляющем файле .aria2. См. опцию --allow-piece-length-change.
download.sametime=aria2 уже загружает другой файл с такими же данными.
download.torrent.sametime=aria2 уже загружает другой торрент-файл с таким же хэшем.
file.exists=Файл уже существует. См. опцию --allow-overwrite.
file.rename.failed=Ошибка при переименовании файла. См. опцию --auto-file-renaming.
file.open.failed=Ошибка при открытии файла.
file.create.failed=Ошибка при создании файла или удалении существующего файла.
io.error=Ошибка файловой системы.
directory.create.failed=Не удалось создать указанный каталог.
name.resolution.failed=Ошибка при разрешении имени.
metalink.file.parse.failed=Ошибка при анализе файла Metalink.
ftp.command.failed=Ошибка при выполнении команды FTP.
http.response.header.bad=Неверный или нераспознанный заголовок ответа HTTP.
redirects.toomany=Слишком много переадресаций для указанного URL.
http.authorization.failed=Ошибка аутентификации HTTP.
bencoded.file.parse.failed=Ошибка при анализе торрент-файла.
torrent.file.corrupted=Указанный торрент-файл ".torrent" поврежден или не содержит необходимую информацию для aria2.
magnet.uri.bad=Указанный магнитный URI недействителен.
option.bad=Ошибка настройки.
server.overload=Удаленный сервер слишком загружен, чтобы обработать текущий запрос.
rpc.request.parse.failed=Ошибка при разборе RPC-запроса.
checksum.failed=Ошибка проверки контрольной суммы файла.

[languages]
Czech=Че́шский
German=Немецкий
English=Английский
Spanish=испанский
French=Французский
Italian=Итальянский
Polish=Польский
Russian=Русский
Simplified Chinese=Упрощенный китайский
Traditional Chinese=Традиционный китайский

[format]
longdate=DD/MM/YYYY HH:mm:ss
time.millisecond={{value}} миллисекунда
time.milliseconds={{value}} миллисекунд
time.second={{value}} секунда
time.seconds={{value}} секунд
time.minute={{value}} минута
time.minutes={{value}} минут
time.hour={{value}} час
time.hours={{value}} часов
requires.aria2-version=Требуется версия {{version}} aria2
task.new.download-links=Ссылки для загрузки ({{count}} ссылки):
task.pieceinfo=Завершено: {{completed}}, Всего: {{total}} блоков
task.error-occurred=Произошла ошибка ({{errorcode}})
task.verifying-percent=Верификация в процессе ({{verifiedPercent}}%)
settings.file-count=({{count}} файлов)
settings.total-count=(Всего: {{count}})
debug.latest-logs=Последние {{count}} логов

[rpc.error]
unauthorized=Аутентификация не удалась!

[option]
true=Да
false=Нет
default=По умолчанию
none=Нет
hide=Скрыть
full=Полный
http=Http
https=Https
ftp=Ftp
mem=Только память
get=GET
tunnel=ТУННЕЛЬ
plain=Простой текст
arc4=ARC4
binary=Бинарный
ascii=ASCII
debug=Отладка
info=Информация
notice=Уведомление
warn=Предупреждение
error=Ошибка
adaptive=Адаптивный
epoll=epoll
falloc=falloc
feedback=Обратная связь
geom=Геометрия
inorder=По порядку
kqueue=kqueue
poll=poll
port=port
prealloc=prealloc
random=Случайный
select=select
trunc=trunc
SSLv3=SSLv3
TLSv1=TLSv1
TLSv1.1=TLSv1.1
TLSv1.2=TLSv1.2

[options]
dir.name=Путь загрузки
dir.description=Указывает директорию, в которую будут сохраняться загруженные файлы.
log.name=Файл логов
log.description=Путь к файлу логов. Если установлено "-", логи будут записаны в stdout. Если установлено пустое значение (""), логи не будут сохраняться на диск.
max-concurrent-downloads.name=Максимальное количество одновременных загрузок
max-concurrent-downloads.description=Устанавливает максимальное количество файлов, которые aria2 будет загружать одновременно.
check-integrity.name=Проверка целостности
check-integrity.description=Проверяет целостность файла путем проверки хэша каждого блока или всего файла. Эта опция действует только для BT, Metalink и HTTP(S)/FTP ссылок, которые настроили опцию --checksum.
continue.name=Возобновить загрузку
continue.description=Возобновляет загрузку частично загруженных файлов. Включение этой опции позволяет возобновить загрузку файлов, скачанных последовательно браузером или другими программами. Эта опция в настоящее время поддерживается только для файлов, загруженных через HTTP(S)/FTP.
all-proxy.name=Прокси-сервер
all-proxy.description=Устанавливает адрес прокси-сервера для всех протоколов. Также можно переопределить эту опцию для конкретных протоколов, используя опции --http-proxy, --https-proxy и --ftp-proxy. Это настройка будет влиять на все загрузки. Формат адреса прокси-сервера: [http://][USER:PASSWORD@]HOST[:PORT].
all-proxy-user.name=Имя пользователя прокси-сервера
all-proxy-user.description=Устанавливает имя пользователя для аутентификации при подключении ко всем прокси-серверам.
all-proxy-passwd.name=Пароль прокси-сервера
all-proxy-passwd.description=Устанавливает пароль для аутентификации при подключении ко всем прокси-серверам.
checksum.name=Контрольная сумма
checksum.description=Устанавливает контрольную сумму. Формат значения опции TIPO=DIGEST. TIPO - это поддерживаемый тип хэша, перечисленный в aria2c -v hash алгоритмах. DIGEST - это шестнадцатеричный дайджест. Например, установить sha-1 хэш будет так: sha-1=0192ba11326fe2298c8cb4de616f4d4140213838. Эта опция действует только для HTTP(S)/FTP загрузок.
connect-timeout.name=Тайм-аут подключения
connect-timeout.description=Устанавливает тайм-аут (в секундах) для установки соединения с HTTP/FTP/прокси сервером. После установления соединения, эта опция больше не будет действовать, используйте опцию --timeout.
dry-run.name=Пробный запуск
dry-run.description=Если установлено "да", aria2 только проверит наличие удаленного файла без загрузки его содержимого. Эта опция действует только для HTTP/FTP загрузок. Если установлено true, BT загрузки будут немедленно прерваны.
lowest-speed-limit.name=Минимальный лимит скорости
lowest-speed-limit.description=Закрывает соединение, если скорость загрузки ниже установленного значения (в Б/с). 0 означает отсутствие минимального лимита скорости. Можно добавлять единицы измерения, такие как K или M (1K=1024, 1M=1024K). Эта опция не влияет на BT загрузки.
max-connection-per-server.name=Максимальное количество соединений на сервер
max-connection-per-server.description=Устанавливает максимальное количество соединений, которые aria2 может одновременно устанавливать с одним сервером для загрузки одного файла. Это помогает оптимизировать скорость загрузки, предотвращая излишнюю нагрузку на сервер.
max-file-not-found.name=Попытки повторного поиска файла не найдены
max-file-not-found.description=Если aria2 получает статус "файл не найден" от удаленного HTTP/FTP сервера больше раз, чем установлено в этой опции, загрузка завершится с ошибкой. Установка 0 отключит эту опцию. Эта опция влияет только на HTTP/FTP серверы. Повторные попытки будут записаны вместе с количеством попыток, поэтому необходимо установить опцию --max-tries.
max-tries.name=Максимальное количество попыток
max-tries.description=Устанавливает максимальное количество попыток. 0 означает отсутствие ограничений.
min-split-size.name=Минимальный размер деления файла
min-split-size.description=aria2 не будет делить файлы размером меньше 2*РАЗМЕР байт. Например, если размер файла 20 МБ, а РАЗМЕР равен 10M, aria2 разделит файл на 2 сегмента [0-10MB) и [10MB-20MB) и будет использовать 2 источника для загрузки (если --split >= 2). Если РАЗМЕР равен 15M, то, поскольку 2*15M > 20MB, aria2 не будет делить файл и использует 1 источник для загрузки. Можно указывать единицы измерения, такие как K или M (1K=1024, 1M=1024K). Допустимые значения: от 1M до 1024M.
netrc-path.name=Путь к файлу .netrc
netrc-path.description=Указывает путь к файлу .netrc, который будет использоваться для аутентификации при подключении к серверу.
no-netrc.name=Отключить netrc
no-netrc.description=Отключает использование файла .netrc для аутентификации. Если установлено, aria2 не будет искать и использовать этот файл для аутентификации.
no-proxy.name=Список серверов, для которых не использовать прокси
no-proxy.description=Устанавливает имена хостов, доменные имена, сетевые адреса с маской подсети или без нее, для которых не использовать прокси-сервер. Используйте запятую для разделения нескольких записей.
out.name=Имя файла
out.description=Имя загруженного файла. Оно всегда относительно пути, установленного опцией --dir. Эта опция недействительна при использовании опции --force-sequential.
proxy-method.name=Метод запроса прокси-сервера
proxy-method.description=Устанавливает метод запроса для использования прокси-сервером. Метод может быть установлен как GET или TUNNEL. Загрузки HTTPS игнорируют эту опцию и всегда используют TUNNEL.
remote-time.name=Получить время файла с сервера
remote-time.description=Получает временную метку удаленного файла от HTTP/FTP службы и устанавливает ее на локальный файл, если доступно
reuse-uri.name=Повторное использование URI
reuse-uri.description=Когда все предоставленные URI использованы, продолжайте использовать уже использованные URI.
retry-wait.name=Время ожидания перед повторной попыткой
retry-wait.description=Устанавливает интервал времени (в секундах) между попытками повторной попытки. Когда установлено значение больше 0, aria2 будет пытаться снова при получении ответа 503 от HTTP сервера.
server-stat-of.name=Сохранить состояние сервера
server-stat-of.description=Указывает имя файла для сохранения состояния сервера. Можно использовать параметр --server-stat-if для чтения сохраненных данных.
server-stat-timeout.name=Тайм-аут состояния сервера
server-stat-timeout.description=Указывает время истечения состояния сервера (в секундах).
split.name=Соединения для загрузки
split.description=Использует N соединений для загрузки. Если предоставлены более N URI адресов, будут использованы первые N адресов, остальные будут использоваться как резервные. Если предоставлено меньше N URI адресов, они будут повторно использоваться для обеспечения одновременной активности N соединений. Количество соединений к одному серверу ограничено опцией --max-connection-per-server.
stream-piece-selector.name=Алгоритм выбора частей
stream-piece-selector.description=Указывает алгоритм выбора частей для загрузки HTTP/FTP. Части - это сегменты фиксированной длины во время параллельной загрузки. Если установлено значение "по умолчанию", aria2 выберет части, уменьшая количество соединений. Так как создание соединений затратно, это разумное поведение по умолчанию. Если установлено значение "последовательно", aria2 выберет части с наименьшим индексом. Индекс 0 указывает на первую часть файла. Это полезно для потокового видео. Опция --enable-http-pipelining помогает уменьшить расходы на переподключение. Обратите внимание, что aria2 зависит от опции --min-split-size, поэтому необходимо установить разумное значение для --min-split-size. Если установлено значение "случайно", aria2 выберет случайную часть. Как и "последовательно", зависит от опции --min-split-size. Если установлено значение "геометрически", aria2 сначала выберет часть с наименьшим индексом, затем зарезервирует место для выбранных ранее частей, используя экспоненциальный рост. Это уменьшит количество соединений, в то время как первые части файла будут загружаться первыми. Это также полезно для потокового видео.
timeout.name=Тайм-аут
timeout.description=Устанавливает таймаут для всех сетевых операций. Если операция не завершается в течение указанного времени, она будет прервана. Значение указывается в секундах.
uri-selector.name=Алгоритм выбора URI
uri-selector.description=Указывает алгоритм выбора URI. Возможные значения включают "последовательно", "отзыв" и "адаптивно". Если установлено значение "последовательно", URI будут использоваться в порядке их появления в списке. Если установлено значение "отзыв", aria2 выберет сервер с самой быстрой скоростью загрузки из списка URI, игнорируя недействительные зеркала. Предыдущая измеренная скорость загрузки будет частью файла состояния сервера, см. опции --server-stat-of и --server-stat-if. Если установлено значение "адаптивно", будет выбран лучший зеркало и ожидающее соединение. Обратите внимание, что возвращенные зеркала не проверены и будут повторно проверены только если все зеркала уже были проверены. Например, "отзыв" использует файл состояния сервера.
check-certificate.name=Проверка сертификата
check-certificate.description=Устанавливает, будет ли aria2 проверять SSL-сертификаты при соединении с HTTPS-серверами. Если установлено "true", aria2 будет проверять сертификаты, если "false" — будет игнорировать их.
http-accept-gzip.name=Принимать GZip
http-accept-gzip.description=Если заголовок ответа удаленного сервера включает Content-Encoding: gzip или Content-Encoding: deflate, отправляет запросы с заголовками Accept: deflate, gzip и распаковывает ответ.
http-auth-challenge.name=Вызов аутентификации HTTP
http-auth-challenge.description=Отправляет заголовки запросов аутентификации HTTP только по запросу сервера. Если установлено значение "нет", всегда отправляет заголовки запросов аутентификации. Исключение: если имя пользователя и пароль включены в URI, эта опция игнорируется, и заголовки запросов аутентификации отправляются всегда.
http-no-cache.name=Отключить кеш
http-no-cache.description=Заголовки запросов будут содержать Cache-Control: no-cache и Pragma: no-cache, чтобы избежать кеширования. Если установлено значение "нет", вышеуказанные заголовки запросов не будут отправлены, и опция --header может быть использована для добавления заголовка Cache-Control.
http-user.name=Имя пользователя HTTP по умолчанию
http-user.description=Устанавливает имя пользователя для аутентификации при подключении к HTTP-серверам.
http-passwd.name=Пароль HTTP по умолчанию
http-passwd.description=Устанавливает пароль для аутентификации при подключении к HTTP-серверам.
http-proxy.name=Прокси-сервер HTTP
http-proxy.description=Устанавливает прокси-сервер для HTTP-соединений. Укажите адрес прокси-сервера, через который будут проходить HTTP-запросы.
http-proxy-user.name=Имя пользователя прокси-сервера HTTP
http-proxy-user.description=Устанавливает имя пользователя для аутентификации при подключении к HTTP-прокси-серверу.
http-proxy-passwd.name=Пароль прокси-сервера HTTP
http-proxy-passwd.description=Устанавливает пароль для аутентификации при подключении к HTTP-прокси-серверу.
https-proxy.name=Прокси-сервер HTTPS
https-proxy.description=Устанавливает прокси-сервер для HTTPS-соединений. Укажите адрес прокси-сервера, через который будут проходить HTTPS-запросы.
https-proxy-user.name=Имя пользователя прокси-сервера HTTPS
https-proxy-user.description=Устанавливает имя пользователя для аутентификации при подключении к HTTPS-прокси-серверу.
https-proxy-passwd.name=Пароль прокси-сервера HTTPS
https-proxy-passwd.description=Устанавливает пароль для аутентификации при подключении к HTTPS-прокси-серверу.
referer.name=Referer
referer.description=Устанавливает заголовок Referer, который будет отправляться при запросах к веб-серверу. Это может быть полезно для имитации настоящих запросов из браузера.
enable-http-keep-alive.name=Включить HTTP keep-alive
enable-http-keep-alive.description=Включает keep-alive HTTP/1.1.
enable-http-pipelining.name=Включить HTTP pipelining
enable-http-pipelining.description=Включает HTTP/1.1 pipelining.
header.name=Пользовательские заголовки
header.description=Добавляет содержимое заголовка HTTP запроса. Каждая строка представляет опцию, содержащую "имя заголовка: значение заголовка".
save-cookies.name=Путь сохранения cookie
save-cookies.description=Сохраняет cookie в файл в формате Mozilla/Firefox(1.x/2.x)/Netscape. Если файл уже существует, он будет перезаписан. Истекшие cookie также будут сохранены, но их время истечения будет установлено на 0.
use-head.name=Использовать метод HEAD
use-head.description=Использует метод HEAD при первом запросе к HTTP серверу.
user-agent.name=Пользовательский агент
user-agent.description=Устанавливает строку User-Agent, которую aria2 будет использовать при взаимодействии с веб-серверами.
ftp-user.name=Имя пользователя FTP по умолчанию
ftp-user.description=Устанавливает имя пользователя, которое будет использоваться по умолчанию для аутентификации при подключении к FTP-серверам.
ftp-passwd.name=Пароль FTP по умолчанию
ftp-passwd.description=Если URI содержит только имя пользователя без пароля, aria2 сначала попытается найти пароль в файле .netrc. Если пароль найден в файле .netrc, он будет использован. В противном случае будет использован пароль, установленный с помощью этой опции.
ftp-pasv.name=Пассивный режим
ftp-pasv.description=Использует пассивный режим в FTP. Если установлено значение "нет", будет использован активный режим. Эта опция не применяется к передачам SFTP.
ftp-proxy.name=Прокси-сервер FTP
ftp-proxy.description=Устанавливает прокси-сервер для FTP-соединений. Укажите адрес прокси-сервера, через который будут проходить FTP-запросы.
ftp-proxy-user.name=Имя пользователя прокси-сервера FTP
ftp-proxy-user.description=Устанавливает имя пользователя для аутентификации при подключении к FTP-прокси-серверу.
ftp-proxy-passwd.name=Пароль прокси-сервера FTP
ftp-proxy-passwd.description=Устанавливает пароль для аутентификации при подключении к FTP-прокси-серверу.
ftp-type.name=Тип передачи
ftp-type.description=Устанавливает тип передачи для FTP-соединений. Возможные значения: passive или active.
ftp-reuse-connection.name=Повторное использование соединения
ftp-reuse-connection.description=Разрешает повторное использование одного FTP-соединения для нескольких загрузок или выгрузок, улучшая производительность.
ssh-host-key-md.name=Контрольная сумма публичного ключа SSH
ssh-host-key-md.description=Устанавливает контрольную сумму публичного ключа SSH сервера. Формат опции: TIPO=DIGEST. TIPO - это тип хэша. Поддерживаемые типы хэшей: sha-1 и md5. DIGEST - это шестнадцатеричный дайджест. Например: sha-1=b030503d4de4539dc7885e6f0f5e256704edf4c3. Эта опция может быть использована для проверки публичного ключа сервера при использовании SFTP. Если эта опция не установлена, будет использована проверка по умолчанию.
bt-detach-seed-only.name=Отделить только задачи посева
bt-detach-seed-only.description=Исключает только задачи посева при подсчете активных задач загрузки (см. опцию -j). Это означает, что если параметр установлен на -j3 и в настоящее время есть 3 активные задачи, одна из которых в режиме посева, она будет исключена (т.е. число станет 2) и следующая задача в очереди будет запущена. Однако важно отметить, что в методах RPC задачи посева по-прежнему считаются активными.
bt-enable-hook-after-hash-check.name=Включить событие по окончанию проверки хэша
bt-enable-hook-after-hash-check.description=Позволяет выполнять команду после завершения проверки хэша загрузок BT (см. опцию -V). По умолчанию, когда проверка хэша успешна, будет выполнена команда, установленная через --on-bt-download-complete. Чтобы отключить это поведение, установите значение "нет".
bt-enable-lpd.name=Включить обнаружение локальных узлов (LPD)
bt-enable-lpd.description=Включает или отключает использование локальной передачи данных (LPD) для обмена информацией о пирах в сети BitTorrent.
bt-exclude-tracker.name=Исключить адреса трекеров BT
bt-exclude-tracker.description=Исключенные адреса трекеров BT, разделенные запятыми. Можно использовать * для совпадения всех адресов, тем самым исключая все адреса трекеров. При использовании * в командной строке оболочки необходимо использовать экранирование или кавычки.
bt-external-ip.name=Внешний IP-адрес
bt-external-ip.description=Указывает внешний IP-адрес, используемый для загрузок BitTorrent и DHT. Может быть отправлен на серверы BitTorrent. Для DHT эта опция сообщит локальным узлам о загрузке определенного торрента. Это важно для использования DHT в частных сетях. Хотя называется "внешний", принимает различные типы IP-адресов.
bt-force-encryption.name=Принудительное шифрование
bt-force-encryption.description=Содержимое BT сообщений должно быть зашифровано с использованием arc4. Эта опция является быстрым способом установки --bt-require-crypto --bt-min-crypto-level=arc4. Не изменяет содержимое этих двух опций. Если установлено значение "да", отклоняет предыдущие BT рукопожатия и использует только замаскированные рукопожатия и зашифрованные сообщения.
bt-hash-check-seed.name=Проверка хэша перед посевом
bt-hash-check-seed.description=Если установлено значение "да", aria2 продолжит посев только после завершения проверки хэша и завершения файла, используя опцию --check-integrity. Если необходимо проверять только файлы, когда они повреждены или неполные, установите значение "нет". Эта опция действует только для загрузок BT.
bt-load-saved-metadata.name=Загрузка сохраненных метаданных
bt-load-saved-metadata.description=При использовании магнитной загрузки сначала пытается загрузить сохраненный файл с опцией --bt-save-metadata перед загрузкой метаданных из DHT. Если загрузка файла успешна, метаданные не будут загружены из DHT.
bt-max-open-files.name=Максимальное количество открытых файлов
bt-max-open-files.description=Устанавливает максимальное количество открытых файлов глобально для загрузок BT/Metalink.
bt-max-peers.name=Максимальное количество пирингов
bt-max-peers.description=Устанавливает максимальное количество подключенных пиров для каждой загрузки BT. 0 означает отсутствие ограничений.
bt-metadata-only.name=Скачать только метаданные
bt-metadata-only.description=Скачивает только торрент-файлы. Файлы, описанные в торрент-файле, не будут загружены. Эта опция действует только для магнитных ссылок.
bt-min-crypto-level.name=Минимальный уровень шифрования
bt-min-crypto-level.description=Устанавливает минимальный уровень шифрования. Если пир предлагает несколько методов шифрования, aria2 выберет минимальный уровень, соответствующий указанному.
bt-prioritize-piece.name=Приоритет загрузки
bt-prioritize-piece.description=Пытается сначала загрузить части в начале или конце каждого файла. Эта опция полезна для предварительного просмотра файлов. Параметры могут включать два ключевых слова: head и tail. Если присутствуют оба ключевых слова, их следует разделить запятой. Каждое ключевое слово может включать параметр SIZE. Например, указав head=SIZE, первые SIZE данных каждого файла будут иметь более высокий приоритет. tail=SIZE означает последние SIZE данных каждого файла. SIZE может включать K или M (1K=1024, 1M=1024K).
bt-remove-unselected-file.name=Удалить невыбранные файлы
bt-remove-unselected-file.description=После завершения задачи BT удаляет невыбранные файлы. Для выбора файлов для загрузки используйте опцию --select-file. Если файлы не выбраны, все файлы будут считаться загружаемыми по умолчанию. Эта опция удаляет файлы напрямую с диска, поэтому используйте ее с осторожностью.
bt-require-crypto.name=Обязательное шифрование
bt-require-crypto.description=Если установлено значение "да", aria не примет ранние рукопожатия BitTorrent (\19протокол BitTorrent) и установит только замаскированное соединение. Таким образом, aria2 будет использовать только замаскированные рукопожатия.
bt-request-peer-speed-limit.name=Желаемая скорость загрузки от пиров
bt-request-peer-speed-limit.description=Если общая скорость загрузки BT ниже значения, установленного этой опцией, aria2 временно увеличит количество соединений для повышения скорости загрузки. В некоторых обстоятельствах установка желаемой скорости загрузки может улучшить скорость загрузки. Можно добавить единицы K или M (1K=1024, 1M=1024K).
bt-save-metadata.name=Сохранить торрент-файл
bt-save-metadata.description=Сохраняет торрент-файл как файл ".torrent". Эта опция действует только для магнитных ссылок. Имя файла представляет собой шестнадцатеричный хэш, закодированный в шестнадцатеричном формате, за которым следует расширение ".torrent". Он сохраняется в той же директории, что и загруженные файлы. Если файл с таким именем уже существует, торрент-файл не будет сохранен.
bt-seed-unverified.name=Не проверять уже загруженные файлы
bt-seed-unverified.description=Не проверяет хэш каждого куска ранее загруженных файлов.
bt-stop-timeout.name=Автоматическое завершение без скорости
bt-stop-timeout.description=Когда скорость загрузки BT задачи остается на уровне 0 в течение времени, установленного этой опцией, загрузка останавливается. Если установлено значение 0, эта функция будет отключена.
bt-tracker.name=Адрес трекера BT
bt-tracker.description=Адреса трекеров BT, разделенные запятыми. Эти адреса не подвержены влиянию опции --bt-exclude-tracker, так как добавляются только после того, как опция --bt-exclude-tracker исключила другие адреса.
bt-tracker-connect-timeout.name=Тайм-аут подключения к трекеру BT
bt-tracker-connect-timeout.description=Устанавливает тайм-аут подключения к трекеру BT в секундах. После установления соединения эта опция больше не действует, используйте опцию --bt-tracker-timeout.
bt-tracker-interval.name=Интервал подключения к трекеру BT
bt-tracker-interval.description=Устанавливает интервал запросов к трекеру BT в секундах. Эта опция полностью переопределит минимальный интервал и интервал, возвращаемые трекером, aria2 будет использовать только значение этой опции. Если установлено значение 0, aria2 решит интервал на основе ответа трекера и прогресса загрузки.
bt-tracker-timeout.name=Тайм-аут трекера BT
bt-tracker-timeout.description=Устанавливает таймаут для взаимодействия с трекерами BitTorrent. Определяет время ожидания ответа от трекера перед повторной попыткой.
dht-file-path.name=Файл DHT (IPv4)
dht-file-path.description=Изменяет путь к файлу таблицы маршрутизации DHT IPv4.
dht-file-path6.name=Файл DHT (IPv6)
dht-file-path6.description=Изменяет путь к файлу таблицы маршрутизации DHT IPv6.
dht-listen-port.name=Порт прослушивания DHT
dht-listen-port.description=Устанавливает UDP порт, используемый DHT (IPv4, IPv6) и UDP сервером. Несколько портов можно разделить запятыми ",", например: 6881,6885. Также можно использовать тире "-" для указания диапазона: 6881-6999, или оба вместе: 6881-6889, 6999.
dht-message-timeout.name=Тайм-аут сообщения DHT
dht-message-timeout.description=Устанавливает таймаут для обмена сообщениями в DHT (распределённая хеш-таблица). Определяет время ожидания ответа от пиров в сети DHT.
enable-dht.name=Включить DHT (IPv4)
enable-dht.description=Включает функцию DHT IPv4. Эта опция также включает поддержку UDP сервера. Если торрент помечен как частный, aria2 не включит DHT, даже если эта опция установлена на "да".
enable-dht6.name=Включить DHT (IPv6)
enable-dht6.description=Включает функцию DHT IPv6. Если торрент помечен как частный, aria2 не включит DHT, даже если эта опция установлена на "да". Используйте опцию --dht-listen-port для установки порта прослушивания.
enable-peer-exchange.name=Включить обмен пирамид
enable-peer-exchange.description=Включает расширение обмена пирамид. Если торрент помечен как частный, aria2 не включит эту функцию, даже если эта опция установлена на "да".
follow-torrent.name=Скачать файлы в торренте
follow-torrent.description=Если установлено "Да" или "Только память", то при завершении загрузки файла с суффиксом .torrent или с типом содержимого application/x-bittorrent, aria2 прочитает и загрузит файлы, указанные в файле torrent. Если установлено "Только память", файл torrent не будет записан на диск, а только сохранен в памяти. Если установлено "Нет", файл .torrent будет загружен на диск, но не будет прочитан, и файлы, указанные в нем, не будут загружены.
listen-port.name=Порт прослушивания
listen-port.description=Устанавливает порт TCP для загрузок BT. Несколько портов могут быть разделены запятыми ",", например: 6881,6885. Также можно использовать дефис "-" для указания диапазона: 6881-6999, или их комбинацию: 6881-6889, 6999.
max-overall-upload-limit.name=Максимальная глобальная скорость отдачи
max-overall-upload-limit.description=Устанавливает максимальную глобальную скорость отдачи в байтах/секунду. 0 означает отсутствие ограничений. Можно увеличить значение, добавив единицы K или M (1K=1024, 1M=1024K).
max-upload-limit.name=Максимальная скорость отдачи
max-upload-limit.description=Устанавливает максимальную скорость отдачи для каждой задачи в байтах/секунду. 0 означает отсутствие ограничений. Можно увеличить значение, добавив единицы K или M (1K=1024, 1M=1024K).
peer-id-prefix.name=Префикс идентификатора узла
peer-id-prefix.description=Задает префикс для идентификатора узла. Идентификатор узла в BT имеет длину 20 байт. Если он превышает 20 байт, будут использованы только первые 20 байт. Если он короче 20 байт, будут добавлены случайные данные, чтобы достичь 20 байт.
peer-agent.name=Агент пира
peer-agent.description=Устанавливает строку User-Agent, которую будет использовать aria2 при соединении с пирами для передачи данных.
seed-ratio.name=Минимальное соотношение раздачи
seed-ratio.description=Задает соотношение раздачи. Раздача прекращается, когда соотношение раздачи достигает значения, установленного в этой опции. Настоятельно рекомендуется установить это значение на уровень выше или равный 1.0. Если не желаете ограничивать соотношение раздачи, можно установить на 0.0. Если также установлена опция --seed-time, раздача завершится, когда одно из условий будет выполнено.
seed-time.name=Минимальное время раздачи
seed-time.description=Задает время раздачи в минутах (в десятичном формате). Если установлено на 0, раздача не произойдет после завершения загрузки задачи BT.
follow-metalink.name=Загрузка файлов из Metalink
follow-metalink.description=Если установлено на "Да" или "Только память", когда файл с суффиксом .meta4 или .metalink или с типом содержимого application/metalink4+xml или application/metalink+xml будет завершен, aria2 прочитает и загрузит файлы, указанные в файле Metalink. Если установлено на "Только память", файл Metalink не будет записан на диск, а только сохранен в памяти. Если установлено на "Нет", файл .metalink будет загружен на диск, но не будет прочитан, и файлы, содержащиеся в нем, не будут загружены.
metalink-base-uri.name=Базовый URI
metalink-base-uri.description=Задает базовый URI для разрешения относительных URI адресов metalink:url и metalink:metaurl в файлах Metalink, хранящихся на локальном диске. Если URI представляет каталог, он должен заканчиваться на /.
metalink-language.name=Язык
metalink-language.description=Устанавливает язык, который будет использоваться для мета-данных в формате Metalink. Язык указывается в виде кода языка, например, "en" для английского.
metalink-location.name=Предпочитаемое расположение сервера
metalink-location.description=Предпочитаемое расположение сервера. Можно использовать список, разделенный запятыми, например: jp,us.
metalink-os.name=Операционная система
metalink-os.description=Операционная система файла для загрузки.
metalink-version.name=Номер версии
metalink-version.description=Номер версии файла для загрузки.
metalink-preferred-protocol.name=Предпочитаемый протокол
metalink-preferred-protocol.description=Задает предпочитаемый для использования протокол. Можно установить http, https, ftp или "нет". Если установлено на "нет", эта опция отключена.
metalink-enable-unique-protocol.name=Использовать только один уникальный протокол
metalink-enable-unique-protocol.description=Если файл Metalink доступен по нескольким протоколам и эта опция установлена на "Да", aria2 будет использовать только один. Используйте параметр --metalink-preferred-protocol для указания предпочитаемого протокола.
enable-rpc.name=Включить сервер JSON-RPC/XML-RPC
enable-rpc.description=Включает интерфейс удалённого вызова процедур (RPC), позволяя управлять aria2 через внешние приложения.
pause-metadata.name=Приостановить после загрузки торрент-файла
pause-metadata.description=Приостанавливает последующие загрузки после загрузки торрент-файла. В aria2 есть 3 типа загрузки торрент-файлов: (1) Загрузка файлов .torrent. (2) Торрент-файлы, загруженные через магнитные ссылки. (3) Загрузка файлов Metalink. Эти торрент-файлы после загрузки продолжат загружаться в зависимости от содержимого файла. Эта опция приостановит эти последующие загрузки. Эта опция эффективна только если опция --enable-rpc включена.
rpc-allow-origin-all.name=Принимать все удаленные запросы
rpc-allow-origin-all.description=Добавляет поле Access-Control-Allow-Origin в заголовок ответа RPC со значением *.
rpc-listen-all.name=Прослушивать на всех сетевых интерфейсах
rpc-listen-all.description=Прослушивает запросы JSON-RPC/XML-RPC на всех сетевых интерфейсах. Если установлено на "Нет", прослушивает только запросы из локальной сети.
rpc-listen-port.name=Порт прослушивания
rpc-listen-port.description=Устанавливает порт, на котором aria2 будет слушать запросы RPC. По умолчанию используется порт 6800.
rpc-max-request-size.name=Максимальный размер запроса
rpc-max-request-size.description=Устанавливает максимальный размер запроса JSON-RPC/XML-RPC. Если aria2 обнаруживает, что запрос превышает установленное количество байт, соединение будет сразу закрыто.
rpc-save-upload-metadata.name=Сохранять загруженные торрент-файлы
rpc-save-upload-metadata.description=Сохраняет загруженные торрент или Metalink файлы в директорию, указанную опцией dir. Имя файла состоит из метаданных хэша SHA-1 и расширения. Для торрент-файлов расширение .torrent. Для Metalink - .meta4. Если эта опция установлена на "Нет", загрузки, добавленные через методы aria2.addTorrent() или aria2.addMetalink(), не могут быть сохранены с опцией --save-session.
rpc-secure.name=Включить SSL/TLS
rpc-secure.description=RPC будет передаваться через шифрование SSL/TLS. Клиент RPC должен использовать протокол https для подключения к серверу. Для WebSocket клиентов используйте протокол wss. Используйте опции --rpc-certificate и --rpc-private-key для установки сертификата и приватного ключа сервера.
allow-overwrite.name=Разрешить перезапись
allow-overwrite.description=Перезагружает файл с начала, если соответствующий контрольный файл не существует. См. опцию --auto-file-renaming.
allow-piece-length-change.name=Разрешить изменение длины части
allow-piece-length-change.description=Если установлено на "Нет", aria2 остановит загрузку, если длина части отличается от длины в контрольном файле. Если установлено на "Да", можно продолжить, но часть прогресса загрузки будет потеряна.
always-resume.name=Всегда возобновлять
always-resume.description=Всегда возобновляет прерванную загрузку. Если установлено на "Да", aria2 всегда пытается возобновить прерванную загрузку, иначе останавливает загрузку. Если установлено на "Нет", для URI, которые не поддерживают возобновление загрузки, или если aria2 сталкивается с N URI, которые не поддерживают возобновление загрузки (N - значение, установленное опцией --max-resume-failure-tries), aria2 загрузит файл с начала. См. параметр --max-resume-failure-tries.
async-dns.name=Асинхронный DNS
async-dns.description=Включает асинхронное разрешение DNS, что позволяет улучшить производительность при разрешении имен хостов.
auto-file-renaming.name=Автоматическое переименование файлов
auto-file-renaming.description=Переименовывает файлы
auto-save-interval.name=Интервал автоматического сохранения
auto-save-interval.description=Автоматически сохраняет контрольный файл (*.aria2) каждые указанные секунды. Если установлено на 0, контрольный файл не будет автоматически сохранен во время загрузки. Независимо от установленного значения, aria2 сохранит контрольный файл после завершения задачи. Значение может быть установлено от 0 до 600.
conditional-get.name=Условная загрузка
conditional-get.description=Загружает файл только если он старше локального файла. Эта функция работает только для загрузок HTTP(S). Если размер файла уже указан в Metalink, функция не будет действовать. Также эта функция проигнорирует заголовок ответа Content-Disposition. Если существует контрольный файл, эта опция будет проигнорирована. Эта функция использует заголовок запроса If-Modified-Since для получения самого нового файла. При извлечении времени модификации локального файла, эта функция будет использовать имя файла, предоставленное пользователем (см. опцию --out), или имя файла в URI, если опция --out не указана. Для перезаписи существующего файла необходимо использовать параметр --allow-overwrite.
conf-path.name=Путь к файлу конфигурации
conf-path.description=Указывает путь к файлу конфигурации, который будет использоваться при запуске aria2.
console-log-level.name=Уровень логов консоли
console-log-level.description=Устанавливает уровень детализации для вывода логов в консоли. Доступные уровни: debug, info, notice, warn, и error.
content-disposition-default-utf8.name=Использовать UTF-8 для обработки Content-Disposition
content-disposition-default-utf8.description=Использует набор символов UTF-8 вместо ISO-8859-1 для обработки строки в заголовке "Content-Disposition", например, параметра имени файла, но не имени файла расширенной версии.
daemon.name=Включить фоновый процесс
daemon.description=Запускает aria2 в фоновом режиме как демон, освобождая терминал для других задач.
deferred-input.name=Отложенная загрузка
deferred-input.description=Если установлено "Да", aria2 не будет читать все URI из файла, указанного в опции --input-file при запуске, а будет читать их только по мере необходимости. Если файл ввода содержит большое количество URI для загрузки, эта опция может снизить использование памяти. Если установлено "Нет", aria2 будет читать все URI при запуске. Опция --deferred-input будет отключена при использовании --save-session.
disable-ipv6.name=Отключить IPv6
disable-ipv6.description=Отключает использование IPv6 для всех сетевых соединений.
disk-cache.name=Дисковый кеш
disk-cache.description=Включает дисковый кеш. Если установлено 0, дисковый кеш будет отключен. Эта функция кеширует загруженные данные в памяти до максимального значения, указанного в этой опции. Память для кеша создается экземпляром aria2 и используется всеми загрузками. Так как данные записываются большими блоками и упорядочиваются по смещениям в файле, одним из преимуществ дискового кеша является уменьшение дискового ввода-вывода. Если вызывается проверка хэша, и данные находятся в памяти, то нет необходимости считывать их с диска. Размер может включать K или M (1K=1024, 1M=1024K).
download-result.name=Результат загрузки
download-result.description=Эта опция изменяет формат результата загрузки. Если установлено "По умолчанию", будет напечатан GID, статус, средняя скорость загрузки и путь/URI. Если задействовано несколько файлов, будет напечатан только путь/URI первого запрашиваемого файла, остальные будут проигнорированы. Если установлено "Полный", будет напечатан GID, статус, средняя скорость загрузки, прогресс загрузки и путь/URI. В этом случае прогресс загрузки и путь/URI будут напечатаны в одной строке для каждого файла. Если установлено "Скрытый", результат загрузки будет скрыт.
dscp.name=DSCP
dscp.description=Устанавливает значение DSCP для поля TOS в исходящих BT пакетах для QoS. Этот параметр устанавливает только биты DSCP поля TOS, а не все поле целиком. Если значения получены из /usr/include/netinet/ip.h, их нужно разделить на 4 (иначе значения будут неверными, например, класс CS1 станет CS4). Если используются стандартные значения из RFC, документации поставщика сети, Wikipedia или других источников, их можно использовать напрямую.
rlimit-nofile.name=Максимальное количество открытых файловых дескрипторов
rlimit-nofile.description=Устанавливает мягкий лимит на количество открытых файловых дескрипторов. Эта опция эффективна только если: a. Система поддерживает (POSIX). b. Лимит не превышает жесткий лимит. c. Указанный лимит больше текущего мягкого лимита. Это эквивалентно установке ulimit, но не может снизить лимит. Эта опция эффективна только если система поддерживает API rlimit.
enable-color.name=Использовать цвета в выводе терминала
enable-color.description=Включает или отключает использование цветного оформления в выводе консоли.
enable-mmap.name=Включить MMap
enable-mmap.description=Кеширует файлы, отображенные в память. Если пространство файла не выделено заранее, эта опция недействительна. См. --file-allocation.
event-poll.name=Метод опроса событий
event-poll.description=Устанавливает метод опроса событий. Возможные значения включают epoll, kqueue, port, poll и select. Для epoll, kqueue, port и poll доступны только если система их поддерживает. Большинство дистрибутивов Linux поддерживают epoll. Различные системы *BSD, включая Mac OS X, поддерживают kqueue. Open Solaris поддерживает port. Значение по умолчанию зависит от используемой операционной системы.
file-allocation.name=Метод выделения файла
file-allocation.description=Задает метод выделения файлов. "None" не выделяет пространство файла заранее. "Prealloc" выделяет пространство перед началом загрузки. Это займет время в зависимости от размера файла. Если используется современная файловая система, такая как ext4 (с расширенной поддержкой), btrfs, xfs или NTFS (только сборка MinGW), "falloc" - лучший выбор. Может выделить большие файлы (несколько GiB) почти мгновенно. Не используйте falloc на устаревших файловых системах, таких как ext3 и FAT32, так как требуется столько же времени, сколько и prealloc, и aria2 будет заблокирован до завершения выделения. falloc может быть недоступен, если система не поддерживает функцию posix_fallocate(3). "Trunc" использует системный вызов ftruncate(2) или платформенно-специфичную реализацию для усечения файла до определенной длины. В загрузках BitTorrent с несколькими файлами, если файл делит те же секции с соседним файлом, также будут выделены соседние файлы.
force-save.name=Принудительное сохранение
force-save.description=Сохраняет задание, даже если оно завершено или удалено при использовании опции --save-session. В этом случае эта опция также сохранит контрольный файл. Эта опция может сохранить задачи BT, которые считаются завершенными, но все еще находятся на раздаче.
save-not-found.name=Сохранять ненайденные файлы
save-not-found.description=При использовании опции --save-session сохраняет задание загрузки, даже если файлы в задании отсутствуют. Эта опция также сохранит эту ситуацию в контрольном файле.
hash-check-only.name=Только проверка хэша
hash-check-only.description=Если установлено "Да", завершает загрузку на основании завершения загрузки после выполнения проверки хэша с помощью опции --check-integrity.
human-readable.name=Читаемый вывод в консоль
human-readable.description=Печатает размеры и скорость в читаемом формате (например, 1.2Ki, 3.4Mi).
keep-unfinished-download-result.name=Сохранять результаты незавершенных задач
keep-unfinished-download-result.description=Сохраняет все результаты незавершенных задач загрузки, даже если они превышают количество, установленное опцией --max-download-result. Это может помочь сохранить все незавершенные загрузки в файле сессии (см. опцию --save-session). Важно отметить, что нет ограничения на количество незавершенных задач. Если это не требуется, отключите эту опцию.
max-download-result.name=Максимальное количество результатов загрузки
max-download-result.description=Устанавливает максимальное количество результатов загрузок, сохраняемых в памяти. Результаты загрузок включают завершённые, с ошибками или удалённые загрузки. Результаты сохраняются в очереди FIFO, что позволяет хранить не более указанного количества записей. Когда очередь заполнена и создаётся новый результат загрузки, самый старый результат удаляется из начала очереди, а новый добавляется в конец. Установка большого значения для этой опции может привести к высокому потреблению памяти, если выполняются тысячи загрузок. Установите 0, чтобы не сохранять результаты загрузок. Обратите внимание, что незавершённые загрузки всегда будут сохраняться в памяти, независимо от настройки этой опции. См. также опцию --keep-unfinished-download-result.
max-mmap-limit.name=Максимальный лимит MMap
max-mmap-limit.description=Устанавливает максимальный размер файла для включения MMap (см. опцию --enable-mmap). Размер файла определяется суммой всех размеров файлов в задаче загрузки. Например, если загрузка содержит 5 файлов, размер файла будет общей суммой этих файлов. Если размер файла превышает установленный для этой опции, MMap будет отключен.
max-resume-failure-tries.name=Максимальное количество попыток возобновления прерванных загрузок
max-resume-failure-tries.description=Когда опция --always-resume установлена на "Нет", если aria2 обнаруживает, что N URI не поддерживают возобновление прерванных загрузок, загрузка файла начнется с начала. Если N установлено на 0, загрузка файла начнется с начала только если все URI не поддерживают возобновление прерванных загрузок. См. опцию --always-resume.
min-tls-version.name=Минимальная версия TLS
min-tls-version.description=Указывает минимальную версию SSL/TLS.
log-level.name=Уровень логов
log-level.description=Устанавливает уровень детализации для записей в журнале. Доступные уровни: debug, info, notice, warn и error.
optimize-concurrent-downloads.name=Оптимизация одновременных загрузок
optimize-concurrent-downloads.description=Оптимизирует количество одновременных загрузок в зависимости от доступной пропускной способности. aria2 использует ранее измеренную скорость загрузки для получения количества одновременных загрузок с помощью правила N=A + B Log10 (скорость в Мбит/с). Коэффициенты A и B можно настроить, разделив их двоеточием в параметре. Значение по умолчанию (A=5, B=25) может использовать 5 одновременных загрузок в сети 1 Мбит/с и 50 в сети 100 Мбит/с. Количество одновременных загрузок ограничено максимумом, определенным параметром --max-concurrent-downloads.
piece-length.name=Размер блока файла
piece-length.description=Устанавливает размер блока для загрузок HTTP/FTP. aria2 делит файлы по этому лимиту. Все сегменты будут кратны этой длине. Эта опция не действует для загрузок BitTorrent.
show-console-readout.name=Отображение данных консоли
show-console-readout.description=Включает или отключает отображение текущего состояния загрузки в консоли.
summary-interval.name=Интервал вывода сводки загрузки
summary-interval.description=Устанавливает интервал вывода сводки прогресса загрузки (в секундах). Установите на 0, чтобы отключить вывод.
max-overall-download-limit.name=Максимальная глобальная скорость загрузки
max-overall-download-limit.description=Устанавливает максимальную глобальную скорость загрузки (байт/сек). 0 означает отсутствие ограничений. Можно увеличить значение, добавив единицы K или M (1K=1024, 1M=1024K).
max-download-limit.name=Максимальная скорость загрузки
max-download-limit.description=Устанавливает максимальную скорость загрузки для каждой задачи (байт/сек). 0 означает отсутствие ограничений. Можно увеличить значение, добавив единицы K или M (1K=1024, 1M=1024K).
no-conf.name=Отключить файл конфигурации
no-conf.description=Запускает aria2 без загрузки настроек из файла конфигурации.
no-file-allocation-limit.name=Лимит выделения файлов
no-file-allocation-limit.description=Не сравнивает выделенные файлы с этим лимитом размера. Можно увеличить значение, добавив единицы K или M (1K=1024, 1M=1024K).
parameterized-uri.name=Включить поддержку параметризованных URI
parameterized-uri.description=Включает поддержку параметризованных URI. Можно указать набор частей: http://{sv1,sv2,sv3}/foo.iso. Также можно использовать шаговый счетчик для указания числовых последовательностей: http://host/image[000-100:2].img. Шаговый счетчик необязателен. Если все URI не указывают на один и тот же файл, как во втором примере выше, необходимо использовать опцию -Z.
quiet.name=Отключить вывод консоли
quiet.description=Отключает вывод информации в стандартный поток вывода. Полезно для запуска в тихом режиме.
realtime-chunk-checksum.name=Контроль целостности данных в реальном времени
realtime-chunk-checksum.description=Если предоставлена контрольная сумма блока данных, проверяет блоки данных с контрольной суммой во время загрузки.
remove-control-file.name=Удалить контрольный файл
remove-control-file.description=Удаляет контрольный файл перед загрузкой. В сочетании с опцией --allow-overwrite=true, загрузка файла всегда начнется с начала. Эта опция может быть полезна пользователям, которые используют прокси, не поддерживающие возобновление загрузок.
save-session.name=Файл сохранения сессии
save-session.description=Сохраняет загрузки с ошибками и незавершенные загрузки в указанный файл при выходе. Можно перезагрузить их с помощью опции --input-file при перезапуске aria2. Если хотите сжать вывод с помощью GZip, можно добавить расширение .gz к имени файла. Обратите внимание, что метаданные загрузок, добавленных через методы RPC aria2.addTorrent() и aria2.addMetalink(), не будут сохранены, если не сохранены в файл. Загрузки, удаленные с помощью aria2.remove() и aria2.forceRemove(), не будут сохранены.
save-session-interval.name=Интервал сохранения сессии
save-session-interval.description=Сохраняет загрузки с ошибками или незавершенные загрузки в файл, указанный в опции --save-session, через указанные секунды. Если установлено на 0, сессия будет сохранена только при выходе aria2.
socket-recv-buffer-size.name=Размер буфера приема сокета
socket-recv-buffer-size.description=Устанавливает максимальный размер буфера приема сокета в байтах. Если установлено на 0, эта опция отключена. Значение этой опции устанавливается на дескриптор файла сокета при вызове setsockopt() с опцией SO_RCVBUF.
stop.name=Время автоматической остановки
stop.description=Останавливает все активные загрузки и завершает работу aria2.
truncate-console-readout.name=Обрезка вывода консоли
truncate-console-readout.description=Обрезает вывод консоли до одной строки.
