# 腾讯会议文件下载器使用说明

## 📁 文件说明

- `tencent_meeting_downloader.py` - 核心下载器类
- `download_cli.py` - 命令行版本（推荐使用）
- `config.py` - 配置文件
- `使用说明.md` - 本文档

## 🚀 快速开始

### 第一步：配置Cookie

1. 打开浏览器，登录腾讯会议网页版
2. 进入会议记录页面：https://meeting.tencent.com/user-center/meeting-record
3. 按 `F12` 打开开发者工具
4. 切换到 `Network`（网络）标签
5. 刷新页面，找到任意一个请求
6. 在请求头中找到 `Cookie` 字段，复制完整的Cookie值
7. 编辑 `config.py` 文件，将Cookie值粘贴到 `TENCENT_MEETING_COOKIE` 变量中

### 第二步：运行程序

```bash
python download_cli.py
```

### 第三步：选择下载方式

程序会显示可用的会议列表，您可以选择：
1. 下载指定会议（输入序号）
2. 下载指定开始时间的会议
3. 下载所有会议

## 📋 功能特性

### ✅ 支持的文件类型

**视频和音频**
- 视频文件：MP4格式
- 音频文件：M4A格式

**文档类型**
- Word文档：DOCX格式（会议转写内容）
- PDF文档：PDF格式（会议转写内容）
- 文本文档：TXT格式（会议转写内容）

### 📂 目录结构

程序会自动创建目录，格式为：`YYYYMMDD_HHMMSS_会议标题/`

示例：
```
20250530_100116_AI-诺岚的快速会议/
├── TM-20250530100116-546898734-录制1.mp4
├── 音频_录制1.m4a
├── TM-20250530100116-546898734-录制2.mp4
├── 音频_录制2.m4a
├── AI-诺岚的快速会议_word.docx
├── AI-诺岚的快速会议_pdf.pdf
└── AI-诺岚的快速会议_txt.txt
```

## 🔧 高级使用

### 直接使用核心类

```python
from tencent_meeting_downloader import TencentMeetingDownloader

# 创建下载器
downloader = TencentMeetingDownloader(your_cookie)

# 下载指定开始时间的会议
downloader.download_meeting_files(target_start_time="1748570479000")

# 下载所有会议
downloader.download_meeting_files(download_all=True)

# 获取会议列表
meetings = downloader.get_meeting_list()

# 下载单个会议
for meeting in meetings:
    if meeting.get("title") == "目标会议名称":
        downloader.download_single_meeting(meeting)
        break
```

### 自定义配置

编辑 `config.py` 文件可以修改：
- 下载目录
- 文件命名格式
- 支持的文档类型
- 重试次数
- 请求间隔

## ⚠️ 注意事项

1. **Cookie有效期**：Cookie有时效性，如果下载失败请重新获取
2. **网络连接**：确保网络连接稳定，大文件下载可能需要较长时间
3. **存储空间**：视频文件通常较大，请确保有足够的存储空间
4. **权限限制**：只能下载您有权限访问的会议文件
5. **使用频率**：避免频繁请求，程序已内置请求间隔

## 🐛 常见问题

### Q: 为什么获取不到会议列表？
A: 请检查：
- Cookie是否正确复制
- Cookie是否已过期
- 网络连接是否正常
- 是否已登录腾讯会议

### Q: 为什么某些文件下载失败？
A: 可能的原因：
- 文件链接已过期
- 网络连接不稳定
- 没有下载权限
- 文件不存在或已被删除

### Q: 如何找到特定会议的开始时间？
A: 运行程序后，会显示所有可用会议的详细信息，包括开始时间戳。

### Q: 下载的文件在哪里？
A: 默认在脚本所在目录下创建以会议时间和标题命名的文件夹。

## 📝 示例场景

### 场景1：下载今天的会议
1. 运行 `python download_cli.py`
2. 查看会议列表，找到今天的会议
3. 选择选项1，输入对应序号

### 场景2：批量下载所有会议
1. 运行 `python download_cli.py`
2. 选择选项3
3. 确认下载所有会议

### 场景3：下载特定时间的会议
1. 运行 `python download_cli.py`
2. 选择选项2
3. 输入会议开始时间戳（如：1748570479000）

## 🔒 免责声明

本工具仅供学习和个人使用，请遵守腾讯会议的使用条款和相关法律法规。使用本工具下载的内容应仅用于合法目的。

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本是否为3.6+
2. Cookie是否正确配置
3. 网络连接是否正常
4. 是否有足够的存储空间

---

**祝您使用愉快！** 🎉
