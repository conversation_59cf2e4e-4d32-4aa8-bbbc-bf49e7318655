[global]
AriaNg Version=AriaNg 版本
Operation Result=操作結果
Operation Succeeded=操作成功
is connected=已連線
Error=錯誤
OK=確定
Confirm=確認
Cancel=取消
Close=關閉
True=是
False=否
DEBUG=偵錯 (Debug)
INFO=普通 (Info)
WARN=警告 (Warn)
ERROR=錯誤 (Error)
Connecting=連線中
Connected=已連線
Disconnected=未連線
Reconnecting=重連中
Waiting to reconnect=等待重連
Global=全域
New=新增
Start=開始工作
Pause=暫停工作
Retry=重試
Retry Selected Tasks=重試選中的工作
Delete=刪除工作
Select All=全部選中
Select None=全部不選
Select Invert=反向選擇
Select All Failed Tasks=全選失敗的工作
Select All Completed Tasks=全選已完成的工作
Select All Tasks=全部選中工作
Display Order=顯示順序
Copy Download Url=複製下載連結
Copy Magnet Link=複製磁力連結
Help=說明
Search=搜尋
Default=預設
Expand=展開
Collapse=摺疊
Expand All=全部展開
Collapse All=全部摺疊
Open=打開
Save=儲存
Import=匯入
Remove Task=刪除工作
Remove Selected Task=刪除選中的工作
Clear Stopped Tasks=清除已結束工作
Click to view task detail=點選檢視工作詳情
By File Name=依檔名
By File Size=依檔案大小
By Progress=依進度
By Selected Status=依選中狀態
By Remaining=依剩餘時間
By Download Speed=依下載速度
By Upload Speed=依上傳速度
By Peer Address=依節點位址
By Client Name=依客戶端名
Filters=篩選器
Download=下載
Upload=上傳
Downloading=正在下載
Pending Verification=等待驗證
Verifying=正在驗證
Seeding=正在做種
Waiting=正在等待
Paused=已暫停
Completed=已完成
Error Occurred=發生錯誤
Removed=已刪除
Finished / Stopped=已完成 / 已停止
Uncompleted=未完成
Click to pin=點選固定
Settings=系統設定
AriaNg Settings=AriaNg 設定
Aria2 Settings=Aria2 設定
Basic Settings=基本設定
HTTP/FTP/SFTP Settings=HTTP/FTP/SFTP 設定
HTTP Settings=HTTP 設定
FTP/SFTP Settings=FTP/SFTP 設定
BitTorrent Settings=BitTorrent 設定
Metalink Settings=Metalink 設定
RPC Settings=RPC 設定
Advanced Settings=進階設定
AriaNg Debug Console=AriaNg 偵錯控制台
Aria2 Status=Aria2 狀態
File Name=檔名
File Size=大小
Progress=進度
Share Ratio=分享率
Remaining=剩餘時間
Download Speed=下載速度
Upload Speed=上傳速度
Links=連結
Torrent File=種子檔案
Metalink File=Metalink 檔案
File Name:=檔名:
Options=選項
Overview=總覽
Pieces=區塊資訊
Files=檔案清單
Peers=連線狀態
Task Name=工作名稱
Task Size=工作大小
Task Status=工作狀態
Error Description=錯誤描述
Health Percentage=健康度
Info Hash=特徵值
Seeders=種子數
Connections=連線數
Seed Creation Time=種子建立時間
Download Url=下載位址
Download Dir=下載路徑
BT Tracker Servers=BT 伺服器
Copy=複製
(Choose Files)=(選擇檔案)
Videos=影片
Audios=音訊
Pictures=圖片
Documents=文件
Applications=應用程式
Archives=封存檔案
Other=其他
Custom=自訂
Custom Choose File=自訂選擇檔案
Address=位址
Client=客戶端
Status=狀態
Speed=速度
(local)=(本機)
No Data=無資料
No connected peers=沒有連線到其他節點
Failed to change some tasks state.=修改一些工作狀態時失敗.
Confirm Retry=確認重試
Are you sure you want to retry the selected task? AriaNg will create same task after clicking OK.=您是否要重試選中的工作? 點選 "確定" 後, AriaNg 將會建立相同的工作.
Failed to retry this task.=該工作重試失敗.
{successCount} tasks have been retried and {failedCount} tasks are failed.={{successCount}} 個工作重試成功以及 {{failedCount}} 個工作失敗.
Confirm Remove=確認刪除
Are you sure you want to remove the selected task?=您是否要刪除選中的工作?
Failed to remove some task(s).=刪除一些工作時失敗.
Confirm Clear=確認清除
Are you sure you want to clear stopped tasks?=您是否要清除已結束的工作?
Download Links:=下載連結:
Download Now=立即下載
Download Later=手動下載
Open Torrent File=打開種子檔案
Open Metalink File=打開 Metalink 檔案
Support multiple URLs, one URL per line.=支援多個 URL 位址, 每個位址佔一行.
Your browser does not support loading file!=您的瀏覽器不支援載入檔案!
The selected file type is invalid!=選擇的檔案類型無效!
Failed to load file!=載入檔案失敗!
Download Completed=下載完成
BT Download Completed=BT 下載完成
Download Error=下載出錯
AriaNg Url=AriaNg 連結
Command API Url=命令行 API 連結
Export Command API=匯出命令行 API
Export=匯出
Copied=已複製
Pause After Task Created=工作建立後暫停
Language=語言
Theme=主題
Light=淺色
Dark=深色
Follow system settings=跟隨系统設定
Debug Mode=偵錯模式
Page Title=頁面標題
Preview=預覽
Tips: You can use the "noprefix" tag to ignore the prefix, "nosuffix" tag to ignore the suffix, and "scale\=n" tag to set the decimal precision.=小提示: 您可以使用 "noprefix" 標籤忽略前綴, "nosuffix" 標籤忽略副檔名, 以及 "scale\=n" 標籤設定小數的精度.
Example: ${downspeed:noprefix:nosuffix:scale\=1}=示例: ${downspeed:noprefix:nosuffix:scale\=1}
Updating Page Title Interval=頁面標題更新間隔
Enable Browser Notification=啟用瀏覽器通知
Browser Notification Sound=瀏覽器通知聲音
Browser Notification Frequency=瀏覽器通知頻次
Unlimited=無限制
High (Up to 10 Notifications / 1 Minute)=高 (最多 10 條通知 / 每分鐘)
Middle (Up to 1 Notification / 1 Minute)=中 (最多 1 條通知 / 每分鐘)
Low (Up to 1 Notification / 5 Minutes)=低 (最多 1 條通知 / 每5分鐘)
WebSocket Auto Reconnect Interval=WebSocket 自动重連線時間
Aria2 RPC Alias=Aria2 RPC 別名
Aria2 RPC Address=Aria2 RPC 位址
Aria2 RPC Protocol=Aria2 RPC 協定
Aria2 RPC Http Request Method=Aria2 RPC Http 要求方法
POST method only supports aria2 v1.15.2 and above.=POST 方法僅支援 aria2 v1.15.2 及以上.
Aria2 RPC Request Headers=Aria2 RPC 要求標頭
Support multiple request headers, one header per line, each line containing "header name: header value".=支援多個要求標頭, 每個要求標頭佔一行, 每行包含 "標頭名: 標頭值".
Aria2 RPC Secret Token=Aria2 RPC 金鑰
Activate=啟用
Reset Settings=重設設定
Confirm Reset=確認重設
Are you sure you want to reset all settings?=您是否要重設所有設定?
Clear Settings History=清除設定歷史
Are you sure you want to clear all settings history?=您是否要清除所有設定的歷史紀錄?
Delete RPC Setting=刪除 RPC 設定
Add New RPC Setting=加入新 RPC 設定
Are you sure you want to remove rpc setting "{rpcName}"?=您是否要刪除 RPC 設定 "{{rpcName}}"?
Updating Global Stat Interval=全域狀態更新間隔
Updating Task Information Interval=工作資訊更新間隔
Keyboard Shortcuts=鍵盤快速鍵
Supported Keyboard Shortcuts=支援的鍵盤快速鍵
Set Focus On Search Box=將焦點設在搜尋框上
Swipe Gesture=滑動手勢
Change Tasks Order by Drag-and-drop=拖拽工作排序
Action After Creating New Tasks=建立新工作後執行操作
Navigate to Task List Page=轉到工作清單頁面
Navigate to Task Detail Page=轉到工作詳情頁面
Action After Retrying Task=重試工作後執行操作
Navigate to Downloading Tasks Page=轉到正在下載工作頁面
Stay on Current Page=留在目前頁面
Remove Old Tasks After Retrying=重試工作後刪除原工作
Confirm Task Removal=工作刪除前確認
Include Prefix When Copying From Task Details=工作詳情頁複製時包括前綴
Show Pieces Info In Task Detail Page=工作詳情頁顯示區塊資訊
Pieces Amount is Less than or Equal to {value}=區塊數量小於等於 {{value}}
RPC List Display Order=RPC 清單顯示順序
Each Task List Page Uses Independent Display Order=各工作清單頁面使用獨立顯示順序
Recently Used=最近使用
RPC Alias=RPC 別名
Import / Export AriaNg Settings=匯入 / 匯出 AriaNg 設定
Import Settings=匯入設定
Export Settings=匯出設定
AriaNg settings data=AriaNg 設定資料
Confirm Import=確認匯入
Are you sure you want to import all settings?=您是否要匯入所有設定?
Invalid settings data format!=無效的設定資料格式!
Data has been copied to clipboard.=資料已經複製到剪貼簿中.
Supported Placeholder=支援的預留位置
AriaNg Title=AriaNg 標題
Current RPC Alias=目前 RPC 別名
Downloading Count=正在下載數量
Waiting Count=正在等待數量
Stopped Count=已停止數量
You have disabled notification in your browser. You should change your browser's settings before you enable this function.=您已經在瀏覽器中停用通知功能. 如需使用此功能, 請修改您瀏覽器的設定.
Language resource has been updated, please reload the page for the changes to take effect.=語言資源已經更新, 請重新載入頁面使其生效.
Configuration has been modified, please reload the page for the changes to take effect.=配置已經修改, 請重新載入頁面使其生效.
Reload AriaNg=重新載入 AriaNg
Show Secret=顯示金鑰
Hide Secret=隱藏金鑰
Aria2 Version=Aria2 版本
Enabled Features=已啟用的功能
Operations=操作
Reconnect=重新連線
Save Session=儲存會話
Shutdown Aria2=關閉 Aria2
Confirm Shutdown=確認關閉
Are you sure you want to shutdown aria2?=您是否要關閉 aria2?
Session has been saved successfully.=會話已經成功儲存.
Aria2 has been shutdown successfully.=Aria2 已經成功關閉.
Toggle Navigation=切換導航
Shortcut=捷徑
Global Rate Limit=全域速度限制
Loading=正在載入...
More Than One Day=超過1天
Unknown=不詳
Bytes=位元組
Hours=小時
Minutes=分
Seconds=秒
Milliseconds=毫秒
Http=Http
Http (Disabled)=Http (已停用)
Https=Https
WebSocket=WebSocket
WebSocket (Disabled)=WebSocket (已停用)
WebSocket (Security)=WebSocket (安全)
Http and WebSocket would be disabled when accessing AriaNg via Https.=使用 Https 訪問 AriaNg 時，Http 和 WebSocket 將被停用.
POST=POST
GET=GET
Enabled=啟用
Disabled=停用
Always=始終
Never=從不
BitTorrent=BitTorrent
Changes to the settings take effect after refreshing page.=設定將在頁面重新整理後生效.
Logging Time=記錄時間
Log Level=記錄層級
Auto Refresh=自動刷新
Refresh Now=立即刷新
Clear Logs=清除記錄
Are you sure you want to clear debug logs?=您是否要清除偵錯記錄?
Show Detail=顯示詳情
Log Detail=記錄詳情
Aria2 RPC Debug=Aria2 RPC 偵錯
Aria2 RPC Request Method=Aria2 RPC 要求方法
Aria2 RPC Request Parameters=Aria2 RPC 要求參數
Aria2 RPC Response=Aria2 RPC 回應
Execute=執行
RPC method is illegal!=RPC方法錯誤!
AriaNg does not support this RPC method!=AriaNg 不支援該RPC方法!
RPC request parameters are invalid!=RPC 要求參數無效!
Type is illegal!=類型錯誤!
Parameter is invalid!=要求參數無效!
Option value cannot be empty!=參數內容不能為空!
Input number is invalid!=輸入的數字無效!
Input number is below min value!=輸入的數字小於最小值 {{value}} !
Input number is above max value!=輸入的數字大於最大值 {{value}} !
Input value is invalid!=輸入的內容無效!
Protocol is invalid!=協定無效!
RPC host cannot be empty!=RPC 主機不能為空!
RPC secret is not base64 encoded!=RPC 金鑰不是 Base64 編碼後的字串!
URL is not base64 encoded!=指定 URL 不是 Base64 編碼後的字串!
Tap to configure and get started with AriaNg.=您還沒有進行過設定, 點選這裡進行設定.
Cannot initialize WebSocket!=無法初始化 WebSocket!
Cannot connect to aria2!=無法連線到 aria2!
Access Denied!=拒絕訪問!
You cannot use AriaNg because this browser does not meet the minimum requirements for data storage.=您無法使用 AriaNg, 因為此瀏覽器不滿足資料儲存的最低要求.

[error]
unknown=不詳錯誤.
operation.timeout=操作超時.
resource.notfound=無法找到指定資源.
resource.notfound.max-file-not-found=無法找到指定資源. 參見 --max-file-not-found option 參數.
download.aborted.lowest-speed-limit=由於下載速度過慢, 下載已經終止. 參見 --lowest-speed-limit option 參數.
network.problem=網路問題.
resume.notsupported=伺服器不支援斷點續傳.
space.notenough=可用磁碟空間不足.
piece.length.different=分段大小與 .aria2 控制檔案中的不同. 參見 --allow-piece-length-change 參數.
download.sametime=aria2 已經下載了另一個相同檔案.
download.torrent.sametime=aria2 已經下載了另一個相同雜湊的種子檔案.
file.exists=檔案已經存在. 參見 --allow-overwrite 參數.
file.rename.failed=檔案重命名失敗. 參見 --auto-file-renaming 參數.
file.open.failed=檔案打開失敗.
file.create.failed=檔案建立或刪除已有檔案失敗.
io.error=檔案系統出錯.
directory.create.failed=無法建立指定目錄.
name.resolution.failed=域名解析失敗.
metalink.file.parse.failed=解析 Metalink 檔案失敗.
ftp.command.failed=FTP 命令執行失敗.
http.response.header.bad=HTTP 返回頭無效或無法識別.
redirects.toomany=指定位址重新導向過多.
http.authorization.failed=HTTP 認證失敗.
bencoded.file.parse.failed=解析種子檔案失敗.
torrent.file.corrupted=指定 ".torrent" 種子檔案已經損壞或缺少 aria2 需要的資訊.
magnet.uri.bad=指定磁力連結位址無效.
option.bad=設定錯誤.
server.overload=遠端伺服器繁忙, 無法處理目前要求.
rpc.request.parse.failed=處理 RPC 要求失敗.
checksum.failed=檔案校驗失敗.

[languages]
Czech=捷克語
German=德語
English=英語
Spanish=西班牙語
French=法語
Italian=義大利語
Polish=波蘭語
Russian=俄語
Simplified Chinese=簡體中文
Traditional Chinese=繁體中文

[format]
longdate=YYYY年MM月DD日 HH:mm:ss
time.millisecond={{value}} 毫秒
time.milliseconds={{value}} 毫秒
time.second={{value}} 秒
time.seconds={{value}} 秒
time.minute={{value}} 分鐘
time.minutes={{value}} 分鐘
time.hour={{value}} 小時
time.hours={{value}} 小時
requires.aria2-version=需要 aria2 v{{version}} 或更高版本
task.new.download-links=下載連結 ({{count}} 個連結):
task.pieceinfo=已完成: {{completed}}, 共計: {{total}} 塊
task.error-occurred=發生錯誤 ({{errorcode}})
task.verifying-percent=正在驗證 ({{verifiedPercent}}%)
settings.file-count=({{count}} 個檔案)
settings.total-count=(共計: {{count}}個)
debug.latest-logs=最近 {{count}} 條記錄

[rpc.error]
unauthorized=認證失敗!

[option]
true=是
false=否
default=預設
none=無
hide=隱藏
full=完整
http=Http
https=Https
ftp=Ftp
mem=僅記憶體
get=GET
tunnel=TUNNEL
plain=明文
arc4=ARC4
binary=二進位
ascii=ASCII
debug=偵錯 (Debug)
info=普通 (Info)
notice=一般 (Notice)
warn=警告 (Warn)
error=錯誤 (Error)
adaptive=自適應
epoll=epoll
falloc=falloc
feedback=反饋
geom=幾何
inorder=順序
kqueue=kqueue
poll=poll
port=port
prealloc=prealloc
random=隨機
select=select
trunc=trunc
SSLv3=SSLv3
TLSv1=TLSv1
TLSv1.1=TLSv1.1
TLSv1.2=TLSv1.2

[options]
dir.name=下載路徑
dir.description=
log.name=記錄檔案
log.description=記錄檔案的路徑. 如果設定為 "-", 記錄則寫入到 stdout. 如果設定為空字串(""), 記錄將不會記錄到磁碟上.
max-concurrent-downloads.name=最大同時下載數
max-concurrent-downloads.description=
check-integrity.name=檢查完整性
check-integrity.description=通過對檔案的每個分塊或整個檔案進行雜湊驗證來檢查檔案的完整性. 此選項僅對BT、Metalink及設定了 --checksum 選項的 HTTP(S)/FTP 鏈接生效.
continue.name=斷點續傳
continue.description=繼續下載部分完成的檔案. 啟用此選項可以繼續下載從瀏覽器或其他程式依順序下載的檔案. 此選項目前只支援 HTTP(S)/FTP 下載的檔案.
all-proxy.name=代理伺服器
all-proxy.description=設定所有協定的代理伺服器位址. 您還可以針對特定的協定覆蓋此選項, 即使用 --http-proxy, --https-proxy 和 --ftp-proxy 選項. 此設定將會影響所有下載. 代理伺服器位址的格式為 [http://][USER:PASSWORD@]HOST[:PORT].
all-proxy-user.name=代理伺服器使用者名稱
all-proxy-user.description=
all-proxy-passwd.name=代理伺服器密碼
all-proxy-passwd.description=
checksum.name=總和檢查碼
checksum.description=設定總和檢查碼. 選項值格式為 TYPE=DIGEST. TYPE 為雜湊類型. 支援的雜湊類型列在 aria2c -v 的 Hash Algorithms 中. DIGEST 是十六進位摘要. 例如, 設定 sha-1 摘要如同這樣: sha-1=0192ba11326fe2298c8cb4de616f4d4140213838 此選項僅對 HTTP(S)/FTP 下載生效.
connect-timeout.name=連線超時時間
connect-timeout.description=設定建立 HTTP/FTP/代理伺服器 連線的超時時間(秒). 當連線建立後, 此選項不再生效, 請使用 --timeout 選項.
dry-run.name=模擬運行
dry-run.description=如果設定為"是", aria2 將僅檢查遠端檔案是否存在而不會下載檔案內容. 此選項僅對 HTTP/FTP 下載生效. 如果設定為 true, BT 下載將會直接取消.
lowest-speed-limit.name=最小速度限制
lowest-speed-limit.description=當下載速度低於此選項設定的值(B/s) 時將會關閉連線. 0 表示不設定最小速度限制. 您可以增加數值的單位 K 或 M (1K = 1024, 1M = 1024K). 此選項不會影響 BT 下載.
max-connection-per-server.name=單伺服器最大連線數
max-connection-per-server.description=
max-file-not-found.name=檔案未找到重試次數
max-file-not-found.description=如果 aria2 從遠端 HTTP/FTP 伺服器收到 "檔案未找到" 的狀態超過此選項設定的次數後下載將會失敗. 設定為 0 將會停用此選項. 此選項僅影響 HTTP/FTP 伺服器. 重試時同時會記錄重試次數, 所以也需要設定 --max-tries 這個選項.
max-tries.name=最大嘗試次數
max-tries.description=設定最大嘗試次數. 0 表示不限制.
min-split-size.name=最小檔案分段大小
min-split-size.description=aria2 不會分割小於 2*SIZE 位元組的檔案. 例如, 檔案大小為 20MB, 如果 SIZE 為 10M, aria2 會把檔案分成 2 段 [0-10MB) 和 [10MB-20MB), 並且使用 2 個源進行下載 (如果 --split >= 2). 如果 SIZE 為 15M, 由於 2*15M > 20MB, 因此 aria2 不會分割檔案並使用 1 個源進行下載. 您可以增加數值的單位 K 或 M (1K = 1024, 1M = 1024K). 可以設定的值為: 1M-1024M.
netrc-path.name=.netrc 檔案路徑
netrc-path.description=
no-netrc.name=停用 netrc
no-netrc.description=
no-proxy.name=不使用代理伺服器清單
no-proxy.description=設定不使用代理伺服器的主機名, 域名, 包含或不包含子網掩碼的網路位址, 多個使用逗號分隔.
out.name=檔名
out.description=下載檔案的檔名. 其總是相對於 --dir 選項中設定的路徑. 當使用 --force-sequential 參數時此選項無效.
proxy-method.name=代理伺服器要求方法
proxy-method.description=設定用來要求代理伺服器的方法. 方法可設定為 GET 或 TUNNEL. HTTPS 下載將忽略此選項並總是使用 TUNNEL.
remote-time.name=獲取伺服器檔案時間
remote-time.description=從 HTTP/FTP 服務獲取遠端檔案的時間戳, 如果可用將設定到本機檔案
reuse-uri.name=URI 復用
reuse-uri.description=當所有給定的 URI 位址都已使用, 繼續使用已經使用過的 URI 位址.
retry-wait.name=重試等待時間
retry-wait.description=設定重試間隔時間(秒). 當此選項的值大於 0 時, aria2 在 HTTP 伺服器返回 503 響應時將會重試.
server-stat-of.name=伺服器狀態儲存檔案
server-stat-of.description=指定用來儲存伺服器狀態的檔名. 您可以使用 --server-stat-if 參數讀取儲存的資料.
server-stat-timeout.name=伺服器狀態超時
server-stat-timeout.description=指定伺服器狀態的過期時間 (單位為秒).
split.name=單工作連線數
split.description=下載時使用 N 個連線. 如果提供超過 N 個 URI 位址, 則使用前 N 個位址, 剩餘的位址將作為備用. 如果提供的 URI 位址不足 N 個, 這些位址多次使用以保證同時建立 N 個連線. 同一伺服器的連線數會被 --max-connection-per-server 選項限制.
stream-piece-selector.name=分段選擇演算法
stream-piece-selector.description=指定 HTTP/FTP 下載使用的分段選擇演算法. 分段表示的是並行下載時固定長度的分隔段. 如果設定為"預設", aria2 將會依減少建立連線數選擇分段. 由於建立連線操作的成本較高, 因此這是合理的預設行為. 如果設定為"順序", aria2 將選擇索引最小的分段. 索引為 0 時表示為檔案的第一個分段. 這將有助於視頻的邊下邊播. --enable-http-pipelining 選項有助於減少重連線的開銷. 請注意, aria2 依賴於 --min-split-size 選項, 所以有必要對 --min-split-size 選項設定一個合理的值. 如果設定為"隨機", aria2 將隨機選擇一個分段. 就像"順序"一樣, 依賴於 --min-split-size 選項. 如果設定為"幾何", aria2 會先選擇索引最小的分段, 然後會為之前選擇的分段保留指數增長的空間. 這將減少建立連線的次數, 同時檔案開始部分將會先行下載. 這也有助於視頻的邊下邊播.
timeout.name=超時時間
timeout.description=
uri-selector.name=URI 選擇演算法
uri-selector.description=指定 URI 選擇的演算法. 可選的值包括 "依順序", "反饋" 和 "自適應". 如果設定為"依順序", URI 將依清單中出現的順序使用. 如果設定為"反饋", aria2 將根據之前的下載速度選擇 URI 清單中下載速度最快的伺服器. 同時也將有效跳過無效鏡像. 之前統計的下載速度將作為伺服器狀態檔案的一部分, 參見 --server-stat-of 和 --server-stat-if 選項. 如果設定為"自適應", 將從最好的鏡像和保留的連線裡選擇一項. 補充說明, 其返回的鏡像沒有被測試過, 同時如果每個鏡像都已經被測試過時, 返回的鏡像還會被重新測試. 否則, 其將不會選擇其他鏡像. 例如"反饋", 其使用伺服器狀態檔案.
check-certificate.name=檢查證書
check-certificate.description=
http-accept-gzip.name=支援 GZip
http-accept-gzip.description=如果遠端伺服器的響應頭中包含 Content-Encoding: gzip 或 Content-Encoding: deflate , 將發送包含 Accept: deflate, gzip 的要求標頭並解壓縮響應.
http-auth-challenge.name=認證質詢
http-auth-challenge.description=僅當伺服器需要時才發送 HTTP 認證要求標頭. 如果設定為"否", 每次都會發送認證要求標頭. 例外: 如果使用者名稱和密碼包含在 URI 中, 將忽略此選項並且每次都會發送認證要求標頭.
http-no-cache.name=停用快取
http-no-cache.description=發送的要求標頭中將包含 Cache-Control: no-cache 和 Pragma: no-cache header 以避免內容被快取. 如果設定為"否", 上述要求標頭將不會發送, 同時您也可以使用 --header 選項將 Cache-Control 要求標頭加入進去.
http-user.name=HTTP 預設使用者名稱
http-user.description=
http-passwd.name=HTTP 預設密碼
http-passwd.description=
http-proxy.name=HTTP 代理伺服器
http-proxy.description=
http-proxy-user.name=HTTP 代理伺服器使用者名稱
http-proxy-user.description=
http-proxy-passwd.name=HTTP 代理伺服器密碼
http-proxy-passwd.description=
https-proxy.name=HTTPS 代理伺服器
https-proxy.description=
https-proxy-user.name=HTTPS 代理伺服器使用者名稱
https-proxy-user.description=
https-proxy-passwd.name=HTTPS 代理伺服器密碼
https-proxy-passwd.description=
referer.name=要求來源
referer.description=設定 HTTP 要求來源 (Referer). 此選項將影響所有 HTTP/HTTPS 下載. 如果設定為 *, 要求來源將設定為下載連結. 此選項可以配合 --parameterized-uri 選項使用.
enable-http-keep-alive.name=啟用持久連線
enable-http-keep-alive.description=啟用 HTTP/1.1 持久連線.
enable-http-pipelining.name=啟用 HTTP 管線化
enable-http-pipelining.description=啟用 HTTP/1.1 管線化.
header.name=自訂要求標頭
header.description=增加 HTTP 要求標頭內容. 每行放置一項, 每項包含 "標頭名: 標頭值".
save-cookies.name=Cookies 儲存路徑
save-cookies.description=以 Mozilla/Firefox(1.x/2.x)/Netscape 格式將 Cookies 儲存到檔案中. 如果檔案已經存在, 將被覆蓋. 會話過期的 Cookies 也將會儲存, 其過期時間將會設定為 0.
use-head.name=啟用 HEAD 方法
use-head.description=第一次要求 HTTP 伺服器時使用 HEAD 方法.
user-agent.name=自訂 User Agent
user-agent.description=
ftp-user.name=FTP 預設使用者名稱
ftp-user.description=
ftp-passwd.name=FTP 預設密碼
ftp-passwd.description=如果 URI 中包含使用者名稱單不包含密碼, aria2 首先會從 .netrc 檔案中獲取密碼. 如果在 .netrc 檔案中找到密碼, 則使用該密碼. 否則, 使用此選項設定的密碼.
ftp-pasv.name=被動模式
ftp-pasv.description=在 FTP 中使用被動模式. 如果設定為"否", 則使用主動模式. 此選項不適用於 SFTP 傳輸.
ftp-proxy.name=FTP 代理伺服器
ftp-proxy.description=
ftp-proxy-user.name=FTP 代理伺服器使用者名稱
ftp-proxy-user.description=
ftp-proxy-passwd.name=FTP 代理伺服器密碼
ftp-proxy-passwd.description=
ftp-type.name=傳輸類型
ftp-type.description=
ftp-reuse-connection.name=連線復用
ftp-reuse-connection.description=
ssh-host-key-md.name=SSH 公鑰總和檢查碼
ssh-host-key-md.description=設定 SSH 主機公鑰的總和檢查碼. 選項值格式為 TYPE=DIGEST. TYPE 為雜湊類型. 支援的雜湊類型為 sha-1 和 md5. DIGEST 是十六進位摘要. 例如: sha-1=b030503d4de4539dc7885e6f0f5e256704edf4c3. 此選項可以在使用 SFTP 時用來驗證伺服器的公鑰. 如果此選項不設定, 即保留預設, 不會進行任何驗證。
bt-detach-seed-only.name=分離僅做種工作
bt-detach-seed-only.description=統計目前活動下載工作(參見 -j 選項) 時排除僅做種的工作. 這意味著, 如果參數設定為 -j3, 此選項打開並且目前有 3 個正在活動的工作, 並且其中有 1 個進入做種模式, 那麼其會從正在下載的數量中排除(即數量會變為 2), 在隊列中等待的下一個工作將會開始執行. 但要知道, 在 RPC 方法中, 做種的工作仍然被認為是活動的下載工作.
bt-enable-hook-after-hash-check.name=啟用雜湊檢查完成事件
bt-enable-hook-after-hash-check.description=允許 BT 下載雜湊檢查(參見 -V 選項) 完成後調用命令. 預設情況下, 當雜湊檢查成功後, 通過 --on-bt-download-complete 設定的命令將會被執行. 如果要停用此行為, 請設定為"否".
bt-enable-lpd.name=啟用本機節點發現 (LPD)
bt-enable-lpd.description=
bt-exclude-tracker.name=BT 排除伺服器位址
bt-exclude-tracker.description=逗號分隔的 BT 排除伺服器位址. 您可以使用 * 匹配所有位址, 因此將排除所有伺服器位址. 當在 shell 命令行使用 * 時, 需要使用跳脫字元或引號.
bt-external-ip.name=外部 IP 位址
bt-external-ip.description=指定用在 BitTorrent 下載和 DHT 中的外部 IP 位址. 它可能被發送到 BitTorrent 伺服器. 對於 DHT, 此選項將會報告本機節點正在下載特定的種子. 這對於在私有網路中使用 DHT 非常關鍵. 雖然這個方法叫外部, 但其可以接受各種類型的 IP 位址.
bt-force-encryption.name=強制加密
bt-force-encryption.description=BT 消息中的內容需要使用 arc4 加密. 此選項是設定 --bt-require-crypto --bt-min-crypto-level=arc4 這兩個選項的快捷方式. 此選項不會修改上述兩個選項的內容. 如果設定為"是", 將拒絕以前的 BT 握手, 並僅使用模糊握手及加密消息.
bt-hash-check-seed.name=做種前檢查檔案雜湊
bt-hash-check-seed.description=如果設定為"是", 當使用 --check-integrity 選項完成雜湊檢查及檔案完成後才繼續做種. 如果您希望僅當檔案損壞或未完成時檢查檔案, 請設定為"否". 此選項僅對 BT 下載有效
bt-load-saved-metadata.name=載入已儲存的中繼資料檔案
bt-load-saved-metadata.description=當使用磁力連結下載時, 在從 DHT 獲取種子中繼資料之前, 首先嘗試載入使用 --bt-save-metadata 選項儲存的檔案. 如果檔案載入成功, 則不會從 DHT 下載中繼資料.
bt-max-open-files.name=最多打開檔案數
bt-max-open-files.description=設定 BT/Metalink 下載全域打開的最大檔案數.
bt-max-peers.name=最大連線節點數
bt-max-peers.description=設定每個 BT 下載的最大連線節點數. 0 表示不限制.
bt-metadata-only.name=僅下載種子檔案
bt-metadata-only.description=僅下載種子檔案. 種子檔案中描述的檔案將不會下載. 此選項僅對磁力連結生效.
bt-min-crypto-level.name=最低加密層級
bt-min-crypto-level.description=設定加密方法的最小層級. 如果節點提供多種加密方法, aria2 將選擇滿足給定層級的最低層級.
bt-prioritize-piece.name=優先下載
bt-prioritize-piece.description=嘗試先下載每個檔案開頭或結尾的分段. 此選項有助於預覽檔案. 參數可以包括兩個關鍵詞: head 和 tail. 如果包含兩個關鍵詞, 需要使用逗號分隔. 每個關鍵詞可以包含一個參數, SIZE. 例如, 如果指定 head=SIZE, 每個檔案的最前 SIZE 資料將會獲得更高的優先順序. tail=SIZE 表示每個檔案的最後 SIZE 資料. SIZE 可以包含 K 或 M (1K = 1024, 1M = 1024K).
bt-remove-unselected-file.name=刪除未選擇的檔案
bt-remove-unselected-file.description=當 BT 工作完成後刪除未選擇的檔案. 要選擇需要下載的檔案, 請使用 --select-file 選項. 如果沒有選擇, 則所有檔案都預設為需要下載. 此選項會從磁碟上直接刪除檔案, 請謹慎使用此選項.
bt-require-crypto.name=需要加密
bt-require-crypto.description=如果設定為"是", aria 將不會接受以前的 BitTorrent 握手協定(\19BitTorrent 協定)並建立連線. 因此 aria2 總是模糊握手.
bt-request-peer-speed-limit.name=期望下載速度
bt-request-peer-speed-limit.description=如果一個 BT 下載的整體下載速度低於此選項設定的值, aria2 會臨時提高連線數以提高下載速度. 在某些情況下, 設定期望下載速度可以提高您的下載速度. 您可以增加數值的單位 K 或 M (1K = 1024, 1M = 1024K).
bt-save-metadata.name=儲存種子檔案
bt-save-metadata.description=儲存種子檔案為 ".torrent" 檔案. 此選項僅對磁力連結生效. 檔名為十六進位編碼後的雜湊值及 ".torrent"副檔名. 儲存的目錄與下載檔案的目錄相同. 如果相同的檔案已存在, 種子檔案將不會儲存.
bt-seed-unverified.name=不檢查已經下載的檔案
bt-seed-unverified.description=不檢查之前下載檔案中每個分段的雜湊值.
bt-stop-timeout.name=無速度時自動停止時間
bt-stop-timeout.description=當 BT 工作下載速度持續為 0, 達到此選項設定的時間後停止下載. 如果設定為 0, 此功能將停用.
bt-tracker.name=BT 伺服器位址
bt-tracker.description=逗號分隔的 BT 伺服器位址. 這些位址不受 --bt-exclude-tracker 選項的影響, 因為這些位址在 --bt-exclude-tracker 選項排除掉其他位址之後才會加入.
bt-tracker-connect-timeout.name=BT 伺服器連線超時時間
bt-tracker-connect-timeout.description=設定 BT 伺服器的連線超時時間 (秒). 當連線建立後, 此選項不再生效, 請使用 --bt-tracker-timeout 選項.
bt-tracker-interval.name=BT 伺服器連線間隔時間
bt-tracker-interval.description=設定要求 BT 伺服器的間隔時間 (秒). 此選項將完全覆蓋伺服器返回的最小間隔時間和間隔時間, aria2 僅使用此選項的值.如果設定為 0, aria2 將根據伺服器的響應情況和下載處理程序決定時間間隔.
bt-tracker-timeout.name=BT 伺服器超時時間
bt-tracker-timeout.description=
dht-file-path.name=DHT (IPv4) 檔案
dht-file-path.description=修改 IPv4 DHT 路由表檔案路徑.
dht-file-path6.name=DHT (IPv6) 檔案
dht-file-path6.description=修改 IPv6 DHT 路由表檔案路徑.
dht-listen-port.name=DHT 監聽埠
dht-listen-port.description=設定 DHT (IPv4, IPv6) 和 UDP 伺服器使用的 UDP 埠. 多個埠可以使用逗號 "," 分隔, 例如: 6881,6885. 您還可以使用短橫線 "-" 表示範圍: 6881-6999, 或可以一起使用: 6881-6889, 6999.
dht-message-timeout.name=DHT 消息超時時間
dht-message-timeout.description=
enable-dht.name=啟用 DHT (IPv4)
enable-dht.description=啟用 IPv4 DHT 功能. 此選項同時會啟用 UDP 伺服器支援. 如果種子設定為私有, 即使此選項設定為"是", aria2 也不會啟用 DHT.
enable-dht6.name=啟用 DHT (IPv6)
enable-dht6.description=啟用 IPv6 DHT 功能. 如果種子設定為私有, 即使此選項設定為"是", aria2 也不會啟用 DHT. 使用 --dht-listen-port 選項設定監聽的埠.
enable-peer-exchange.name=啟用節點交換
enable-peer-exchange.description=啟用節點交換擴充. 如果種子設定為私有, 即使此選項設定為"是", aria2 也不會啟用此功能.
follow-torrent.name=下載種子中的檔案
follow-torrent.description=如果設定為"是"或"僅記憶體", 當副檔名為 .torrent 或內容類型為 application/x-bittorrent 的檔案下載完成時, aria2 將依種子檔案讀取並下載該檔案中提到的檔案. 如果設定為"僅記憶體", 該種子檔案將不會寫入到磁碟中, 而僅會儲存在記憶體中. 如果設定為"否", 則 .torrent 檔案會下載到磁碟中, 但不會依種子檔案讀取並且其中的檔案不會進行下載.
listen-port.name=監聽埠
listen-port.description=設定 BT 下載的 TCP 埠. 多個埠可以使用逗號 "," 分隔, 例如: 6881,6885. 您還可以使用短橫線 "-" 表示範圍: 6881-6999, 或可以一起使用: 6881-6889, 6999.
max-overall-upload-limit.name=全域最大上傳速度
max-overall-upload-limit.description=設定全域最大上傳速度 (位元組/秒). 0 表示不限制. 您可以增加數值的單位 K 或 M (1K = 1024, 1M = 1024K).
max-upload-limit.name=最大上傳速度
max-upload-limit.description=設定每個工作的最大上傳速度 (位元組/秒). 0 表示不限制. 您可以增加數值的單位 K 或 M (1K = 1024, 1M = 1024K).
peer-id-prefix.name=節點 ID 前綴
peer-id-prefix.description=指定節點 ID 的前綴. BT 中節點 ID 長度為 20 位元組. 如果超過 20 位元組, 將僅使用前 20 位元組. 如果少於 20 位元組, 將在其後不足隨機的資料保證為 20 位元組.
peer-agent.name=Peer Agent
peer-agent.description=指定 BT 擴充握手期間用於節點客戶端版本的字串.
seed-ratio.name=最小分享率
seed-ratio.description=指定分享率. 當分享率達到此選項設定的值時會完成做種. 強烈建議您將此選項設定為大於等於 1.0. 如果您想不限制分享比率, 可以設定為 0.0. 如果同時設定了 --seed-time 選項, 當任意一個條件滿足時將停止做種.
seed-time.name=最小做種時間
seed-time.description=以 (小數形式的) 分鐘指定做種時間. 此選項設置爲 0 時, 將在 BT 任務下載完成後不進行做種.
follow-metalink.name=下載 Metalink 中的檔案
follow-metalink.description=如果設定為"是"或"僅記憶體", 當副檔名為 .meta4 或 .metalink 或內容類型為 application/metalink4+xml 或 application/metalink+xml 的檔案下載完成時, aria2 將依 Metalink 檔案讀取並下載該檔案中提到的檔案. 如果設定為"僅記憶體", 該 Metalink 檔案將不會寫入到磁碟中, 而僅會儲存在記憶體中. 如果設定為"否", 則 .metalink 檔案會下載到磁碟中, 但不會依 Metalink 檔案讀取並且其中的檔案不會進行下載.
metalink-base-uri.name=基礎 URI
metalink-base-uri.description=指定基礎 URI 以便解析本機磁碟中儲存的 Metalink 檔案裡 metalink:url 和 metalink:metaurl 中的相對 URI 位址. 如果 URI 表示的為目錄, 最後需要以 / 結尾.
metalink-language.name=語言
metalink-language.description=
metalink-location.name=首選伺服器位置
metalink-location.description=首選伺服器所在的位置. 可以使用逗號分隔的清單, 例如: jp,us.
metalink-os.name=操作系統
metalink-os.description=下載檔案的操作系統.
metalink-version.name=版本號
metalink-version.description=下載檔案的版本號.
metalink-preferred-protocol.name=首選使用協定
metalink-preferred-protocol.description=指定首選使用的協定. 可以設定為 http, https, ftp 或"無". 設定為"無"時停用此選項.
metalink-enable-unique-protocol.name=僅使用唯一協定
metalink-enable-unique-protocol.description=如果一個 Metalink 檔案可用多種協定, 並且此選項設定為"是", aria2 將只會使用其中一種. 使用 --metalink-preferred-protocol 參數指定首選的協定.
enable-rpc.name=啟用 JSON-RPC/XML-RPC 伺服器
enable-rpc.description=
pause-metadata.name=種子檔案下載完後暫停
pause-metadata.description=當種子檔案下載完成後暫停後續的下載. 在 aria2 中有 3 種種子檔案的下載類型: (1) 下載 .torrent 檔案. (2) 通過磁力連結下載的種子檔案. (3) 下載 Metalink 檔案. 這些種子檔案下載完後會根據檔案內容繼續進行下載. 此選項會暫停這些後續的下載. 此選項僅當 --enable-rpc 選項啟用時生效.
rpc-allow-origin-all.name=接受所有遠端要求
rpc-allow-origin-all.description=在 RPC 響應頭增加 Access-Control-Allow-Origin 欄位, 值為 * .
rpc-listen-all.name=在所有網卡上監聽
rpc-listen-all.description=在所有網路適配器上監聽 JSON-RPC/XML-RPC 的要求, 如果設定為"否", 僅監聽本機網路的要求.
rpc-listen-port.name=監聽埠
rpc-listen-port.description=
rpc-max-request-size.name=最大要求大小
rpc-max-request-size.description=設定 JSON-RPC/XML-RPC 最大的要求大小. 如果 aria2 檢測到要求超過設定的位元組數, 會直接取消連線.
rpc-save-upload-metadata.name=儲存上傳的種子檔案
rpc-save-upload-metadata.description=在 dir 選項設定的目錄中儲存上傳的種子檔案或 Metalink 檔案. 檔名包括 SHA-1 雜湊後的中繼資料和副檔名兩部分. 對於種子檔案, 副檔名為 '.torrent'. 對於 Metalink 為 '.meta4'. 如果此選項設定為"否", 通過 aria2.addTorrent() 或 aria2.addMetalink() 方法加入的下載將無法通過 --save-session 選項儲存.
rpc-secure.name=啟用 SSL/TLS
rpc-secure.description=RPC 將通過 SSL/TLS 加密傳輸. RPC 客戶端需要使用 https 協定連線伺服器. 對於 WebSocket 客戶端, 使用 wss 協定. 使用 --rpc-certificate 和 --rpc-private-key 選項設定伺服器的證書和私鑰.
allow-overwrite.name=允許覆蓋
allow-overwrite.description=如果相應的控制檔案不存在時從頭重新下載檔案. 參見 --auto-file-renaming 選項.
allow-piece-length-change.name=允許分段大小變化
allow-piece-length-change.description=如果設定為"否", 當分段長度與控制檔案中的不同時, aria2 將會中止下載. 如果設定為"是", 您可以繼續, 但部分下載進度將會丟失.
always-resume.name=始終斷點續傳
always-resume.description=始終斷點續傳. 如果設定為"是", aria2 始終嘗試斷點續傳, 如果無法恢復, 則中止下載. 如果設定為"否", 對於不支援斷點續傳的 URI 或 aria2 遇到 N 個不支援斷點續傳的 URI (N 為 --max-resume-failure-tries 選項設定的值), aria2 會從頭下載檔案. 參見 --max-resume-failure-tries 參數.
async-dns.name=非同步 DNS
async-dns.description=
auto-file-renaming.name=檔案自動重命名
auto-file-renaming.description=重新命名已經存在的檔案. 此選項僅對 HTTP(S)/FTP 下載有效. 新的檔名後會在檔名後、副檔名 (如果有) 前追加句點和數字(1..9999).
auto-save-interval.name=自動儲存間隔
auto-save-interval.description=每隔設定的秒數自動儲存控制檔案(*.aria2). 如果設定為 0, 下載期間控制檔案不會自動儲存. 不論設定的值為多少, aria2 會在工作結束時儲存控制檔案. 可以設定的值為 0 到 600.
conditional-get.name=條件下載
conditional-get.description=僅當本機檔案比遠端檔案舊時才進行下載. 此功能僅適用於 HTTP(S) 下載. 如果在 Metalink 中檔案大小已經被指定則功能無法生效. 同時此功能還將忽略 Content-Disposition 響應頭. 如果存在控制檔案, 此選項將被忽略. 此功能通過 If-Modified-Since 要求標頭獲取較新的檔案. 當獲取到本機檔案的修改時間時, 此功能將使用使用者提供的檔名 (參見 --out 選項), 如果沒有指定 --out 選項則使用 URI 中的檔名. 為了覆蓋已經存在的檔案, 需要使用 --allow-overwrite 參數.
conf-path.name=設定檔案路徑
conf-path.description=
console-log-level.name=控制台記錄層級
console-log-level.description=
content-disposition-default-utf8.name=使用 UTF-8 處理 Content-Disposition
content-disposition-default-utf8.description=處理 "Content-Disposition" 頭中的字串時使用 UTF-8 字集來代替 ISO-8859-1, 例如, 檔名參數, 但不是擴充版本的檔名.
daemon.name=啟用背景處理程序
daemon.description=
deferred-input.name=延遲載入
deferred-input.description=如果設定為"是", aria2 在啟動時不會讀取 --input-file 選項設定的檔案中的所有 URI 位址, 而是會在之後需要時依需讀取. 如果輸入檔案中包含大量要下載的 URI, 此選項可以減少記憶體的使用. 如果設定為"否", aria2 會在啟動時讀取所有的 URI. 當 -save-session 使用時將會停用 --deferred-input 選項.
disable-ipv6.name=停用 IPv6
disable-ipv6.description=
disk-cache.name=磁碟快取
disk-cache.description=啟用磁碟快取. 如果設定為 0, 將停用磁碟快取. 此功能將下載的資料快取在記憶體中, 最多佔用此選項設定的位元組數. 快取儲存由 aria2 實例建立並對所有下載共享. 由於資料以較大的單位寫入並依檔案的偏移重新排序, 所以磁碟快取的一個優點是減少磁碟的 I/O. 如果調用雜湊檢查時並且資料快取在記憶體中時, 將不需要從磁碟中讀取. 大小可以包含 K 或 M (1K = 1024, 1M = 1024K).
download-result.name=下載結果
download-result.description=此選項將修改下載結果的格式. 如果設定為"預設", 將列印 GID, 狀態, 平均下載速度和路徑/URI. 如果涉及多個檔案, 僅列印第一個要求檔案的路徑/URI, 其餘的將被忽略. 如果設定為"完整", 將列印 GID, 狀態, 平均下載速度, 下載進度和路徑/URI. 其中, 下載進度和路徑/URI 將會每個檔案列印一行. 如果設定為"隱藏", 下載結果將會隱藏.
dscp.name=DSCP
dscp.description=為 QoS 設定 BT 上行 IP 包的 DSCP 值. 此參數僅設定 IP 包中 TOS 欄位的 DSCP 位, 而不是整個欄位. 如果您從 /usr/include/netinet/ip.h 得到的值, 需要除以 4 (否則值將不正確, 例如您的 CS1 類將會轉為 CS4). 如果您從 RFC, 網路供應商的文件, 維基百科或其他來源採取常用的值, 可以直接使用.
rlimit-nofile.name=最多打開的檔案描述項
rlimit-nofile.description=設定打開的檔案描述項的軟限制 (soft limit). 此選項僅當滿足如下條件時開放: a. 系統支援它 (posix). b. 限制沒有超過硬限制 (hard limit). c. 指定的限制比目前的軟限制高. 這相當於設定 ulimit, 除了其不能降低限制. 此選項僅當系統支援 rlimit API 時有效.
enable-color.name=終端輸出使用顏色
enable-color.description=
enable-mmap.name=啟用 MMap
enable-mmap.description=記憶體中存放映射檔案. 當檔案空間沒有預先分配至, 此選項無效. 參見 --file-allocation.
event-poll.name=事件輪詢方法
event-poll.description=設定事件輪詢的方法. 可選的值包括 epoll, kqueue, port, poll 和 select. 對於 epoll, kqueue, port 和 poll, 只有系統支援時才可用. 最新的 Linux 支援 epoll. 各種 *BSD 系統包括 Mac OS X 支援 kqueue. Open Solaris 支援 port. 預設值根據您使用的操作系統不同而不同.
file-allocation.name=檔案分配方法
file-allocation.description=指定檔案分配方法. "無" 不會預先分配檔案空間. "prealloc"會在下載開始前預先分配空間. 這將會根據檔案的大小需要一定的時間. 如果您使用的是較新的檔案系統, 例如 ext4 (帶擴充支援), btrfs, xfs 或 NTFS (僅 MinGW 組建), "falloc" 是最好的選擇. 其幾乎可以瞬間分配大(數 GiB)檔案. 不要在舊的檔案系統, 例如 ext3 和 FAT32 上使用 falloc, 因為與 prealloc 花費的時間相同, 並且其會阻塞 aria2 直到分配完成. 當您的系統不支援 posix_fallocate(3) 函數時, falloc 可能無法使用. "trunc" 使用 ftruncate(2) 系統調用或平台特定的實現將檔案截取到特定的長度. 在多檔案的 BitTorrent 下載中, 若某檔案與其相鄰的檔案共享相同的分段時, 則相鄰的檔案也會被分配.
force-save.name=強制儲存
force-save.description=即使工作完成或刪除時使用 --save-session 選項時也儲存該工作. 此選項在這種情況下還會儲存控制檔案. 此選項可以儲存被認為已經完成但正在做種的 BT 工作.
save-not-found.name=儲存未找到的檔案
save-not-found.description=當使用 --save-session 選項時, 即使當工作中的檔案不存在時也儲存該下載工作. 此選項同時會將這種情況儲存到控制檔案中.
hash-check-only.name=僅雜湊檢查
hash-check-only.description=如果設定為"是", 雜湊檢查完使用 --check-integrity 選項, 根據是否下載完成決定是否終止下載.
human-readable.name=控制台可讀輸出
human-readable.description=在控制台輸出可讀格式的大小和速度 (例如, 1.2Ki, 3.4Mi).
keep-unfinished-download-result.name=保留未完成的工作
keep-unfinished-download-result.description=保留所有未完成的下載結果, 即使超過了 --max-download-result 選項設定的數量. 這將有助於在會話檔案中儲存所有的未完成的下載 (參考 --save-session 選項). 需要注意的是, 未完成工作的數量沒有上限. 如果不希望這樣, 請關閉此選項.
max-download-result.name=最多下載結果
max-download-result.description=設定記憶體中儲存最多的下載結果數量. 下載結果包括已完成/錯誤/已刪除的下載. 下載結果儲存在一個先進先出的隊列中, 因此其可以儲存最多指定的下載結果的數量. 當隊列已滿且有新的下載結果建立時, 最老的下載結果將從隊列的最前部移除, 新的將放在最後. 此選項設定較大的值後如果經過幾千次的下載將導致較高的記憶體消耗. 設定為 0 表示不儲存下載結果. 注意, 未完成的下載將始終儲存在記憶體中, 不考慮該選項的設定. 參考 --keep-unfinished-download-result 選項.
max-mmap-limit.name=MMap 最大限制
max-mmap-limit.description=設定啟用 MMap (參見 --enable-mmap 選項) 最大的檔案大小. 檔案大小由一個下載工作中所有檔案大小的和決定. 例如, 如果一個下載包含 5 個檔案, 那麼檔案大小就是這些檔案的總大小. 如果檔案大小超過此選項設定的大小時, MMap 將會停用.
max-resume-failure-tries.name=最大斷點續傳嘗試次數
max-resume-failure-tries.description=當 --always-resume 選項設定為"否"時, 如果 aria2 檢測到有 N 個 URI 不支援斷點續傳時, 將從頭開始下載檔案. 如果 N 設定為 0, 當所有 URI 都不支援斷點續傳時才會從頭下載檔案. 參見 --always-resume 選項.
min-tls-version.name=最低 TLS 版本
min-tls-version.description=指定啟用的最低 SSL/TLS 版本.
log-level.name=記錄層級
log-level.description=
optimize-concurrent-downloads.name=最佳化同時下載
optimize-concurrent-downloads.description=根據可用頻寬最佳化同時下載的數量. aria2 使用之前統計的下載速度通過規則 N = A + B Log10 (速度單位為 Mbps) 得到同時下載的數量. 其中係數 A 和 B 可以在參數中以冒號分隔自訂. 預設值 (A=5, B=25) 可以在 1Mbps 網路上使用通常 5 個同時下載, 在 100Mbps 網路上為 50 個. 同時下載的數量保持在 --max-concurrent-downloads 參數定義的最大之下.
piece-length.name=檔案分段大小
piece-length.description=設定 HTTP/FTP 下載的分配大小. aria2 根據這個邊界分割檔案. 所有的分割都是這個長度的倍數. 此選項不適用於 BitTorrent 下載. 如果 Metalink 檔案中包含分段雜湊的結果此選項也不適用.
show-console-readout.name=顯示控制台輸出
show-console-readout.description=
summary-interval.name=下載摘要輸出間隔
summary-interval.description=設定下載進度摘要的輸出間隔(秒). 設定為 0 禁止輸出.
max-overall-download-limit.name=全域最大下載速度
max-overall-download-limit.description=設定全域最大下載速度 (位元組/秒). 0 表示不限制. 您可以增加數值的單位 K 或 M (1K = 1024, 1M = 1024K).
max-download-limit.name=最大下載速度
max-download-limit.description=設定每個工作的最大下載速度 (位元組/秒). 0 表示不限制. 您可以增加數值的單位 K 或 M (1K = 1024, 1M = 1024K).
no-conf.name=停用設定檔案
no-conf.description=
no-file-allocation-limit.name=檔案分配限制
no-file-allocation-limit.description=不對比此參數設定大小小的分配檔案. 您可以增加數值的單位 K 或 M (1K = 1024, 1M = 1024K).
parameterized-uri.name=啟用參數化 URI 支援
parameterized-uri.description=啟用參數化 URI 支援. 您可以指定部分的集合: http://{sv1,sv2,sv3}/foo.iso. 同時您也可以使用步進計數器指定數字化的序列: http://host/image[000-100:2].img. 步進計數器可以省略. 如果所有 URI 位址不指向同樣的檔案, 例如上述第二個示例, 需要使用 -Z 選項.
quiet.name=停用控制台輸出
quiet.description=
realtime-chunk-checksum.name=即時資料區塊驗證
realtime-chunk-checksum.description=如果提供了資料區塊的總和檢查碼, 將在下載過程中通過總和檢查碼驗證資料區塊.
remove-control-file.name=刪除控制檔案
remove-control-file.description=在下載前刪除控制檔案. 使用 --allow-overwrite=true 選項時, 總是從頭開始下載檔案. 此選項將有助於使用不支援斷點續傳代理伺服器的使用者.
save-session.name=狀態儲存檔案
save-session.description=當離開時儲存錯誤及未完成的工作到指定的檔案中. 您可以在重啟 aria2 時使用 --input-file 選項重新載入. 如果您希望輸出的內容使用 GZip 壓縮, 您可以在檔名後增加 .gz 副檔名. 請注意, 通過 aria2.addTorrent() 和 aria2.addMetalink() RPC 方法加入的下載, 其中繼資料沒有儲存到檔案的將不會儲存. 通過 aria2.remove() 和 aria2.forceRemove() 刪除的下載將不會儲存.
save-session-interval.name=儲存狀態間隔
save-session-interval.description=每隔此選項設定的時間(秒)後會儲存錯誤或未完成的工作到 --save-session 選項指定的檔案中. 如果設定為 0, 僅當 aria2 離開時才會儲存.
socket-recv-buffer-size.name=Socket 接收緩衝區大小
socket-recv-buffer-size.description=設定 Socket 接收緩衝區最大的位元組數. 指定為 0 時將停用此選項. 當使用 SO_RCVBUF 選項調用 setsockopt() 時此選項的值將設定到 Socket 的檔案描述項中.
stop.name=自動關閉時間
stop.description=在此選項設定的時間(秒)後關閉應用程式. 如果設定為 0, 此功能將停用.
truncate-console-readout.name=縮短控制台輸出內容
truncate-console-readout.description=縮短控制台輸出的內容在一行中.
