#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯会议下载器 - aria2加速版本
自动启动aria2并使用加速下载
"""

import os
import sys

def main():
    """
    主函数 - 自动使用aria2加速下载
    """
    print("🚀 腾讯会议下载器 - aria2加速版")
    print("=" * 50)
    
    try:
        # 导入必要的模块
        from tencent_meeting_downloader import TencentMeetingDownloader
        from aria2_downloader import start_aria2_if_needed, TencentMeetingAria2Downloader
        
        # 初始化基础下载器
        print("🔧 初始化下载器...")
        base_downloader = TencentMeetingDownloader()
        print("✅ 基础下载器初始化成功")
        
        # 启动aria2
        print("\n🔄 检查并启动aria2...")
        if start_aria2_if_needed():
            # 使用aria2下载器（直接从配置文件读取）
            downloader = TencentMeetingAria2Downloader()
            print("✅ aria2加速下载模式已启用")
        else:
            print("⚠️  aria2启动失败，使用普通下载模式")
            downloader = base_downloader
        
        print("\n" + "=" * 50)
        print("📋 下载选项:")
        print("1. 下载指定会议 (需要提供会议开始时间)")
        print("2. 下载所有会议")
        print("3. 仅获取会议列表")
        print("4. 退出")
        
        while True:
            choice = input("\n请选择操作 (1-4): ").strip()
            
            if choice == '1':
                # 下载指定会议
                target_time = input("请输入会议开始时间 (毫秒时间戳): ").strip()
                if target_time:
                    print(f"\n🎯 开始下载会议 (开始时间: {target_time})")
                    downloader.download_meeting_files(target_start_time=target_time)
                else:
                    print("❌ 时间戳不能为空")
                break
                
            elif choice == '2':
                # 下载所有会议
                confirm = input("⚠️  确定要下载所有会议吗? (y/N): ").strip().lower()
                if confirm == 'y':
                    print("\n🎯 开始下载所有会议")
                    downloader.download_meeting_files(download_all=True)
                else:
                    print("❌ 操作已取消")
                break
                
            elif choice == '3':
                # 仅获取会议列表
                print("\n📋 获取会议列表...")
                meetings = downloader.get_meeting_list()
                if meetings:
                    print(f"\n📊 找到 {len(meetings)} 个会议:")
                    for i, meeting in enumerate(meetings, 1):
                        title = meeting.get("title", "未知会议")
                        start_time = meeting.get("start_time", "未知时间")
                        record_type = meeting.get("record_type", "未知类型")
                        print(f"  {i}. {title}")
                        print(f"     开始时间: {start_time}")
                        print(f"     记录类型: {record_type}")
                        print()
                else:
                    print("❌ 未获取到会议列表")
                break
                
            elif choice == '4':
                print("👋 再见!")
                break
                
            else:
                print("❌ 无效选择，请输入1-4")
        
    except ValueError as e:
        print(f"❌ 初始化失败: {str(e)}")
        print("💡 请确保config.json文件存在并包含有效的cookie")
        print("💡 可以运行 python update_cookie.py 来更新cookie")
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {str(e)}")
        print("💡 请确保所有必要的文件都存在")
        
    except Exception as e:
        print(f"❌ 程序异常: {str(e)}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
    
    finally:
        input("\n按Enter键退出...")

if __name__ == "__main__":
    main()
