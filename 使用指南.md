# 腾讯会议下载器 - 完整使用指南

## 🎯 快速开始

### 方式1：一键启动（推荐新手）
1. 双击 `启动下载器.bat`
2. 首次使用会自动引导配置Cookie
3. 选择下载选项即可开始

### 方式2：命令行启动（推荐熟练用户）
```bash
# aria2加速下载（推荐）
python download_with_aria2.py

# 普通下载
python tencent_meeting_downloader.py

# 更新Cookie
python update_cookie.py

# 功能测试
python 完整功能测试.py
```

## 🔧 首次配置

### 1. 获取Cookie
1. 打开浏览器，访问 https://meeting.tencent.com
2. 登录您的腾讯会议账号
3. 按F12打开开发者工具
4. 切换到Network(网络)标签页
5. 刷新页面或进行任意操作
6. 在请求列表中选择任意一个请求
7. 在Request Headers中找到Cookie字段
8. 复制Cookie的完整值

### 2. 配置Cookie
```bash
# 运行配置工具
python update_cookie.py

# 或者手动编辑config.json文件
```

## 🚀 功能特性

### aria2加速下载
- ✅ 自动启动本地aria2程序
- ✅ 多线程并发下载，速度提升5-10倍
- ✅ 实时显示下载进度和速度
- ✅ 自动重试和断点续传
- ✅ 支持大文件稳定下载

### 智能文件管理
- ✅ 按会议时间自动创建目录
- ✅ 规范化文件命名
- ✅ 支持多种文件类型（视频、音频、文档）
- ✅ 避免重复下载

### 用户友好界面
- ✅ 交互式菜单选择
- ✅ 详细的状态提示
- ✅ 错误处理和恢复建议
- ✅ 一键启动脚本

## 📁 文件说明

| 文件名 | 功能说明 |
|--------|----------|
| `启动下载器.bat` | 一键启动脚本（推荐） |
| `download_with_aria2.py` | aria2加速下载器 |
| `tencent_meeting_downloader.py` | 主程序文件 |
| `aria2_downloader.py` | aria2集成模块 |
| `update_cookie.py` | Cookie更新工具 |
| `完整功能测试.py` | 功能测试脚本 |
| `config.json` | 配置文件 |
| `config.example.json` | 配置模板 |

## 🔄 Cookie更新

当Cookie过期时（通常表现为401错误），需要更新：

### 自动更新
```bash
python update_cookie.py
```

### 手动更新
1. 编辑 `config.json` 文件
2. 替换 `cookie` 字段的值
3. 保存文件

## 📊 下载选项

### 1. 下载指定会议
- 需要提供会议开始时间（毫秒时间戳）
- 精确下载特定会议的所有文件

### 2. 下载所有会议
- 批量下载账号下的所有会议文件
- 适合备份所有会议记录

### 3. 仅获取会议列表
- 查看可下载的会议信息
- 获取会议时间戳用于精确下载

## ⚠️ 注意事项

### Cookie管理
- Cookie有时效性，通常24-48小时过期
- 过期后需要重新获取和配置
- 建议定期更新以确保正常使用

### 网络要求
- 需要稳定的网络连接
- 大文件下载建议使用aria2模式
- 如遇网络问题，程序会自动重试

### 权限要求
- 只能下载您有权限访问的会议文件
- 确保登录的账号有相应的下载权限

## 🛠️ 故障排除

### 1. Cookie相关问题
```
❌ 401错误 / 获取会议列表失败
```
**解决方法**：
1. 运行 `python update_cookie.py` 更新Cookie
2. 确保Cookie完整且有效
3. 检查账号登录状态

### 2. aria2相关问题
```
❌ aria2启动失败
```
**解决方法**：
1. 检查 `AriaNg-DailyBuild-master/Aria2/aria2.exe` 是否存在
2. 运行 `python test_aria2.py` 测试aria2功能
3. 如果失败，程序会自动回退到普通下载模式

### 3. 网络连接问题
```
❌ 连接超时 / 下载失败
```
**解决方法**：
1. 检查网络连接
2. 尝试使用aria2模式（更稳定）
3. 程序会自动重试失败的下载

### 4. 文件权限问题
```
❌ 无法创建目录 / 保存文件
```
**解决方法**：
1. 确保有足够的磁盘空间
2. 检查目录写入权限
3. 以管理员身份运行程序

## 🧪 功能测试

运行完整功能测试确保一切正常：
```bash
python 完整功能测试.py
```

测试内容包括：
- ✅ 文件结构完整性
- ✅ 模块导入测试
- ✅ 配置文件加载
- ✅ aria2功能测试
- ✅ 下载器初始化

## 📞 技术支持

如果遇到问题：
1. 首先运行功能测试脚本
2. 检查错误信息和解决建议
3. 确保所有依赖文件完整
4. 验证Cookie和网络连接

---

**祝您使用愉快！** 🎉
