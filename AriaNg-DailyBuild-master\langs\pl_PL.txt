[global]
AriaNg Version=Wersja AriaNg
Operation Result=Wynik operacji
Operation Succeeded=Operacja zakończona sukcesem
is connected=jest połączony
Error=Błąd
OK=OK
Confirm=Potwierdź
Cancel=Anuluj
Close=Zamknij
True=Prawda
False=Fałsz
DEBUG=Debug
INFO=Informacja
WARN=Ostrzeżenie
ERROR=Błąd
Connecting=Łączenie
Connected=Połączony
Disconnected=Rozłączony
Reconnecting=Ponowne łączenie
Waiting to reconnect=Oczekiwanie na ponowne połączenie
Global=Globalne
New=Nowy
Start=Start
Pause=Pauza
Retry=Ponów próbę
Retry Selected Tasks=Ponów wybrane zadania
Delete=Usuń
Select All=Zaznacz wszystko
Select None=Odznacz wszystko
Select Invert=Odwrotne zaznaczenie
Select All Failed Tasks=Zaznacz wszystkie nieudane zadania
Select All Completed Tasks=Zaznacz wszystkie ukończone zadania
Select All Tasks=Zaznacz wszystkie zadania
Display Order=Kolejność wyświetlania
Copy Download Url=Kopiuj adres URL pobierania
Copy Magnet Link=Kopiuj link Magnet
Help=Pomoc
Search=Szukaj
Default=Domyślne
Expand=Rozwiń
Collapse=Zwiń
Expand All=Rozwiń wszystko
Collapse All=Zwiń wszystko
Open=Otwórz
Save=Zapisz
Import=Importuj
Remove Task=Usuń zadanie
Remove Selected Task=Usuń wybrane zadanie
Clear Stopped Tasks=Wyczyść zatrzymane zadania
Click to view task detail=Kliknij, aby zobaczyć szczegóły zadania
By File Name=Według nazwy pliku
By File Size=Według rozmiaru pliku
By Progress=Według postępu
By Selected Status=Według wybranego statusu
By Remaining=Według pozostałej ilości
By Download Speed=Według prędkości pobierania
By Upload Speed=Według prędkości wysyłania
By Peer Address=Według adresu peer
By Client Name=Według nazwy klienta
Filters=Filtry
Download=Pobierz
Upload=Wyślij
Downloading=Pobieranie
Pending Verification=Oczekuje na weryfikację
Verifying=Weryfikacja
Seeding=Udostępnianie
Waiting=Oczekiwanie
Paused=Wstrzymane
Completed=Zakończone
Error Occurred=Wystąpił błąd
Removed=Usunięte
Finished / Stopped=Zakończone / Zatrzymane
Uncompleted=Niezakończone
Click to pin=Kliknij, aby przypiąć
Settings=Ustawienia
AriaNg Settings=Ustawienia AriaNg
Aria2 Settings=Ustawienia Aria2
Basic Settings=Podstawowe ustawienia
HTTP/FTP/SFTP Settings=Ustawienia HTTP/FTP/SFTP
HTTP Settings=Ustawienia HTTP
FTP/SFTP Settings=Ustawienia FTP/SFTP
BitTorrent Settings=Ustawienia BitTorrent
Metalink Settings=Ustawienia Metalink
RPC Settings=Ustawienia RPC
Advanced Settings=Zaawansowane ustawienia
AriaNg Debug Console=Konsola debugowania AriaNg
Aria2 Status=Status Aria2
File Name=Nazwa pliku
File Size=Rozmiar pliku
Progress=Postęp
Share Ratio=Współczynnik udostępniania
Remaining=Pozostało
Download Speed=Prędkość pobierania
Upload Speed=Prędkość wysyłania
Links=Linki
Torrent File=Plik torrent
Metalink File=Plik Metalink
File Name:=Nazwa pliku:
Options=Opcje
Overview=Przegląd
Pieces=Fragmenty
Files=Pliki
Peers=Peery
Task Name=Nazwa zadania
Task Size=Rozmiar zadania
Task Status=Status zadania
Error Description=Opis błędu
Health Percentage=Procent zdrowia
Info Hash=Hash informacji
Seeders=Udostępniający
Connections=Połączenia
Seed Creation Time=Czas utworzenia seeda
Download Url=Adres URL pobierania
Download Dir=Katalog pobierania
BT Tracker Servers=Serwery BT Tracker
Copy=Kopiuj
(Choose Files)=(Wybierz pliki)
Videos=Wideo
Audios=Audio
Pictures=Obrazy
Documents=Dokumenty
Applications=Aplikacje
Archives=Archiwa
Other=Inne
Custom=Niestandardowe
Custom Choose File=Niestandardowy wybór pliku
Address=Adres
Client=Klient
Status=Status
Speed=Prędkość
(local)=(lokalne)
No Data=Brak danych
No connected peers=Brak połączonych peerów
Failed to change some tasks state.=Nie udało się zmienić stanu niektórych zadań.
Confirm Retry=Potwierdź ponowną próbę
Are you sure you want to retry the selected task? AriaNg will create same task after clicking OK.=Czy na pewno chcesz ponowić wybrane zadanie? AriaNg utworzy to samo zadanie po kliknięciu OK.
Failed to retry this task.=Nie udało się ponowić tego zadania.
{successCount} tasks have been retried and {failedCount} tasks are failed.={{successCount}} zadania zostały ponowione, a {{failedCount}} zadań nie powiodło się.
Confirm Remove=Potwierdź usunięcie
Are you sure you want to remove the selected task?=Czy na pewno chcesz usunąć wybrane zadanie?
Failed to remove some task(s).=Nie udało się usunąć niektórych zadań.
Confirm Clear=Potwierdź wyczyszczenie
Are you sure you want to clear stopped tasks?=Czy na pewno chcesz wyczyścić zatrzymane zadania?
Download Links:=Linki do pobrania:
Download Now=Pobierz teraz
Download Later=Pobierz później
Open Torrent File=Otwórz plik torrent
Open Metalink File=Otwórz plik Metalink
Support multiple URLs, one URL per line.=Obsługuj wiele adresów URL, jeden adres URL na linię.
Your browser does not support loading file!=Twoja przeglądarka nie obsługuje ładowania plików!
The selected file type is invalid!=Wybrany typ pliku jest nieprawidłowy!
Failed to load file!=Nie udało się załadować pliku!
Download Completed=Pobieranie zakończone
BT Download Completed=Pobieranie BT zakończone
Download Error=Błąd pobierania
AriaNg Url=Adres URL AriaNg
Command API Url=Adres URL API poleceń
Export Command API=Eksportuj API poleceń
Export=Eksportuj
Copied=Skopiowano
Pause After Task Created=Wstrzymaj po utworzeniu zadania
Language=Język
Theme=Motyw
Light=Jasny
Dark=Ciemny
Follow system settings=Dostosuj do ustawień systemowych
Debug Mode=Tryb debugowania
Page Title=Tytuł strony
Preview=Podgląd
Tips: You can use the "noprefix" tag to ignore the prefix, "nosuffix" tag to ignore the suffix, and "scale\=n" tag to set the decimal precision.=Wskazówki: Możesz użyć tagu "noprefix", aby zignorować prefiks, tagu "nosuffix", aby zignorować sufiks, oraz tagu "scale\=n", aby ustawić precyzję dziesiętną.
Example: ${downspeed:noprefix:nosuffix:scale\=1}=Przykład: ${downspeed:noprefix:nosuffix:scale\=1}
Updating Page Title Interval=Interwał aktualizacji tytułu strony
Enable Browser Notification=Włącz powiadomienia w przeglądarce
Browser Notification Sound=Dźwięk powiadomienia w przeglądarce
Browser Notification Frequency=Częstotliwość powiadomień w przeglądarce
Unlimited=Nieograniczone
High (Up to 10 Notifications / 1 Minute)=Wysoka (do 10 powiadomień / 1 minuta)
Middle (Up to 1 Notification / 1 Minute)=Średnia (do 1 powiadomienia / 1 minuta)
Low (Up to 1 Notification / 5 Minutes)=Niska (do 1 powiadomienia / 5 minut)
WebSocket Auto Reconnect Interval=Interwał automatycznego ponownego łączenia WebSocket
Aria2 RPC Alias=Alias RPC Aria2
Aria2 RPC Address=Adres RPC Aria2
Aria2 RPC Protocol=Protokół RPC Aria2
Aria2 RPC Http Request Method=Metoda żądania HTTP RPC Aria2
POST method only supports aria2 v1.15.2 and above.=Metoda POST obsługuje tylko aria2 w wersji 1.15.2 i nowszej.
Aria2 RPC Request Headers=Nagłówki żądania RPC Aria2
Support multiple request headers, one header per line, each line containing "header name: header value".=Obsługuj wiele nagłówków żądania, jeden nagłówek na linię, każda linia zawiera "nazwa nagłówka: wartość nagłówka".
Aria2 RPC Secret Token=Tajny token RPC Aria2
Activate=Aktywuj
Reset Settings=Resetuj ustawienia
Confirm Reset=Potwierdź reset
Are you sure you want to reset all settings?=Czy na pewno chcesz zresetować wszystkie ustawienia?
Clear Settings History=Wyczyść historię ustawień
Are you sure you want to clear all settings history?=Czy na pewno chcesz wyczyścić całą historię ustawień?
Delete RPC Setting=Usuń ustawienie RPC
Add New RPC Setting=Dodaj nowe ustawienie RPC
Are you sure you want to remove rpc setting "{rpcName}"?=Czy na pewno chcesz usunąć ustawienie RPC "{{rpcName}}"?
Updating Global Stat Interval=Interwał aktualizacji statystyk globalnych
Updating Task Information Interval=Interwał aktualizacji informacji o zadaniach
Keyboard Shortcuts=Skróty klawiszowe
Supported Keyboard Shortcuts=Obsługiwane skróty klawiszowe
Set Focus On Search Box=Ustaw fokus na polu wyszukiwania
Swipe Gesture=Gest przesuwania
Change Tasks Order by Drag-and-drop=Zmień kolejność zadań przez przeciąganie i upuszczanie
Action After Creating New Tasks=Akcja po utworzeniu nowych zadań
Navigate to Task List Page=Przejdź do strony listy zadań
Navigate to Task Detail Page=Przejdź do strony szczegółów zadania
Action After Retrying Task=Akcja po ponowieniu zadania
Navigate to Downloading Tasks Page=Przejdź do strony pobierania zadań
Stay on Current Page=Pozostań na bieżącej stronie
Remove Old Tasks After Retrying=Usuń stare zadania po ponowieniu
Confirm Task Removal=Potwierdź usunięcie zadania
Include Prefix When Copying From Task Details=Uwzględnij prefiks podczas kopiowania ze szczegółów zadania
Show Pieces Info In Task Detail Page=Pokaż informacje o fragmentach na stronie szczegółów zadania
Pieces Amount is Less than or Equal to {value}=Liczba fragmentów jest mniejsza lub równa {{value}}
RPC List Display Order=Kolejność wyświetlania listy RPC
Each Task List Page Uses Independent Display Order=Każda strona listy zadań używa niezależnej kolejności wyświetlania
Recently Used=Ostatnio używane
RPC Alias=Alias RPC
Import / Export AriaNg Settings=Importuj / Eksportuj ustawienia AriaNg
Import Settings=Importuj ustawienia
Export Settings=Eksportuj ustawienia
AriaNg settings data=Dane ustawień AriaNg
Confirm Import=Potwierdź import
Are you sure you want to import all settings?=Czy na pewno chcesz zaimportować wszystkie ustawienia?
Invalid settings data format!=Nieprawidłowy format danych ustawień!
Data has been copied to clipboard.=Dane zostały skopiowane do schowka.
Supported Placeholder=Obsługiwany zastępczy
AriaNg Title=Tytuł AriaNg
Current RPC Alias=Bieżący alias RPC
Downloading Count=Liczba pobieranych
Waiting Count=Liczba oczekujących
Stopped Count=Liczba zatrzymanych
You have disabled notification in your browser. You should change your browser's settings before you enable this function.=Wyłączyłeś powiadomienia w przeglądarce. Powinieneś zmienić ustawienia przeglądarki przed włączeniem tej funkcji.
Language resource has been updated, please reload the page for the changes to take effect.=Zasób językowy został zaktualizowany, przeładuj stronę, aby zmiany zostały zastosowane.
Configuration has been modified, please reload the page for the changes to take effect.=Konfiguracja została zmodyfikowana, przeładuj stronę, aby zmiany zostały zastosowane.
Reload AriaNg=Przeładuj AriaNg
Show Secret=Pokaż sekret
Hide Secret=Ukryj sekret
Aria2 Version=Wersja Aria2
Enabled Features=Włączone funkcje
Operations=Operacje
Reconnect=Ponowne łączenie
Save Session=Zapisz sesję
Shutdown Aria2=Wyłącz Aria2
Confirm Shutdown=Potwierdź wyłączenie
Are you sure you want to shutdown aria2?=Czy na pewno chcesz wyłączyć aria2?
Session has been saved successfully.=Sesja została pomyślnie zapisana.
Aria2 has been shutdown successfully.=Aria2 zostało pomyślnie wyłączone.
Toggle Navigation=Przełącz nawigację
Shortcut=Skrót
Global Rate Limit=Globalne ograniczenie prędkości
Loading=Ładowanie...
More Than One Day=Więcej niż 1 dzień
Unknown=Nieznane
Bytes=Bajty
Hours=Godziny
Minutes=Minuty
Seconds=Sekundy
Milliseconds=Milisekundy
Http=Http
Http (Disabled)=Http (wyłączone)
Https=Https
WebSocket=WebSocket
WebSocket (Disabled)=WebSocket (wyłączone)
WebSocket (Security)=WebSocket (bezpieczeństwo)
Http and WebSocket would be disabled when accessing AriaNg via Https.=Http i WebSocket będą wyłączone podczas dostępu do AriaNg przez Https.
POST=POST
GET=GET
Enabled=Włączone
Disabled=Wyłączone
Always=Zawsze
Never=Nigdy
BitTorrent=BitTorrent
Changes to the settings take effect after refreshing page.=Zmiany w ustawieniach zostaną zastosowane po odświeżeniu strony.
Logging Time=Czas logowania
Log Level=Poziom logowania
Auto Refresh=Automatyczne odświeżanie
Refresh Now=Odśwież teraz
Clear Logs=Wyczyść logi
Are you sure you want to clear debug logs?=Czy na pewno chcesz wyczyścić logi debugowania?
Show Detail=Pokaż szczegóły
Log Detail=Szczegóły logu
Aria2 RPC Debug=Debugowanie RPC Aria2
Aria2 RPC Request Method=Metoda żądania RPC Aria2
Aria2 RPC Request Parameters=Parametry żądania RPC Aria2
Aria2 RPC Response=Odpowiedź RPC Aria2
Execute=Wykonaj
RPC method is illegal!=Metoda RPC jest nielegalna!
AriaNg does not support this RPC method!=AriaNg nie obsługuje tej metody RPC!
RPC request parameters are invalid!=Parametry żądania RPC są nieprawidłowe!
Type is illegal!=Typ jest nielegalny!
Parameter is invalid!=Parametr jest nieprawidłowy!
Option value cannot be empty!=Wartość opcji nie może być pusta!
Input number is invalid!=Wprowadzona liczba jest nieprawidłowa!
Input number is below min value!=Wprowadzona liczba jest poniżej minimalnej wartości {{value}}!
Input number is above max value!=Wprowadzona liczba jest powyżej maksymalnej wartości {{value}}!
Input value is invalid!=Wprowadzona wartość jest nieprawidłowa!
Protocol is invalid!=Protokół jest nieprawidłowy!
RPC host cannot be empty!=Host RPC nie może być pusty!
RPC secret is not base64 encoded!=Sekret RPC nie jest zakodowany w base64!
URL is not base64 encoded!=URL nie jest zakodowany w base64!
Tap to configure and get started with AriaNg.=Kliknij, aby skonfigurować i rozpocząć pracę z AriaNg.
Cannot initialize WebSocket!=Nie można zainicjować WebSocket!
Cannot connect to aria2!=Nie można połączyć się z aria2!
Access Denied!=Odmowa dostępu!
You cannot use AriaNg because this browser does not meet the minimum requirements for data storage.=Nie możesz używać AriaNg, ponieważ ta przeglądarka nie spełnia minimalnych wymagań dotyczących przechowywania danych.

[error]
unknown=Wystąpił nieznany błąd.
operation.timeout=Przekroczono limit czasu operacji.
resource.notfound=Zasób nie został znaleziony.
resource.notfound.max-file-not-found=Zasób nie został znaleziony. Zobacz opcję --max-file-not-found.
download.aborted.lowest-speed-limit=Pobieranie zostało przerwane, ponieważ prędkość pobierania była zbyt niska. Zobacz opcję --lowest-speed-limit.
network.problem=Wystąpił problem z siecią.
resume.notsupported=Zdalny serwer nie obsługuje wznowienia.
space.notenough=Brak wystarczającej ilości miejsca na dysku.
piece.length.different=Długość fragmentu różniła się od tej w pliku kontrolnym .aria2. Zobacz opcję --allow-piece-length-change.
download.sametime=aria2 pobierało ten sam plik w tym samym czasie.
download.torrent.sametime=aria2 pobierało ten sam plik w tym samym czasie.
file.exists=Plik już istnieje. Zobacz opcję --allow-overwrite.
file.rename.failed=Nie udało się zmienić nazwy pliku. Zobacz opcję --auto-file-renaming.
file.open.failed=Nie udało się otworzyć istniejącego pliku.
file.create.failed=Nie udało się utworzyć nowego pliku lub przyciąć istniejącego pliku.
io.error=Wystąpił błąd systemu plików.
directory.create.failed=Nie udało się utworzyć katalogu.
name.resolution.failed=Nie udało się rozwiązać nazwy domeny.
metalink.file.parse.failed=Nie udało się przeanalizować dokumentu Metalink.
ftp.command.failed=Polecenie FTP nie powiodło się.
http.response.header.bad=Nagłówek odpowiedzi HTTP był nieprawidłowy lub nieoczekiwany.
redirects.toomany=Wystąpiło zbyt wiele przekierowań.
http.authorization.failed=Autoryzacja HTTP nie powiodła się.
bencoded.file.parse.failed=Nie udało się przeanalizować pliku zakodowanego w bencode (zwykle plik ".torrent").
torrent.file.corrupted=Plik ".torrent" był uszkodzony lub brakowało informacji potrzebnych dla aria2.
magnet.uri.bad=Link Magnet był nieprawidłowy.
option.bad=Podano nieprawidłową/nieznaną opcję lub nieoczekiwany argument opcji.
server.overload=Zdalny serwer nie mógł obsłużyć żądania z powodu tymczasowego przeciążenia lub konserwacji.
rpc.request.parse.failed=Nie udało się przeanalizować żądania JSON-RPC.
checksum.failed=Weryfikacja sumy kontrolnej nie powiodła się.

[languages]
Czech=Czeski
German=Niemiecki
English=Angielski
Spanish=Hiszpański
French=Francuski
Italian=Włoski
Polish=Polski
Russian=Rosyjski
Simplified Chinese=Chiński uproszczony
Traditional Chinese=Chiński tradycyjny

[format]
longdate=MM/DD/YYYY HH:mm:ss
time.millisecond={{value}} Milisekunda
time.milliseconds={{value}} Milisekundy
time.second={{value}} Sekunda
time.seconds={{value}} Sekundy
time.minute={{value}} Minuta
time.minutes={{value}} Minuty
time.hour={{value}} Godzina
time.hours={{value}} Godziny
requires.aria2-version=Wymaga aria2 w wersji {{version}} lub wyższej
task.new.download-links=Linki do pobrania ({{count}} linków):
task.pieceinfo=Ukończono: {{completed}}, Łącznie: {{total}}
task.error-occurred=Wystąpił błąd ({{errorcode}})
task.verifying-percent=Weryfikacja ({{verifiedPercent}}%)
settings.file-count=({{count}} plików)
settings.total-count=(Łączna liczba: {{count}})
debug.latest-logs=Ostatnie {{count}} logi

[rpc.error]
unauthorized=Autoryzacja nie powiodła się!

[option]
true=Prawda
false=Fałsz
default=Domyślne
none=Brak
hide=Ukryj
full=Pełne
http=Http
https=Https
ftp=Ftp
mem=Tylko pamięć
get=GET
tunnel=TUNNEL
plain=Zwykłe
arc4=ARC4
binary=Binarne
ascii=ASCII
debug=Debug
info=Informacja
notice=Uwaga
warn=Ostrzeżenie
error=Błąd
adaptive=adaptacyjne
epoll=epoll
falloc=falloc
feedback=feedback
geom=geom
inorder=w kolejności
kqueue=kqueue
poll=poll
port=port
prealloc=prealloc
random=losowe
select=select
trunc=trunc
SSLv3=SSLv3
TLSv1=TLSv1
TLSv1.1=TLSv1.1
TLSv1.2=TLSv1.2

[options]
dir.name=Ścieżka pobierania
dir.description=
log.name=Plik dziennika
log.description=Nazwa pliku dziennika. Jeśli podano -, dziennik jest zapisywany na standardowe wyjście. Jeśli podano pusty ciąg (""), dziennik nie jest zapisywany na dysku.
max-concurrent-downloads.name=Maksymalna liczba równoczesnych pobrań
max-concurrent-downloads.description=
check-integrity.name=Sprawdź integralność
check-integrity.description=Sprawdź integralność pliku, weryfikując hashe fragmentów lub hash całego pliku. Ta opcja ma wpływ tylko na pobieranie BitTorrent, Metalink z sumami kontrolnymi lub pobieranie HTTP(S)/FTP z opcją --checksum.
continue.name=Wznów pobieranie
continue.description=Wznów pobieranie częściowo pobranego pliku. Użyj tej opcji, aby wznowić pobieranie rozpoczęte przez przeglądarkę internetową lub inny program, który pobiera pliki sekwencyjnie od początku. Obecnie ta opcja dotyczy tylko pobierania HTTP(S)/FTP.
all-proxy.name=Serwer proxy
all-proxy.description=Użyj serwera proxy dla wszystkich protokołów. Możesz również zastąpić to ustawienie i określić serwer proxy dla konkretnego protokołu, używając --http-proxy, --https-proxy i --ftp-proxy. Wpływa to na wszystkie pobierania. Format PROXY to [http://][USER:PASSWORD@]HOST[:PORT].
all-proxy-user.name=Nazwa użytkownika proxy
all-proxy-user.description=
all-proxy-passwd.name=Hasło proxy
all-proxy-passwd.description=
checksum.name=Suma kontrolna
checksum.description=Ustaw sumę kontrolną. Format wartości opcji to TYPE\=DIGEST. TYPE to typ hasha. Obsługiwane typy hashów są wymienione w algorytmach hashujących w aria2c -v. DIGEST to szesnastkowy skrót. Na przykład ustawienie skrótu sha-1 wygląda tak: sha-1=0192ba11326fe2298c8cb4de616f4d4140213838. Ta opcja dotyczy tylko pobierania HTTP(S)/FTP.
connect-timeout.name=Limit czasu połączenia
connect-timeout.description=Ustaw limit czasu w sekundach na nawiązanie połączenia z serwerem HTTP/FTP/proxy. Po nawiązaniu połączenia ta opcja nie ma wpływu, a zamiast niej używana jest opcja --timeout.
dry-run.name=Symulacja
dry-run.description=Jeśli podano true, aria2 tylko sprawdza, czy zdalny plik jest dostępny, i nie pobiera danych. Ta opcja ma wpływ na pobieranie HTTP/FTP. Pobieranie BitTorrent jest anulowane, jeśli podano true.
lowest-speed-limit.name=Minimalna prędkość pobierania
lowest-speed-limit.description=Zamknij połączenie, jeśli prędkość pobierania jest mniejsza lub równa tej wartości (bajty na sekundę). 0 oznacza, że aria2 nie ma minimalnej prędkości pobierania. Możesz dodać K lub M (1K = 1024, 1M = 1024K). Ta opcja nie wpływa na pobieranie BitTorrent.
max-connection-per-server.name=Maksymalna liczba połączeń na serwer
max-connection-per-server.description=
max-file-not-found.name=Maksymalna liczba prób nieznalezienia pliku
max-file-not-found.description=Jeśli aria2 otrzyma status "plik nie znaleziony" od zdalnych serwerów HTTP/FTP NUM razy bez pobrania ani jednego bajtu, wymuś niepowodzenie pobierania. Podaj 0, aby wyłączyć tę opcję. Ta opcja jest skuteczna tylko przy użyciu serwerów HTTP/FTP. Liczba prób jest liczona w kierunku --max-tries, więc należy ją również skonfigurować.
max-tries.name=Maksymalna liczba prób
max-tries.description=Ustaw liczbę prób. 0 oznacza nieograniczoną liczbę prób.
min-split-size.name=Minimalny rozmiar podziału
min-split-size.description=aria2 nie dzieli zakresu mniejszego niż 2*SIZE bajtów. Na przykład rozważmy pobieranie pliku 20MiB. Jeśli SIZE to 10M, aria2 może podzielić plik na 2 zakresy [0-10MiB) i [10MiB-20MiB) i pobrać go za pomocą 2 źródeł (jeśli --split >= 2, oczywiście). Jeśli SIZE to 15M, ponieważ 2*15M > 20MiB, aria2 nie dzieli pliku i pobiera go za pomocą 1 źródła. Możesz dodać K lub M (1K = 1024, 1M = 1024K). Możliwe wartości: 1M-1024M.
netrc-path.name=Ścieżka .netrc
netrc-path.description=
no-netrc.name=Wyłącz netrc
no-netrc.description=
no-proxy.name=Lista bez proxy
no-proxy.description=Określ listę nazw hostów, domen i adresów sieciowych z maską podsieci lub bez, gdzie nie powinno się używać proxy.
out.name=Nazwa pliku
out.description=Nazwa pobieranego pliku. Jest zawsze względna do katalogu podanego w opcji --dir. Gdy używana jest opcja --force-sequential, ta opcja jest ignorowana.
proxy-method.name=Metoda proxy
proxy-method.description=Ustaw metodę używaną w żądaniu proxy. METHOD to albo GET, albo TUNNEL. Pobieranie HTTPS zawsze używa TUNNEL, niezależnie od tej opcji.
remote-time.name=Zdalny znacznik czasu pliku
remote-time.description=Pobierz znacznik czasu zdalnego pliku z zdalnego serwera HTTP/FTP i, jeśli jest dostępny, zastosuj go do lokalnego pliku.
reuse-uri.name=Ponowne użycie URI
reuse-uri.description=Ponownie użyj już użytych URI, jeśli nie ma dostępnych nieużywanych URI.
retry-wait.name=Czas oczekiwania między próbami
retry-wait.description=Ustaw liczbę sekund do oczekiwania między próbami. Gdy SEC > 0, aria2 ponowi pobieranie, gdy serwer HTTP zwróci odpowiedź 503.
server-stat-of.name=Plik statystyk serwera
server-stat-of.description=Określ nazwę pliku, do którego zapisywany jest profil wydajności serwerów. Możesz załadować zapisane dane za pomocą opcji --server-stat-if.
server-stat-timeout.name=Limit czasu statystyk serwera
server-stat-timeout.description=Określa limit czasu w sekundach na unieważnienie profilu wydajności serwerów od ostatniego kontaktu z nimi.
split.name=Liczba podziałów
split.description=Pobierz plik za pomocą N połączeń. Jeśli podano więcej niż N URI, pierwsze N URI jest używane, a pozostałe URI są używane jako zapasowe. Jeśli podano mniej niż N URI, te URI są używane więcej niż raz, tak aby łącznie było N jednoczesnych połączeń. Liczba połączeń do tego samego hosta jest ograniczona przez opcję --max-connection-per-server.
stream-piece-selector.name=Algorytm wyboru fragmentów
stream-piece-selector.description=Określ algorytm wyboru fragmentów używany w pobieraniu HTTP/FTP. Fragment oznacza segment o stałej długości, który jest pobierany równolegle w pobieraniu segmentowym. Jeśli podano default, aria2 wybiera fragment tak, aby zmniejszyć liczbę nawiązywanych połączeń. Jest to rozsądne domyślne zachowanie, ponieważ nawiązanie połączenia jest kosztowną operacją. Jeśli podano inorder, aria2 wybiera fragment o najniższym indeksie. Indeks=0 oznacza pierwszy fragment pliku. Będzie to przydatne do oglądania filmu podczas jego pobierania. Opcja --enable-http-pipelining może być przydatna do zmniejszenia narzutu ponownego łączenia. Należy pamiętać, że aria2 honoruje opcję --min-split-size, więc konieczne będzie określenie rozsądnej wartości dla opcji --min-split-size. Jeśli podano random, aria2 wybiera fragment losowo. Podobnie jak w przypadku inorder, opcja --min-split-size jest honorowana. Jeśli podano geom, na początku aria2 wybiera fragment o najniższym indeksie, jak w przypadku inorder, ale wykładniczo zwiększa odstęp od poprzednio wybranego fragmentu. Zmniejszy to liczbę nawiązywanych połączeń i jednocześnie pobierze początkową część pliku jako pierwszą. Będzie to przydatne do oglądania filmu podczas jego pobierania.
timeout.name=Limit czasu
timeout.description=
uri-selector.name=Algorytm wyboru URI
uri-selector.description=Określ algorytm wyboru URI. Możliwe wartości to inorder, feedback i adaptive. Jeśli podano inorder, URI są próbowane w kolejności pojawienia się na liście URI. Jeśli podano feedback, aria2 używa prędkości pobierania obserwowanej w poprzednich pobraniach i wybiera najszybszy serwer na liście URI. To również skutecznie pomija martwe lustra. Obserwowana prędkość pobierania jest częścią profilu wydajności serwerów wymienionych w --server-stat-of i --server-stat-if. Jeśli podano adaptive, wybiera jedno z najlepszych luster dla pierwszego i zarezerwowanego połączenia. Dla uzupełniających, zwraca lustra, które nie zostały jeszcze przetestowane, a jeśli każde z nich zostało już przetestowane, zwraca lustra, które muszą zostać ponownie przetestowane. W przeciwnym razie nie wybiera więcej luster. Podobnie jak feedback, używa profilu wydajności serwerów.
check-certificate.name=Sprawdź certyfikat
check-certificate.description=
http-accept-gzip.name=Akceptuj GZip
http-accept-gzip.description=Wyślij nagłówek żądania Accept: deflate, gzip i zdekompresuj odpowiedź, jeśli zdalny serwer odpowiada nagłówkiem Content-Encoding: gzip lub Content-Encoding: deflate.
http-auth-challenge.name=Wyzwanie autoryzacji HTTP
http-auth-challenge.description=Wyślij nagłówek autoryzacji HTTP tylko wtedy, gdy jest to wymagane przez serwer. Jeśli podano false, nagłówek autoryzacji jest zawsze wysyłany do serwera. Istnieje wyjątek: jeśli nazwa użytkownika i hasło są osadzone w URI, nagłówek autoryzacji jest zawsze wysyłany do serwera, niezależnie od tej opcji.
http-no-cache.name=Brak cache
http-no-cache.description=Wysyłaj nagłówki Cache-Control: no-cache i Pragma: no-cache, aby uniknąć buforowania treści. Jeśli ustawione na false, te nagłówki nie są wysyłane i można dodać nagłówek Cache-Control z dowolną dyrektywą, używając opcji --header.
http-user.name=Domyślna nazwa użytkownika HTTP
http-user.description=
http-passwd.name=Domyślne hasło HTTP
http-passwd.description=
http-proxy.name=Serwer proxy HTTP
http-proxy.description=
http-proxy-user.name=Nazwa użytkownika proxy HTTP
http-proxy-user.description=
http-proxy-passwd.name=Hasło proxy HTTP
http-proxy-passwd.description=
https-proxy.name=Serwer proxy HTTPS
https-proxy.description=
https-proxy-user.name=Nazwa użytkownika proxy HTTPS
https-proxy-user.description=
https-proxy-passwd.name=Hasło proxy HTTPS
https-proxy-passwd.description=
referer.name=Referer
referer.description=Ustawia odsyłający adres HTTP (Referer). Dotyczy wszystkich pobrań HTTP/HTTPS. Jeśli podano *, adres URI pobierania jest również używany jako referer. Może to być przydatne w połączeniu z opcją --parameterized-uri.
enable-http-keep-alive.name=Włącz trwałe połączenie
enable-http-keep-alive.description=Włącz trwałe połączenie HTTP/1.1.
enable-http-pipelining.name=Włącz HTTP Pipelining
enable-http-pipelining.description=Włącz pipelining HTTP/1.1.
header.name=Niestandardowy nagłówek
header.description=Dodaj nagłówek HEADER do żądania HTTP. Każdy wpis w osobnej linii w formacie "nazwa nagłówka: wartość nagłówka".
save-cookies.name=Ścieżka do pliku cookies
save-cookies.description=Zapisz pliki cookies do pliku FILE w formacie Mozilla/Firefox(1.x/2.x)/Netscape. Jeśli FILE już istnieje, zostanie nadpisany. Sesyjne cookies również są zapisywane, a ich wartości wygaśnięcia są traktowane jako 0.
use-head.name=Użyj metody HEAD
use-head.description=Użyj metody HEAD dla pierwszego żądania do serwera HTTP.
user-agent.name=Niestandardowy User Agent
user-agent.description=
ftp-user.name=Domyślna nazwa użytkownika FTP
ftp-user.description=
ftp-passwd.name=Domyślne hasło FTP
ftp-passwd.description=Jeśli nazwa użytkownika jest osadzona w URI, ale hasło nie, aria2 spróbuje znaleźć hasło w pliku .netrc. Jeśli je znajdzie, zostanie użyte, w przeciwnym razie użyte zostanie hasło określone w tej opcji.
ftp-pasv.name=Tryb pasywny
ftp-pasv.description=Użyj trybu pasywnego w FTP. Jeśli ustawione na false, używany będzie tryb aktywny. Opcja ta jest ignorowana dla transferów SFTP.
ftp-proxy.name=Serwer proxy FTP
ftp-proxy.description=
ftp-proxy-user.name=Nazwa użytkownika proxy FTP
ftp-proxy-user.description=
ftp-proxy-passwd.name=Hasło proxy FTP
ftp-proxy-passwd.description=
ftp-type.name=Typ transferu
ftp-type.description=
ftp-reuse-connection.name=Ponowne użycie połączenia
ftp-reuse-connection.description=
ssh-host-key-md.name=Suma kontrolna klucza publicznego SSH
ssh-host-key-md.description=Ustaw sumę kontrolną klucza publicznego hosta SSH. Format wartości to TYPE=DIGEST. TYPE to typ hasha (obsługiwane: sha-1 lub md5). DIGEST to suma kontrolna w formacie szesnastkowym. Przykład: sha-1=b030503d4de4539dc7885e6f0f5e256704edf4c3. Opcja ta może być używana do weryfikacji klucza publicznego serwera podczas korzystania z SFTP. Jeśli nie ustawiono tej opcji (domyślnie), weryfikacja nie jest wykonywana.
bt-detach-seed-only.name=Oddziel tylko seedowanie
bt-detach-seed-only.description=Wyklucz pobrania będące tylko w trybie seedowania przy liczeniu aktywnych pobrań (patrz opcja -j). Oznacza to, że jeśli podano -j3 i ta opcja jest włączona, a 3 pobrania są aktywne, a jedno przejdzie w tryb seedowania, zostaje ono wykluczone z liczenia aktywnych pobrań (stając się 2), co pozwala na uruchomienie kolejnego pobrania z kolejki. Należy jednak pamiętać, że w metodzie RPC element seedujący nadal jest uznawany za aktywne pobranie.
bt-enable-hook-after-hash-check.name=Włącz hook po sprawdzeniu hasha
bt-enable-hook-after-hash-check.description=Zezwól na wykonanie polecenia hook po sprawdzeniu hasha (patrz opcja -V) w pobraniach BitTorrent. Domyślnie, jeśli weryfikacja hasha zakończy się sukcesem, wykonywane jest polecenie określone w opcji --on-bt-download-complete. Aby wyłączyć tę akcję, ustaw tę opcję na false.
bt-enable-lpd.name=Włącz Local Peer Discovery (LPD)
bt-enable-lpd.description=Włącz Local Peer Discovery. Jeśli torrent ma ustawioną flagę prywatną, aria2 nie użyje tej funkcji dla tego pobrania, nawet jeśli wartość opcji jest ustawiona na true.
bt-exclude-tracker.name=Wyklucz trackery BitTorrent
bt-exclude-tracker.description=Lista oddzielona przecinkami z URI trackerów BitTorrent do usunięcia. Można użyć specjalnej wartości *, która pasuje do wszystkich URI i usuwa wszystkie ogłoszenia trackerów. Przy używaniu * w wierszu poleceń powłoki nie zapomnij go uciec lub umieścić w cudzysłowie.
bt-external-ip.name=Zewnętrzny adres IP
bt-external-ip.description=Określ zewnętrzny adres IP do użycia w pobraniach BitTorrent i DHT. Może być wysyłany do trackera BitTorrent. W przypadku DHT ta opcja powinna być ustawiona, aby zgłosić, że lokalny węzeł pobiera określony torrent. Jest to kluczowe do użycia DHT w sieci prywatnej. Chociaż funkcja nosi nazwę "zewnętrzna", akceptuje dowolne adresy IP.
bt-force-encryption.name=Wymuś szyfrowanie
bt-force-encryption.description=Wymaga szyfrowania ładunku wiadomości BitTorrent przy użyciu arc4. Jest to skrót dla --bt-require-crypto --bt-min-crypto-level=arc4. Ta opcja nie zmienia wartości tych opcji. Jeśli ustawione na true, aria2 odrzuca tradycyjny handshake BitTorrent i używa tylko Obfuscation handshake, zawsze szyfrując ładunek wiadomości.
bt-hash-check-seed.name=Sprawdzenie hasha przed seedowaniem
bt-hash-check-seed.description=Jeśli ustawione na true, po sprawdzeniu hasha przy użyciu opcji --check-integrity i ukończeniu pliku, aria2 kontynuuje jego seedowanie. Jeśli chcesz sprawdzić plik i pobrać go tylko wtedy, gdy jest uszkodzony lub niekompletny, ustaw tę opcję na false. Ta opcja ma zastosowanie tylko do pobrań BitTorrent.
bt-load-saved-metadata.name=Wczytaj zapisany plik metadanych
bt-load-saved-metadata.description=Przed pobraniem metadanych torrenta z DHT przy pobieraniu za pomocą linku magnet, najpierw spróbuj odczytać plik zapisany przez opcję --bt-save-metadata. Jeśli się powiedzie, pominięte zostanie pobieranie metadanych z DHT.
bt-max-open-files.name=Maksymalna liczba otwartych plików
bt-max-open-files.description=Określ maksymalną liczbę otwartych plików w globalnym pobieraniu BitTorrent/Metalink.
bt-max-peers.name=Maksymalna liczba peerów
bt-max-peers.description=Określ maksymalną liczbę peerów na torrent. 0 oznacza brak ograniczeń.
bt-metadata-only.name=Pobieraj tylko metadane
bt-metadata-only.description=Pobieraj tylko metadane. Pliki opisane w metadanych nie będą pobierane. Ta opcja działa tylko w przypadku korzystania z BitTorrent Magnet URI.
bt-min-crypto-level.name=Minimalny poziom szyfrowania
bt-min-crypto-level.description=Ustaw minimalny poziom metody szyfrowania. Jeśli peer oferuje kilka metod szyfrowania, aria2 wybiera najniższy poziom spełniający podane wymagania.
bt-prioritize-piece.name=Priorytetowe fragmenty
bt-prioritize-piece.description=Spróbuj pobrać najpierw pierwsze i ostatnie fragmenty każdego pliku. Jest to przydatne do podglądu plików. Argument może zawierać dwa słowa kluczowe: head i tail. Aby użyć obu, należy je oddzielić przecinkiem. Można także określić rozmiar, np. head=ROZMIAR oznacza nadanie wyższego priorytetu fragmentom z pierwszych ROZMIAR bajtów każdego pliku. tail=ROZMIAR odnosi się do ostatnich bajtów. ROZMIAR może być podany w K lub M (1K = 1024, 1M = 1024K).
bt-remove-unselected-file.name=Usuń nieoznaczone pliki
bt-remove-unselected-file.description=Usuwa nieoznaczone pliki po zakończeniu pobierania torrenta. Aby wybrać pliki, użyj opcji --select-file. Jeśli nie zostanie użyta, wszystkie pliki są traktowane jako wybrane. Używaj tej opcji ostrożnie, ponieważ pliki zostaną faktycznie usunięte z dysku.
bt-require-crypto.name=Wymagaj szyfrowania
bt-require-crypto.description=Jeśli ustawione na true, aria2 nie akceptuje i nie nawiązuje połączeń przy użyciu przestarzałego BitTorrent handshake (\19BitTorrent protocol). Zamiast tego zawsze używa szyfrowania Obfuscation handshake.
bt-request-peer-speed-limit.name=Preferowana prędkość pobierania
bt-request-peer-speed-limit.description=Jeśli całkowita prędkość pobierania wszystkich torrentów jest niższa niż SPEED, aria2 tymczasowo zwiększa liczbę peerów w celu uzyskania większej prędkości. Możesz dodać K lub M (1K = 1024, 1M = 1024K).
bt-save-metadata.name=Zapisz metadane
bt-save-metadata.description=Zapisz metadane jako plik .torrent. Ta opcja działa tylko przy użyciu BitTorrent Magnet URI. Nazwa pliku to zakodowany hash info z rozszerzeniem .torrent. Plik zostanie zapisany w tym samym katalogu co pobierany plik. Jeśli taki plik już istnieje, metadane nie zostaną zapisane.
bt-seed-unverified.name=Nie weryfikuj pobranych plików
bt-seed-unverified.description=Udostępniaj wcześniej pobrane pliki bez weryfikacji ich sum kontrolnych.
bt-stop-timeout.name=Czas oczekiwania na zatrzymanie
bt-stop-timeout.description=Zatrzymaj pobieranie torrenta, jeśli prędkość pobierania wynosi 0 przez kolejne SEC sekund. Wartość 0 wyłącza tę funkcję.
bt-tracker.name=Trackery BitTorrenta
bt-tracker.description=Lista dodatkowych trackerów BitTorrenta oddzielonych przecinkami. Te adresy nie są usuwane przez opcję --bt-exclude-tracker, ponieważ są dodawane po zastosowaniu tej opcji.
bt-tracker-connect-timeout.name=Czas oczekiwania na połączenie z trackerem
bt-tracker-connect-timeout.description=Ustaw czas oczekiwania (w sekundach) na nawiązanie połączenia z trackerem. Po nawiązaniu połączenia ta opcja nie ma już wpływu, zamiast tego stosowana jest opcja --bt-tracker-timeout.
bt-tracker-interval.name=Interwał połączeń z trackerem
bt-tracker-interval.description=Ustaw interwał (w sekundach) między zapytaniami do trackerów. Całkowicie nadpisuje wartość interwału zwróconą przez tracker. Wartość 0 pozwala aria2 określić interwał na podstawie odpowiedzi trackera i postępu pobierania.
bt-tracker-timeout.name=Limit czasu trackera
bt-tracker-timeout.description=
dht-file-path.name=Plik DHT (IPv4)
dht-file-path.description=Zmień plik tablicy routingu DHT dla IPv4 na PATH.
dht-file-path6.name=Plik DHT (IPv6)
dht-file-path6.description=Zmień plik tablicy routingu DHT dla IPv6 na PATH.
dht-listen-port.name=Port nasłuchiwania DHT
dht-listen-port.description=Ustaw port UDP używany przez DHT (IPv4, IPv6) oraz trackery UDP. Można określić wiele portów, np. 6881,6885. Można także podać zakres, np. 6881-6999. Obie metody można łączyć: 6881-6889,6999.
dht-message-timeout.name=Czas oczekiwania na wiadomość DHT
dht-message-timeout.description=
enable-dht.name=Włącz DHT (IPv4)
enable-dht.description=Włącz funkcję DHT dla IPv4. Automatycznie włącza także wsparcie dla trackerów UDP. Jeśli torrent ma ustawioną flagę prywatności, aria2 nie użyje DHT dla tego pobrania, nawet jeśli opcja jest ustawiona na true.
enable-dht6.name=Włącz DHT (IPv6)
enable-dht6.description=Włącz funkcję DHT dla IPv6. Jeśli torrent ma ustawioną flagę prywatności, aria2 nie użyje DHT dla tego pobrania, nawet jeśli opcja jest ustawiona na true. Aby określić port nasłuchiwania, użyj opcji --dht-listen-port.
enable-peer-exchange.name=Włącz wymianę peerów
enable-peer-exchange.description=Włącz rozszerzenie wymiany peerów (PEX). Jeśli torrent ma ustawioną flagę prywatności, ta funkcja jest wyłączona, nawet jeśli true jest ustawione.
follow-torrent.name=Śledź torrenta
follow-torrent.description=Jeśli ustawione na true lub mem, pliki z rozszerzeniem .torrent lub typem MIME application/x-bittorrent są traktowane jako torrenty i ich zawartość jest pobierana. Jeśli ustawione na mem, plik .torrent nie jest zapisywany na dysku, a jedynie przechowywany w pamięci. Jeśli false, plik .torrent jest pobierany, ale nie jest analizowany ani używany do pobierania jego zawartości.
listen-port.name=Port nasłuchiwania
listen-port.description=Ustaw numer portu TCP dla pobrań BitTorrenta. Można określić wiele portów, np. 6881,6885. Można także podać zakres, np. 6881-6999, lub kombinację: 6881-6889,6999.
max-overall-upload-limit.name=Maksymalny globalny limit wysyłania
max-overall-upload-limit.description=Ustaw maksymalną globalną prędkość wysyłania w bajtach na sekundę. 0 oznacza brak ograniczeń. Możesz dodać K lub M (1K = 1024, 1M = 1024K).
max-upload-limit.name=Maksymalny limit wysyłania
max-upload-limit.description=Ustaw maksymalną prędkość wysyłania na torrent w bajtach na sekundę. 0 oznacza brak ograniczeń. Możesz dodać K lub M (1K = 1024, 1M = 1024K).
peer-id-prefix.name=Prefiks ID peera
peer-id-prefix.description=Określ prefiks ID peera. Pełne ID ma długość 20 bajtów. Jeśli podasz więcej, zostanie przycięte. Jeśli mniej, zostaną dodane losowe bajty.
peer-agent.name=Agent Peera
peer-agent.description=Określa ciąg używany podczas rozszerzonego handshake BitTorrenta dla wersji klienta peera.
seed-ratio.name=Minimalny Współczynnik Udostępniania
seed-ratio.description=Określa współczynnik udostępniania. Seedy zakończone torrenty do momentu, gdy współczynnik udostępniania osiągnie RATIO. Zaleca się ustawienie wartości równej lub większej niż 1.0. Ustawienie 0.0 powoduje seedowanie bez względu na współczynnik udostępniania. Jeśli opcja --seed-time jest również określona, seedowanie kończy się, gdy spełniony zostanie przynajmniej jeden z warunków.
seed-time.name=Minimalny Czas Seedowania
seed-time.description=Określa czas seedowania w (częściowych) minutach. Ustawienie --seed-time=0 wyłącza seedowanie po zakończeniu pobierania.
follow-metalink.name=Śledzenie Metalink
follow-metalink.description=Jeśli ustawione na true lub mem, po pobraniu pliku o rozszerzeniu .meta4, .metalink lub typie zawartości application/metalink4+xml lub application/metalink+xml, aria2 analizuje go jako plik metalink i pobiera pliki w nim wymienione. Jeśli ustawione na mem, plik metalink nie jest zapisywany na dysku, a jedynie przechowywany w pamięci. Jeśli ustawione na false, plik .metalink jest pobierany na dysk, ale nie jest analizowany jako plik metalink, a jego zawartość nie jest pobierana.
metalink-base-uri.name=Bazowy URI
metalink-base-uri.description=Określa bazowy URI do rozwiązywania względnych URI w elementach metalink:url i metalink:metaurl w pliku metalink zapisanym na lokalnym dysku. Jeśli URI wskazuje na katalog, musi kończyć się znakiem /.
metalink-language.name=Język
metalink-language.description=
metalink-location.name=Preferowana Lokalizacja Serwera
metalink-location.description=Lokalizacja preferowanego serwera. Można podać listę lokalizacji oddzielonych przecinkami, np. jp,us.
metalink-os.name=System Operacyjny
metalink-os.description=System operacyjny pliku do pobrania.
metalink-version.name=Wersja
metalink-version.description=Wersja pliku do pobrania.
metalink-preferred-protocol.name=Preferowany Protokół
metalink-preferred-protocol.description=Określa preferowany protokół. Możliwe wartości to http, https, ftp i none. Ustawienie none wyłącza tę funkcję.
metalink-enable-unique-protocol.name=Włącz Unikalny Protokół
metalink-enable-unique-protocol.description=Jeśli ustawione na true i dla danego mirrora w pliku metalink dostępnych jest kilka protokołów, aria2 używa jednego z nich. Aby określić preferowany protokół, użyj opcji --metalink-preferred-protocol.
enable-rpc.name=Włącz Serwer JSON-RPC/XML-RPC
enable-rpc.description=
pause-metadata.name=Wstrzymaj Po Pobieraniu Metadanych
pause-metadata.description=Wstrzymuje pobrania utworzone w wyniku pobierania metadanych. Istnieją 3 typy pobierania metadanych w aria2: (1) pobieranie pliku .torrent, (2) pobieranie metadanych torrenta za pomocą linku magnetycznego, (3) pobieranie pliku metalink. Pobrania utworzone na podstawie tych metadanych zostaną wstrzymane. Opcja działa tylko wtedy, gdy ustawiono --enable-rpc=true.
rpc-allow-origin-all.name=Zezwól na Żądania ze Wszystkich Źródeł
rpc-allow-origin-all.description=Dodaje nagłówek Access-Control-Allow-Origin z wartością * do odpowiedzi RPC.
rpc-listen-all.name=Nasłuchuj na Wszystkich Interfejsach Sieciowych
rpc-listen-all.description=Nasłuchuje przychodzących żądań JSON-RPC/XML-RPC na wszystkich interfejsach sieciowych. Jeśli ustawione na false, nasłuchuje tylko na lokalnym interfejsie pętli zwrotnej.
rpc-listen-port.name=Port Nasłuchu
rpc-listen-port.description=
rpc-max-request-size.name=Maksymalny Rozmiar Żądania
rpc-max-request-size.description=Ustawia maksymalny rozmiar żądania JSON-RPC/XML-RPC. Jeśli aria2 wykryje, że żądanie przekracza SIZE bajtów, połączenie zostanie odrzucone.
rpc-save-upload-metadata.name=Zapisz Metadane Przesyłania
rpc-save-upload-metadata.description=Zapisuje przesłane metadane torrenta lub metalink w katalogu określonym przez opcję --dir. Nazwa pliku składa się z szesnastkowego skrótu SHA-1 metadanych plus rozszerzenie. Dla torrenta rozszerzenie to '.torrent', a dla metalink '.meta4'. Jeśli ustawione na false, pobrania dodane przez aria2.addTorrent() lub aria2.addMetalink() nie zostaną zapisane przez opcję --save-session.
rpc-secure.name=Włącz SSL/TLS
rpc-secure.description=Transport RPC będzie szyfrowany za pomocą SSL/TLS. Klienci RPC muszą używać schematu https do dostępu do serwera. W przypadku klienta WebSocket użyj schematu wss. Aby określić certyfikat serwera i klucz prywatny, użyj opcji --rpc-certificate i --rpc-private-key.
allow-overwrite.name=Zezwól na Nadpisywanie
allow-overwrite.description=Rozpocznij pobieranie od początku, jeśli nie istnieje odpowiedni plik kontrolny. Zobacz także opcję --auto-file-renaming.
allow-piece-length-change.name=Zezwól na Zmianę Długości Części
allow-piece-length-change.description=Jeśli ustawione na false, aria2 przerywa pobieranie, gdy długość części różni się od tej w pliku kontrolnym. Jeśli ustawione na true, pobieranie może być kontynuowane, ale część postępu zostanie utracona.
always-resume.name=Zawsze Wznawiaj Pobieranie
always-resume.description=Zawsze wznawiaj pobieranie. Jeśli ustawione na true, aria2 zawsze próbuje wznowić pobieranie i jeśli nie jest to możliwe, przerywa je. Jeśli ustawione na false, gdy wszystkie podane URI nie obsługują wznawiania lub aria2 napotka N URI, które nie obsługują wznawiania (gdzie N to wartość opcji --max-resume-failure-tries), aria2 pobiera plik od początku. Zobacz opcję --max-resume-failure-tries.
async-dns.name=Asynchroniczny DNS
async-dns.description=
auto-file-renaming.name=Automatyczna Zmiana Nazwy Pliku
auto-file-renaming.description=Zmienia nazwę pliku, jeśli plik o tej samej nazwie już istnieje. Opcja działa tylko w pobieraniu HTTP(S)/FTP. Nowa nazwa pliku ma dodaną kropkę i numer (1..9999) przed rozszerzeniem pliku, jeśli istnieje.
auto-save-interval.name=Automatyczny Interwał Zapisu
auto-save-interval.description=Zapisuje plik kontrolny (*.aria2) co SEC sekund. Jeśli ustawione na 0, plik kontrolny nie jest zapisywany podczas pobierania. aria2 zapisuje plik kontrolny po zatrzymaniu, niezależnie od tej wartości. Możliwe wartości to od 0 do 600.
conditional-get.name=Pobieranie warunkowe
conditional-get.description=Pobieraj plik tylko wtedy, gdy lokalny plik jest starszy niż zdalny. Funkcja ta działa tylko w przypadku pobierania przez HTTP(S). Nie działa, jeśli rozmiar pliku jest określony w Metalink. Ignoruje również nagłówek Content-Disposition. Jeśli istnieje plik kontrolny, ta opcja zostanie zignorowana. Funkcja ta używa nagłówka If-Modified-Since, aby warunkowo pobierać nowszy plik. Przy pobieraniu czasu modyfikacji lokalnego pliku używa nazwy pliku podanej przez użytkownika (patrz opcja --out) lub części nazwy pliku w URI, jeśli --out nie jest określona. Aby nadpisać istniejący plik, wymagane jest --allow-overwrite.
conf-path.name=Plik konfiguracyjny
conf-path.description=
console-log-level.name=Poziom logów konsoli
console-log-level.description=
content-disposition-default-utf8.name=Użyj UTF-8 do obsługi Content-Disposition
content-disposition-default-utf8.description=Obsługuj cytowane ciągi w nagłówku Content-Disposition jako UTF-8 zamiast ISO-8859-1, na przykład parametr nazwy pliku, ale nie rozszerzoną wersję nazwy pliku.
daemon.name=Włącz Daemon
daemon.description=
deferred-input.name=Odwleczone ładowanie
deferred-input.description=Jeśli podano wartość true, aria2 nie odczytuje wszystkich URI i opcji z pliku określonego przez opcję --input-file przy uruchamianiu, lecz odczytuje je jeden po drugim, gdy są potrzebne później. Może to zmniejszyć zużycie pamięci, jeśli plik wejściowy zawiera wiele URI do pobrania. Jeśli podano false, aria2 odczytuje wszystkie URI i opcje przy uruchamianiu. Opcja --deferred-input zostanie wyłączona, gdy używana jest opcja --save-session.
disable-ipv6.name=Wyłącz IPv6
disable-ipv6.description=
disk-cache.name=Cache dyskowy
disk-cache.description=Włącz cache dyskowy. Jeśli rozmiar SIZE wynosi 0, cache dyskowy jest wyłączony. Ta funkcja buforuje pobrane dane w pamięci, która rośnie do maksymalnego rozmiaru SIZE w bajtach. Przechowywanie cache jest tworzone dla instancji aria2 i dzielone przez wszystkie pobierania. Zaletą cache dyskowego jest zmniejszenie operacji I/O na dysku, ponieważ dane są zapisywane w większych jednostkach i są reorganizowane według przesunięcia pliku. Jeśli weryfikacja haszy jest zaangażowana, a dane są buforowane w pamięci, nie musimy ich ponownie odczytywać z dysku. SIZE może zawierać K lub M (1K = 1024, 1M = 1024K).
download-result.name=Wynik pobierania
download-result.description=Ta opcja zmienia sposób formatowania wyników pobierania. Jeśli OPT to domyślnie, wyświetlane będą GID, status, średnia prędkość pobierania oraz ścieżka/URI. Jeśli zaangażowane są wiele plików, wyświetlana jest ścieżka/URI pierwszego żądanego pliku, a pozostałe są pomijane. Jeśli OPT to pełne, wyświetlane będą GID, status, średnia prędkość pobierania, procent postępu oraz ścieżka/URI. Procent postępu i ścieżka/URI są wyświetlane dla każdego żądanego pliku w każdym wierszu. Jeśli OPT to ukryte, wyniki pobierania są ukryte.
dscp.name=DSCP
dscp.description=Ustaw wartość DSCP w wychodzących pakietach IP dla ruchu BitTorrent dla QoS. Ten parametr ustawia tylko bity DSCP w polu TOS pakietów IP, a nie całe pole. Jeśli bierzesz wartości z /usr/include/netinet/ip.h, podziel je przez 4 (w przeciwnym razie wartości będą niepoprawne, np. twój klas CS1 stanie się CS4). Jeśli bierzesz powszechnie używane wartości z RFC, dokumentacji dostawców sieci, Wikipedii lub innych źródeł, użyj ich tak jak są.
rlimit-nofile.name=Miękki limit otwartych deskryptorów plików
rlimit-nofile.description=Ustaw miękki limit otwartych deskryptorów plików. Ta opcja będzie miała wpływ tylko wtedy, gdy: a. System ją obsługuje (posix). b. Limit nie przekracza twardego limitu. c. Określony limit jest większy niż aktualny miękki limit. Jest to równoważne ustawieniu nofile za pomocą ulimit, z tym, że nigdy nie zmniejsza limitu. Ta opcja jest dostępna tylko na systemach wspierających API rlimit.
enable-color.name=Włącz kolor w terminalu
enable-color.description=
enable-mmap.name=Włącz MMap
enable-mmap.description=Mapowanie plików do pamięci. Ta opcja może nie działać, jeśli przestrzeń pliku nie jest wcześniej przydzielona. Zobacz --file-allocation.
event-poll.name=Metoda sondowania zdarzeń
event-poll.description=Określ metodę sondowania zdarzeń. Możliwe wartości to epoll, kqueue, port, poll i select. Każda z tych metod jest dostępna, jeśli system ją obsługuje. epoll jest dostępny w nowszych wersjach Linuksa. kqueue jest dostępny na różnych systemach *BSD, w tym Mac OS X. port jest dostępny na Open Solaris. Domyślna wartość może się różnić w zależności od używanego systemu.
file-allocation.name=Metoda przydzielania plików
file-allocation.description=Określ metodę przydzielania plików. none nie przydziela przestrzeni pliku przed pobieraniem. prealloc przydziela przestrzeń pliku przed rozpoczęciem pobierania. Może to zająć trochę czasu w zależności od rozmiaru pliku. Jeśli używasz nowszych systemów plików, takich jak ext4 (z obsługą rozszerzeń), btrfs, xfs lub NTFS (tylko kompilacja MinGW), falloc jest najlepszym wyborem. Przydziela duże (kilka GiB) pliki niemal natychmiast. Nie używaj falloc w starszych systemach plików, takich jak ext3 i FAT32, ponieważ trwa to prawie tyle samo, co prealloc, a aria2 jest całkowicie zablokowane do czasu zakończenia alokacji. falloc może być niedostępny, jeśli system nie posiada funkcji posix_fallocate(3). trunc używa systemowego wywołania ftruncate(2) lub odpowiednika specyficznego dla platformy, aby przyciąć plik do określonej długości. W przypadku pobierania wielu plików torrent, pliki przylegające do określonych plików są również przydzielane, jeśli dzielą te same kawałki.
force-save.name=Wymuś Zapis
force-save.description=Zapisz pobieranie za pomocą opcji --save-session, nawet jeśli pobieranie zostało zakończone lub usunięte. Opcja ta zapisuje również plik kontrolny w takich sytuacjach. Może to być przydatne do zapisania udostępniania plików w stanie zakończonym.
save-not-found.name=Zapisz Plik, Który Nie Został Znaleziony
save-not-found.description=Zapisz pobieranie za pomocą opcji --save-session, nawet jeśli plik nie został znaleziony na serwerze. Opcja ta zapisuje również plik kontrolny w takich sytuacjach.
hash-check-only.name=Tylko Sprawdzenie Hasz
hash-check-only.description=Jeśli podano "true", po sprawdzeniu haszu przy użyciu opcji --check-integrity, pobieranie zostanie przerwane, niezależnie od tego, czy plik został pobrany w całości.
human-readable.name=Ludzko Czytelny Format Konsoli
human-readable.description=Wyświetl rozmiary i prędkości w formacie przyjaznym dla człowieka (np. 1.2Ki, 3.4Mi) w konsoli.
keep-unfinished-download-result.name=Zachowaj Wyniki Niedokończonych Pobierania
keep-unfinished-download-result.description=Zachowaj wyniki niedokończonych pobrań, nawet jeśli ich liczba przekracza --max-download-result. To przydatne, jeśli chcesz, aby wszystkie niedokończone pobrania zostały zapisane w pliku sesji (zobacz opcję --save-session). Pamiętaj, że nie ma górnej granicy liczby niedokończonych wyników do zapisania. Jeśli jest to niepożądane, wyłącz tę opcję.
max-download-result.name=Maksymalna Liczba Wyników Pobierania
max-download-result.description=Ustaw maksymalną liczbę wyników pobierania przechowywanych w pamięci. Wyniki pobierania obejmują pobrane, błędne i usunięte pliki. Wyniki pobierania są przechowywane w kolejce FIFO i może pomieścić maksymalnie NUM wyników pobierania. Gdy kolejka jest pełna, a nowy wynik pobierania zostaje utworzony, najstarszy wynik zostaje usunięty, a nowy dodany na koniec. Ustawienie dużej liczby w tej opcji może spowodować wysokie zużycie pamięci po tysiącach pobrań. Określenie wartości 0 oznacza, że żadne wyniki pobierania nie będą przechowywane. Należy pamiętać, że niedokończone pobrania są przechowywane w pamięci, niezależnie od tej opcji. Zobacz opcję --keep-unfinished-download-result.
max-mmap-limit.name=Maksymalny Limit MMap
max-mmap-limit.description=Ustaw maksymalny rozmiar pliku, dla którego włączone będzie mmap (zobacz opcję --enable-mmap). Rozmiar pliku jest określany przez sumę wszystkich plików zawartych w jednym pobieraniu. Na przykład, jeśli pobieranie zawiera 5 plików, rozmiar pliku to całkowity rozmiar tych plików. Jeśli rozmiar pliku jest większy niż wartość określona w tej opcji, mmap zostanie wyłączone.
max-resume-failure-tries.name=Maksymalna Liczba Prób Wznawiania
max-resume-failure-tries.description=Gdy używasz opcji --always-resume=false, aria2 pobiera plik od nowa, gdy wykryje N liczbę URI, które nie obsługują wznowienia. Jeśli N wynosi 0, aria2 pobierze plik od nowa, gdy wszystkie URI nie obsługują wznowienia. Zobacz opcję --always-resume.
min-tls-version.name=Minimalna Wersja TLS
min-tls-version.description=Określa minimalną wersję SSL/TLS do włączenia.
log-level.name=Poziom Logów
log-level.description=Określa poziom szczegółowości logów.
optimize-concurrent-downloads.name=Optymalizuj Równoczesne Pobierania
optimize-concurrent-downloads.description=Optymalizuje liczbę równoczesnych pobrań zgodnie z dostępną przepustowością. aria2 wykorzystuje prędkość pobierania z poprzednich pobrań, aby dostosować liczbę pobrań uruchamianych równolegle zgodnie z zasadą N = A + B Log10(prędkość w Mbps). Współczynniki A i B można dostosować, oddzielając je dwukropkiem. Domyślne wartości (A=5, B=25) prowadzą do używania typowo 5 równoczesnych pobrań w sieci 1Mbps i ponad 50 w sieci 100Mbps. Liczba równoczesnych pobrań pozostaje ograniczona maksymalną wartością określoną przez opcję --max-concurrent-downloads.
piece-length.name=Długość Części
piece-length.description=Określ długość części dla pobrań HTTP/FTP. Jest to granica, przy której aria2 dzieli plik. Wszystkie podziały odbywają się w wielokrotnościach tej długości. Ta opcja zostanie zignorowana w pobieraniach BitTorrent. Zostanie również zignorowana, jeśli plik Metalink zawiera sumy kontrolne dla części.
show-console-readout.name=Wyświetl Wyniki w Konsoli
show-console-readout.description=Określ, czy wyniki pobierania mają być wyświetlane w konsoli.
summary-interval.name=Interwał Podsumowania Pobierania
summary-interval.description=Określ interwał w sekundach dla wyświetlania podsumowania postępu pobierania. Ustawienie 0 tłumi wyjście.
max-overall-download-limit.name=Maksymalny Limit Łącznego Pobierania
max-overall-download-limit.description=Ustaw maksymalną prędkość pobierania łączną w bajtach na sekundę. 0 oznacza brak ograniczeń. Możesz dodać K lub M (1K = 1024, 1M = 1024K).
max-download-limit.name=Maksymalny Limit Pobierania
max-download-limit.description=Ustaw maksymalną prędkość pobierania na pojedyncze pobieranie w bajtach na sekundę. 0 oznacza brak ograniczeń. Możesz dodać K lub M (1K = 1024, 1M = 1024K).
no-conf.name=Wyłącz Plik Konfiguracyjny
no-conf.description=Określa, czy plik konfiguracyjny ma być wyłączony.
no-file-allocation-limit.name=Brak Limitów Alokacji Pliku
no-file-allocation-limit.description=Brak alokacji pliku dla plików mniejszych niż SIZE. Możesz dodać K lub M (1K = 1024, 1M = 1024K).
parameterized-uri.name=Włącz Użycie Parametryzowanych URI
parameterized-uri.description=Włącz obsługę parametryzowanych URI. Możesz określić zestaw części: http://{sv1,sv2,sv3}/foo.iso. Możesz także określić numeryczne ciągi z licznikiem kroków: http://host/image[000-100:2].img. Licznik kroków może zostać pominięty. Jeśli wszystkie URI nie wskazują na ten sam plik, jak w drugim przykładzie, wymagane jest użycie opcji -Z.
quiet.name=Wyłącz Wyjście w Konsoli
quiet.description=Określa, czy wyjście w konsoli ma być wyłączone.
realtime-chunk-checksum.name=Walidacja Części Danych w Czasie Rzeczywistym
realtime-chunk-checksum.description=Sprawdzenie części danych przez obliczenie sumy kontrolnej podczas pobierania pliku, jeśli dostępne są sumy kontrolne dla części.
remove-control-file.name=Usuń Plik Kontrolny
remove-control-file.description=Usuń plik kontrolny przed pobraniem. Używając z --allow-overwrite=true, pobieranie zawsze rozpocznie się od nowa. Będzie to przydatne dla użytkowników korzystających z serwerów proxy, które uniemożliwiają wznowienie pobierania.
save-session.name=Plik Zapisanej Sesji
save-session.description=Zapisz błędy/niedokończone pobrania do PLIKU po zakończeniu. Możesz przekazać ten plik do aria2c za pomocą opcji --input-file podczas ponownego uruchomienia. Jeśli chcesz, aby plik wynikowy był zapisany w formacie gzip, dodaj rozszerzenie .gz do nazwy pliku. Należy pamiętać, że pobrania dodane za pomocą metody RPC aria2.addTorrent() i aria2.addMetalink(), których metadane nie mogą zostać zapisane jako plik, nie będą zapisane. Pobrania usunięte za pomocą aria2.remove() i aria2.forceRemove() nie będą zapisane.
save-session-interval.name=Interwał Zapisów Sesji
save-session-interval.description=Zapisz błędy/niedokończone pobrania do pliku określonego przez --save-session co SEC sekund. Ustawienie 0 oznacza, że plik będzie zapisywany tylko przy zakończeniu działania aria2.
socket-recv-buffer-size.name=Rozmiar Bufora Odbioru Gniazda
socket-recv-buffer-size.description=Ustaw maksymalny rozmiar bufora odbioru gniazda w bajtach. Określenie 0 wyłącza tę opcję. Wartość ta będzie ustawiona dla deskryptora gniazda za pomocą opcji SO_RCVBUF.
stop.name=Automatyczne Zakończenie
stop.description=Zakończ działanie programu po upływie SEC sekund. Jeśli 0, ta funkcja jest wyłączona.
truncate-console-readout.name=Ostrzeżenia Konsoli
truncate-console-readout.description=Przytnij wyjście konsoli do jednej linii.
