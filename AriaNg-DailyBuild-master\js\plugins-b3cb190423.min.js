/*! AdminLTE app.js
* ================
* Main JS application file for AdminLTE v2. This file
* should be included in all pages. It controls some layout
* options and implements exclusive AdminLTE plugins.
*
* <AUTHOR>
* @support <https://github.com/ColorlibHQ/AdminLTE/issues>
* @version v2.4.18
* @repository git://github.com/ColorlibHQ/AdminLTE.git
* @license MIT <http://opensource.org/licenses/MIT>
*/
if("undefined"==typeof jQuery)throw new Error("AdminLTE requires jQuery");!function(o){"use strict";var i="lte.boxrefresh",a={source:"",params:{},trigger:".refresh-btn",content:".box-body",loadInContent:!0,responseType:"",overlayTemplate:'<div class="overlay"><div class="fa fa-refresh fa-spin"></div></div>',onLoadStart:function(){},onLoadDone:function(e){return e}},e='[data-widget="box-refresh"]',s=function(e,t){if(this.element=e,this.options=t,this.$overlay=o(t.overlayTemplate),""===t.source)throw new Error("Source url was not defined. Please specify a url in your BoxRefresh source option.");this._setUpListeners(),this.load()};function t(r){return this.each(function(){var e=o(this),t=e.data(i);if(!t){var n=o.extend({},a,e.data(),"object"==typeof r&&r);e.data(i,t=new s(e,n))}if("string"==typeof t){if(void 0===t[r])throw new Error("No method named "+r);t[r]()}})}s.prototype.load=function(){this._addOverlay(),this.options.onLoadStart.call(o(this)),o.get(this.options.source,this.options.params,function(e){this.options.loadInContent&&o(this.element).find(this.options.content).html(e),this.options.onLoadDone.call(o(this),e),this._removeOverlay()}.bind(this),""!==this.options.responseType&&this.options.responseType)},s.prototype._setUpListeners=function(){o(this.element).on("click",this.options.trigger,function(e){e&&e.preventDefault(),this.load()}.bind(this))},s.prototype._addOverlay=function(){o(this.element).append(this.$overlay)},s.prototype._removeOverlay=function(){o(this.$overlay).remove()};var n=o.fn.boxRefresh;o.fn.boxRefresh=t,o.fn.boxRefresh.Constructor=s,o.fn.boxRefresh.noConflict=function(){return o.fn.boxRefresh=n,this},o(window).on("load",function(){o(e).each(function(){t.call(o(this))})})}(jQuery),function(o){"use strict";var i="lte.boxwidget",a={animationSpeed:500,collapseTrigger:'[data-widget="collapse"]',removeTrigger:'[data-widget="remove"]',collapseIcon:"fa-minus",expandIcon:"fa-plus",removeIcon:"fa-times"},e=".box",t=".collapsed-box",s=".box-header",l=".box-body",u=".box-footer",c=".box-tools",d="collapsed-box",f="collapsing.boxwidget",p="collapsed.boxwidget",h="expanding.boxwidget",g="expanded.boxwidget",n="removing.boxwidget",r="removed.boxwidget",m=function(e,t){this.element=e,this.options=t,this._setUpListeners()};function v(r){return this.each(function(){var e=o(this),t=e.data(i);if(!t){var n=o.extend({},a,e.data(),"object"==typeof r&&r);e.data(i,t=new m(e,n))}if("string"==typeof r){if(void 0===t[r])throw new Error("No method named "+r);t[r]()}})}m.prototype.toggle=function(){!o(this.element).is(t)?this.collapse():this.expand()},m.prototype.expand=function(){var e=o.Event(g),t=o.Event(h),n=this.options.collapseIcon,r=this.options.expandIcon;o(this.element).removeClass(d),o(this.element).children(s+", "+l+", "+u).children(c).find("."+r).removeClass(r).addClass(n),o(this.element).children(l+", "+u).slideDown(this.options.animationSpeed,function(){o(this.element).trigger(e)}.bind(this)).trigger(t)},m.prototype.collapse=function(){var e=o.Event(p),t=o.Event(f),n=this.options.collapseIcon,r=this.options.expandIcon;o(this.element).children(s+", "+l+", "+u).children(c).find("."+n).removeClass(n).addClass(r),o(this.element).children(l+", "+u).slideUp(this.options.animationSpeed,function(){o(this.element).addClass(d),o(this.element).trigger(e)}.bind(this)).trigger(t)},m.prototype.remove=function(){var e=o.Event(r),t=o.Event(n);o(this.element).slideUp(this.options.animationSpeed,function(){o(this.element).trigger(e),o(this.element).remove()}.bind(this)).trigger(t)},m.prototype._setUpListeners=function(){var t=this;o(this.element).on("click",this.options.collapseTrigger,function(e){return e&&e.preventDefault(),t.toggle(o(this)),!1}),o(this.element).on("click",this.options.removeTrigger,function(e){return e&&e.preventDefault(),t.remove(o(this)),!1})};var y=o.fn.boxWidget;o.fn.boxWidget=v,o.fn.boxWidget.Constructor=m,o.fn.boxWidget.noConflict=function(){return o.fn.boxWidget=y,this},o(window).on("load",function(){o(e).each(function(){v.call(o(this))})})}(jQuery),function(o){"use strict";var i="lte.controlsidebar",a={controlsidebarSlide:!0},t=".control-sidebar",e='[data-toggle="control-sidebar"]',n=".control-sidebar-open",r=".control-sidebar-bg",s=".wrapper",l=".layout-boxed",u="control-sidebar-open",c="control-sidebar-hold-transition",d="collapsed.controlsidebar",f="expanded.controlsidebar",p=function(e,t){this.element=e,this.options=t,this.hasBindedResize=!1,this.init()};function h(r){return this.each(function(){var e=o(this),t=e.data(i);if(!t){var n=o.extend({},a,e.data(),"object"==typeof r&&r);e.data(i,t=new p(e,n))}"string"==typeof r&&t.toggle()})}p.prototype.init=function(){o(this.element).is(e)||o(this).on("click",this.toggle),this.fix(),o(window).resize(function(){this.fix()}.bind(this))},p.prototype.toggle=function(e){e&&e.preventDefault(),this.fix(),o(t).is(n)||o("body").is(n)?this.collapse():this.expand()},p.prototype.expand=function(){o(t).show(),this.options.controlsidebarSlide?o(t).addClass(u):o("body").addClass(c).addClass(u).delay(50).queue(function(){o("body").removeClass(c),o(this).dequeue()}),o(this.element).trigger(o.Event(f))},p.prototype.collapse=function(){this.options.controlsidebarSlide?o(t).removeClass(u):o("body").addClass(c).removeClass(u).delay(50).queue(function(){o("body").removeClass(c),o(this).dequeue()}),o(t).fadeOut(),o(this.element).trigger(o.Event(d))},p.prototype.fix=function(){o("body").is(l)&&this._fixForBoxed(o(r))},p.prototype._fixForBoxed=function(e){e.css({position:"absolute",height:o(s).height()})};var g=o.fn.controlSidebar;o.fn.controlSidebar=h,o.fn.controlSidebar.Constructor=p,o.fn.controlSidebar.noConflict=function(){return o.fn.controlSidebar=g,this},o(document).on("click",e,function(e){e&&e.preventDefault(),h.call(o(this),"toggle")})}(jQuery),function(r){"use strict";var o="lte.directchat",e='[data-widget="chat-pane-toggle"]',t=".direct-chat",n="direct-chat-contacts-open",i=function(e){this.element=e};function a(n){return this.each(function(){var e=r(this),t=e.data(o);t||e.data(o,t=new i(e)),"string"==typeof n&&t.toggle(e)})}i.prototype.toggle=function(e){e.parents(t).first().toggleClass(n)};var s=r.fn.directChat;r.fn.directChat=a,r.fn.directChat.Constructor=i,r.fn.directChat.noConflict=function(){return r.fn.directChat=s,this},r(document).on("click",e,function(e){e&&e.preventDefault(),a.call(r(this),"toggle")})}(jQuery),function(o){"use strict";var i="lte.pushmenu",a={collapseScreenSize:767,expandOnHover:!1,expandTransitionDelay:200},e=".sidebar-collapse",t=".main-sidebar",n=".content-wrapper",r=".sidebar-form .form-control",s='[data-toggle="push-menu"]',l=".sidebar-mini",u=".sidebar-expanded-on-hover",c=".fixed",d="sidebar-collapse",f="sidebar-open",p="sidebar-expanded-on-hover",h="sidebar-mini-expand-feature",g="expanded.pushMenu",m="collapsed.pushMenu",v=function(e){this.options=e,this.init()};function y(r){return this.each(function(){var e=o(this),t=e.data(i);if(!t){var n=o.extend({},a,e.data(),"object"==typeof r&&r);e.data(i,t=new v(n))}"toggle"===r&&t.toggle()})}v.prototype.init=function(){(this.options.expandOnHover||o("body").is(l+c))&&(this.expandOnHover(),o("body").addClass(h)),o(n).click(function(){o(window).width()<=this.options.collapseScreenSize&&o("body").hasClass(f)&&this.close()}.bind(this)),o(r).click(function(e){e.stopPropagation()})},v.prototype.toggle=function(){var e=o(window).width(),t=!o("body").hasClass(d);e<=this.options.collapseScreenSize&&(t=o("body").hasClass(f)),t?this.close():this.open()},v.prototype.open=function(){o(window).width()>this.options.collapseScreenSize?o("body").removeClass(d).trigger(o.Event(g)):o("body").addClass(f).trigger(o.Event(g))},v.prototype.close=function(){o(window).width()>this.options.collapseScreenSize?o("body").addClass(d).trigger(o.Event(m)):o("body").removeClass(f+" "+d).trigger(o.Event(m))},v.prototype.expandOnHover=function(){o(t).hover(function(){o("body").is(l+e)&&o(window).width()>this.options.collapseScreenSize&&this.expand()}.bind(this),function(){o("body").is(u)&&this.collapse()}.bind(this))},v.prototype.expand=function(){setTimeout(function(){o("body").removeClass(d).addClass(p)},this.options.expandTransitionDelay)},v.prototype.collapse=function(){setTimeout(function(){o("body").removeClass(p).addClass(d)},this.options.expandTransitionDelay)};var b=o.fn.pushMenu;o.fn.pushMenu=y,o.fn.pushMenu.Constructor=v,o.fn.pushMenu.noConflict=function(){return o.fn.pushMenu=b,this},o(document).on("click",s,function(e){e.preventDefault(),y.call(o(this),"toggle")}),o(window).on("load",function(){y.call(o(s))})}(jQuery),function(o){"use strict";var i="lte.todolist",a={onCheck:function(e){return e},onUnCheck:function(e){return e}},t={data:'[data-widget="todo-list"]'},n="done",s=function(e,t){this.element=e,this.options=t,this._setUpListeners()};function e(r){return this.each(function(){var e=o(this),t=e.data(i);if(!t){var n=o.extend({},a,e.data(),"object"==typeof r&&r);e.data(i,t=new s(e,n))}if("string"==typeof t){if(void 0===t[r])throw new Error("No method named "+r);t[r]()}})}s.prototype.toggle=function(e){e.parents(t.li).first().toggleClass(n),e.prop("checked")?this.check(e):this.unCheck(e)},s.prototype.check=function(e){this.options.onCheck.call(e)},s.prototype.unCheck=function(e){this.options.onUnCheck.call(e)},s.prototype._setUpListeners=function(){var e=this;o(this.element).on("change ifChanged","input:checkbox",function(){e.toggle(o(this))})};var r=o.fn.todoList;o.fn.todoList=e,o.fn.todoList.Constructor=s,o.fn.todoList.noConflict=function(){return o.fn.todoList=r,this},o(window).on("load",function(){o(t.data).each(function(){e.call(o(this))})})}(jQuery),function(i){"use strict";var r="lte.tree",o={animationSpeed:500,accordion:!0,followLink:!1,trigger:".treeview a"},a=".treeview",s=".treeview-menu",l=".menu-open, .active",e='[data-widget="tree"]',n=".active",u="menu-open",c="tree",d="collapsed.tree",f="expanded.tree",p=function(e,t){this.element=e,this.options=t,i(this.element).addClass(c),i(a+n,this.element).addClass(u),this._setUpListeners()};function t(n){return this.each(function(){var e=i(this);if(!e.data(r)){var t=i.extend({},o,e.data(),"object"==typeof n&&n);e.data(r,new p(e,t))}})}p.prototype.toggle=function(e,t){var n=e.next(s),r=e.parent(),o=r.hasClass(u);r.is(a)&&(this.options.followLink&&"#"!==e.attr("href")||t.preventDefault(),o?this.collapse(n,r):this.expand(n,r))},p.prototype.expand=function(e,t){var n=i.Event(f);if(this.options.accordion){var r=t.siblings(l),o=r.children(s);this.collapse(o,r)}t.addClass(u),e.stop().slideDown(this.options.animationSpeed,function(){i(this.element).trigger(n),t.height("auto")}.bind(this))},p.prototype.collapse=function(e,t){var n=i.Event(d);t.removeClass(u),e.stop().slideUp(this.options.animationSpeed,function(){i(this.element).trigger(n),t.find(a).removeClass(u).find(s).hide()}.bind(this))},p.prototype._setUpListeners=function(){var t=this;i(this.element).on("click",this.options.trigger,function(e){t.toggle(i(this),e)})};var h=i.fn.tree;i.fn.tree=t,i.fn.tree.Constructor=p,i.fn.tree.noConflict=function(){return i.fn.tree=h,this},i(window).on("load",function(){i(e).each(function(){t.call(i(this))})})}(jQuery),function(s){"use strict";var o="lte.layout",i={slimscroll:!0,resetHeight:!0},l=".wrapper",u=".content-wrapper",c=".layout-boxed",d=".main-footer",f=".main-header",e=".main-sidebar",t="slimScrollDiv",p=".sidebar",h=".control-sidebar",n=".sidebar-menu",r=".main-header .logo",g="fixed",a="hold-transition",m=function(e){this.options=e,this.bindedResize=!1,this.activate()};function v(r){return this.each(function(){var e=s(this),t=e.data(o);if(!t){var n=s.extend({},i,e.data(),"object"==typeof r&&r);e.data(o,t=new m(n))}if("string"==typeof r){if(void 0===t[r])throw new Error("No method named "+r);t[r]()}})}m.prototype.activate=function(){this.fix(),this.fixSidebar(),s("body").removeClass(a),this.options.resetHeight&&s("body, html, "+l).css({height:"auto","min-height":"100%"}),this.bindedResize||(s(window).resize(function(){this.fix(),this.fixSidebar(),s(r+", "+p).one("webkitTransitionEnd otransitionend oTransitionEnd msTransitionEnd transitionend",function(){this.fix(),this.fixSidebar()}.bind(this))}.bind(this)),this.bindedResize=!0),s(n).on("expanded.tree",function(){this.fix(),this.fixSidebar()}.bind(this)),s(n).on("collapsed.tree",function(){this.fix(),this.fixSidebar()}.bind(this))},m.prototype.fix=function(){s(c+" > "+l).css("overflow","hidden");var e=s(d).outerHeight()||0,t=s(f).outerHeight()||0,n=t+e,r=s(window).height(),o=s(p).outerHeight()||0;if(s("body").hasClass(g))s(u).css("min-height",r-e);else{var i;i=o+t<=r?(s(u).css("min-height",r-n),r-n):(s(u).css("min-height",o),o);var a=s(h);void 0!==a&&a.height()>i&&s(u).css("min-height",a.height())}},m.prototype.fixSidebar=function(){s("body").hasClass(g)?this.options.slimscroll&&void 0!==s.fn.slimScroll&&0===s(e).find(t).length&&s(p).slimScroll({height:s(window).height()-s(f).height()+"px"}):void 0!==s.fn.slimScroll&&s(p).slimScroll({destroy:!0}).height("auto")};var y=s.fn.layout;s.fn.layout=v,s.fn.layout.Constuctor=m,s.fn.layout.noConflict=function(){return s.fn.layout=y,this},s(window).on("load",function(){v.call(s("body"))})}(jQuery),
/*! Copyright (c) 2011 Piotr Rochala (http://rocha.la)
 * Dual licensed under the MIT (http://www.opensource.org/licenses/mit-license.php)
 * and GPL (http://www.opensource.org/licenses/gpl-license.php) licenses.
 *
 * Version: 1.3.8
 *
 */
function(S){S.fn.extend({slimScroll:function(w){var C=S.extend({width:"auto",height:"250px",size:"7px",color:"#000",position:"right",distance:"1px",start:"top",opacity:.4,alwaysVisible:!1,disableFadeOut:!1,railVisible:!1,railColor:"#333",railOpacity:.2,railDraggable:!0,railClass:"slimScrollRail",barClass:"slimScrollBar",wrapperClass:"slimScrollDiv",allowPageScroll:!1,wheelStep:20,touchScrollStep:200,borderRadius:"7px",railBorderRadius:"7px"},w);return this.each(function(){function e(e){if(a){var t=0;(e=e||window.event).wheelDelta&&(t=-e.wheelDelta/120),e.detail&&(t=e.detail/3),S(e.target||e.srcTarget||e.srcElement).closest("."+C.wrapperClass).is(g.parent())&&r(t,!0),e.preventDefault&&!h&&e.preventDefault(),h||(e.returnValue=!1)}}function r(e,t,n){h=!1;var r=g.outerHeight()-v.outerHeight();t&&(t=parseInt(v.css("top"))+e*parseInt(C.wheelStep)/100*v.outerHeight(),t=Math.min(Math.max(t,0),r),t=0<e?Math.ceil(t):Math.floor(t),v.css({top:t+"px"})),t=(f=parseInt(v.css("top"))/(g.outerHeight()-v.outerHeight()))*(g[0].scrollHeight-g.outerHeight()),n&&(e=(t=e)/g[0].scrollHeight*g.outerHeight(),e=Math.min(Math.max(e,0),r),v.css({top:e+"px"})),g.scrollTop(t),g.trigger("slimscrolling",~~t),o(),i()}function n(){d=Math.max(g.outerHeight()/g[0].scrollHeight*g.outerHeight(),30),v.css({height:d+"px"});var e=d==g.outerHeight()?"none":"block";v.css({display:e})}function o(){n(),clearTimeout(u),f==~~f?(h=C.allowPageScroll,p!=f&&g.trigger("slimscroll",0==~~f?"top":"bottom")):h=!1,p=f,d>=g.outerHeight()?h=!0:(v.stop(!0,!0).fadeIn("fast"),C.railVisible&&y.stop(!0,!0).fadeIn("fast"))}function i(){C.alwaysVisible||(u=setTimeout(function(){C.disableFadeOut&&a||s||l||(v.fadeOut("slow"),y.fadeOut("slow"))},1e3))}var a,s,l,u,c,d,f,p,h=!1,g=S(this);if(g.parent().hasClass(C.wrapperClass)){var m=g.scrollTop(),v=g.siblings("."+C.barClass),y=g.siblings("."+C.railClass);if(n(),S.isPlainObject(w)){if("height"in w&&"auto"==w.height){g.parent().css("height","auto"),g.css("height","auto");var b=g.parent().parent().height();g.parent().css("height",b),g.css("height",b)}else"height"in w&&(b=w.height,g.parent().css("height",b),g.css("height",b));if("scrollTo"in w)m=parseInt(C.scrollTo);else if("scrollBy"in w)m+=parseInt(C.scrollBy);else if("destroy"in w)return v.remove(),y.remove(),void g.unwrap();r(m,!1,!0)}}else if(!(S.isPlainObject(w)&&"destroy"in w)){C.height="auto"==C.height?g.parent().height():C.height,m=S("<div></div>").addClass(C.wrapperClass).css({position:"relative",overflow:"hidden",width:C.width,height:C.height}),g.css({overflow:"hidden",width:C.width,height:C.height});y=S("<div></div>").addClass(C.railClass).css({width:C.size,height:"100%",position:"absolute",top:0,display:C.alwaysVisible&&C.railVisible?"block":"none","border-radius":C.railBorderRadius,background:C.railColor,opacity:C.railOpacity,zIndex:90}),v=S("<div></div>").addClass(C.barClass).css({background:C.color,width:C.size,position:"absolute",top:0,opacity:C.opacity,display:C.alwaysVisible?"block":"none","border-radius":C.borderRadius,BorderRadius:C.borderRadius,MozBorderRadius:C.borderRadius,WebkitBorderRadius:C.borderRadius,zIndex:99}),b="right"==C.position?{right:C.distance}:{left:C.distance};y.css(b),v.css(b),g.wrap(m),g.parent().append(v),g.parent().append(y),C.railDraggable&&v.bind("mousedown",function(e){var n=S(document);return l=!0,t=parseFloat(v.css("top")),pageY=e.pageY,n.bind("mousemove.slimscroll",function(e){currTop=t+e.pageY-pageY,v.css("top",currTop),r(0,v.position().top,!1)}),n.bind("mouseup.slimscroll",function(e){l=!1,i(),n.unbind(".slimscroll")}),!1}).bind("selectstart.slimscroll",function(e){return e.stopPropagation(),e.preventDefault(),!1}),y.hover(function(){o()},function(){i()}),v.hover(function(){s=!0},function(){s=!1}),g.hover(function(){a=!0,o(),i()},function(){a=!1,i()}),g.bind("touchstart",function(e,t){e.originalEvent.touches.length&&(c=e.originalEvent.touches[0].pageY)}),g.bind("touchmove",function(e){h||e.originalEvent.preventDefault(),e.originalEvent.touches.length&&(r((c-e.originalEvent.touches[0].pageY)/C.touchScrollStep,!0),c=e.originalEvent.touches[0].pageY)}),n(),"bottom"===C.start?(v.css({top:g.outerHeight()-v.outerHeight()}),r(0,!0)):"top"!==C.start&&(r(S(C.start).position().top,null,!0),C.alwaysVisible||v.hide()),window.addEventListener?(this.addEventListener("DOMMouseScroll",e,!1),this.addEventListener("mousewheel",e,!1)):document.attachEvent("onmousewheel",e)}}),this}}),S.fn.extend({slimscroll:S.fn.slimScroll})}(jQuery),function(C,S,x){"use strict";!function i(a,s,l){function u(n,e){if(!s[n]){if(!a[n]){var t="function"==typeof require&&require;if(!e&&t)return t(n,!0);if(c)return c(n,!0);var r=new Error("Cannot find module '"+n+"'");throw r.code="MODULE_NOT_FOUND",r}var o=s[n]={exports:{}};a[n][0].call(o.exports,function(e){var t=a[n][1][e];return u(t||e)},o,o.exports,i,a,s,l)}return s[n].exports}for(var c="function"==typeof require&&require,e=0;e<l.length;e++)u(l[e]);return u}({1:[function(e,t,n){var r=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(n,"__esModule",{value:!0});var d,f,o,p,h=e("./modules/handle-dom"),g=e("./modules/utils"),m=e("./modules/handle-swal-dom"),v=e("./modules/handle-click"),y=r(e("./modules/handle-key")),b=r(e("./modules/default-params")),w=r(e("./modules/set-params"));n.default=o=p=function(){function e(e){var t=n;return t[e]===x?b.default[e]:t[e]}var n=arguments[0];if(h.addClass(S.body,"stop-scrolling"),m.resetInput(),n===x)return g.logStr("SweetAlert expects at least 1 attribute!"),!1;var t=g.extend({},b.default);switch(typeof n){case"string":t.title=n,t.text=arguments[1]||"",t.type=arguments[2]||"";break;case"object":if(n.title===x)return g.logStr('Missing "title" argument!'),!1;for(var r in t.title=n.title,b.default)t[r]=e(r);t.confirmButtonText=t.showCancelButton?"Confirm":b.default.confirmButtonText,t.confirmButtonText=e("confirmButtonText"),t.doneFunction=arguments[1]||null;break;default:return g.logStr('Unexpected type of argument! Expected "string" or "object", got '+typeof n),!1}w.default(t),m.fixVerticalPosition(),m.openModal(arguments[1]);for(var o=m.getModal(),i=o.querySelectorAll("button"),a=["onclick","onmouseover","onmouseout","onmousedown","onmouseup","onfocus"],s=function(e){return v.handleButton(e,t,o)},l=0;l<i.length;l++)for(var u=0;u<a.length;u++){var c=a[u];i[l][c]=s}m.getOverlay().onclick=s,d=C.onkeydown;C.onkeydown=function(e){return y.default(e,t,o)},C.onfocus=function(){setTimeout(function(){f!==x&&(f.focus(),f=x)},0)},p.enableButtons()},o.setDefaults=p.setDefaults=function(e){if(!e)throw new Error("userParams is required");if("object"!=typeof e)throw new Error("userParams has to be a object");g.extend(b.default,e)},o.close=p.close=function(){var t=m.getModal();h.fadeOut(m.getOverlay(),5),h.fadeOut(t,5),h.removeClass(t,"showSweetAlert"),h.addClass(t,"hideSweetAlert"),h.removeClass(t,"visible");var e=t.querySelector(".sa-icon.sa-success");h.removeClass(e,"animate"),h.removeClass(e.querySelector(".sa-tip"),"animateSuccessTip"),h.removeClass(e.querySelector(".sa-long"),"animateSuccessLong");var n=t.querySelector(".sa-icon.sa-error");h.removeClass(n,"animateErrorIcon"),h.removeClass(n.querySelector(".sa-x-mark"),"animateXMark");var r=t.querySelector(".sa-icon.sa-warning");return h.removeClass(r,"pulseWarning"),h.removeClass(r.querySelector(".sa-body"),"pulseWarningIns"),h.removeClass(r.querySelector(".sa-dot"),"pulseWarningIns"),setTimeout(function(){var e=t.getAttribute("data-custom-class");h.removeClass(t,e)},300),h.removeClass(S.body,"stop-scrolling"),C.onkeydown=d,C.previousActiveElement&&C.previousActiveElement.focus(),f=x,clearTimeout(t.timeout),!0},o.showInputError=p.showInputError=function(e){var t=m.getModal(),n=t.querySelector(".sa-input-error");h.addClass(n,"show");var r=t.querySelector(".sa-error-container");h.addClass(r,"show"),r.querySelector("p").innerHTML=e,setTimeout(function(){o.enableButtons()},1),t.querySelector("input").focus()},o.resetInputError=p.resetInputError=function(e){if(e&&13===e.keyCode)return!1;var t=m.getModal(),n=t.querySelector(".sa-input-error");h.removeClass(n,"show");var r=t.querySelector(".sa-error-container");h.removeClass(r,"show")},o.disableButtons=p.disableButtons=function(){var e=m.getModal(),t=e.querySelector("button.confirm"),n=e.querySelector("button.cancel");t.disabled=!0,n.disabled=!0},o.enableButtons=p.enableButtons=function(){var e=m.getModal(),t=e.querySelector("button.confirm"),n=e.querySelector("button.cancel");t.disabled=!1,n.disabled=!1},void 0!==C?C.sweetAlert=C.swal=o:g.logStr("SweetAlert is a frontend module!"),t.exports=n.default},{"./modules/default-params":2,"./modules/handle-click":3,"./modules/handle-dom":4,"./modules/handle-key":5,"./modules/handle-swal-dom":6,"./modules/set-params":8,"./modules/utils":9}],2:[function(e,t,n){Object.defineProperty(n,"__esModule",{value:!0});n.default={title:"",text:"",type:null,allowOutsideClick:!1,showConfirmButton:!0,showCancelButton:!1,closeOnConfirm:!0,closeOnCancel:!0,confirmButtonText:"OK",confirmButtonColor:"#8CD4F5",cancelButtonText:"Cancel",imageUrl:null,imageSize:null,timer:null,customClass:"",html:!1,animation:!0,allowEscapeKey:!0,inputType:"text",inputPlaceholder:"",inputValue:"",showLoaderOnConfirm:!1},t.exports=n.default},{}],3:[function(e,t,n){Object.defineProperty(n,"__esModule",{value:!0});var v=e("./utils"),y=(e("./handle-swal-dom"),e("./handle-dom")),b=function(e,t){var n=!0;y.hasClass(e,"show-input")&&((n=e.querySelector("input").value)||(n="")),t.doneFunction(n),t.closeOnConfirm&&sweetAlert.close(),t.showLoaderOnConfirm&&sweetAlert.disableButtons()},w=function(e,t){var n=String(t.doneFunction).replace(/\s/g,"");"function("===n.substring(0,9)&&")"!==n.substring(9,10)&&t.doneFunction(!1),t.closeOnCancel&&sweetAlert.close()};n.default={handleButton:function(e,t,n){function r(e){u&&t.confirmButtonColor&&(l.style.backgroundColor=e)}var o,i,a,s=e||C.event,l=s.target||s.srcElement,u=-1!==l.className.indexOf("confirm"),c=-1!==l.className.indexOf("sweet-overlay"),d=y.hasClass(n,"visible"),f=t.doneFunction&&"true"===n.getAttribute("data-has-done-function");switch(u&&t.confirmButtonColor&&(o=t.confirmButtonColor,i=v.colorLuminance(o,-.04),a=v.colorLuminance(o,-.14)),s.type){case"mouseover":r(i);break;case"mouseout":r(o);break;case"mousedown":r(a);break;case"mouseup":r(i);break;case"focus":var p=n.querySelector("button.confirm"),h=n.querySelector("button.cancel");u?h.style.boxShadow="none":p.style.boxShadow="none";break;case"click":var g=n===l,m=y.isDescendant(n,l);if(!g&&!m&&d&&!t.allowOutsideClick)break;u&&f&&d?b(n,t):f&&d||c?w(n,t):y.isDescendant(n,l)&&"BUTTON"===l.tagName&&sweetAlert.close()}},handleConfirm:b,handleCancel:w},t.exports=n.default},{"./handle-dom":4,"./handle-swal-dom":6,"./utils":9}],4:[function(e,t,n){Object.defineProperty(n,"__esModule",{value:!0});var r=function(e,t){return new RegExp(" "+t+" ").test(" "+e.className+" ")},o=function(e){e.style.opacity="",e.style.display="block"},i=function(e){e.style.opacity="",e.style.display="none"};n.hasClass=r,n.addClass=function(e,t){r(e,t)||(e.className+=" "+t)},n.removeClass=function(e,t){var n=" "+e.className.replace(/[\t\r\n]/g," ")+" ";if(r(e,t)){for(;0<=n.indexOf(" "+t+" ");)n=n.replace(" "+t+" "," ");e.className=n.replace(/^\s+|\s+$/g,"")}},n.escapeHtml=function(e){var t=S.createElement("div");return t.appendChild(S.createTextNode(e)),t.innerHTML},n._show=o,n.show=function(e){if(e&&!e.length)return o(e);for(var t=0;t<e.length;++t)o(e[t])},n._hide=i,n.hide=function(e){if(e&&!e.length)return i(e);for(var t=0;t<e.length;++t)i(e[t])},n.isDescendant=function(e,t){for(var n=t.parentNode;null!==n;){if(n===e)return!0;n=n.parentNode}return!1},n.getTopMargin=function(e){e.style.left="-9999px",e.style.display="block";var t,n=e.clientHeight;return t="undefined"!=typeof getComputedStyle?parseInt(getComputedStyle(e).getPropertyValue("padding-top"),10):parseInt(e.currentStyle.padding),e.style.left="",e.style.display="none","-"+parseInt((n+t)/2)+"px"},n.fadeIn=function(e,t){if(+e.style.opacity<1){t=t||16,e.style.opacity=0,e.style.display="block";var n=+new Date,r=function(e){function t(){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}(function(){e.style.opacity=+e.style.opacity+(new Date-n)/100,n=+new Date,+e.style.opacity<1&&setTimeout(r,t)});r()}e.style.display="block"},n.fadeOut=function(e,t){t=t||16,e.style.opacity=1;var n=+new Date,r=function(e){function t(){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}(function(){e.style.opacity=+e.style.opacity-(new Date-n)/100,n=+new Date,0<+e.style.opacity?setTimeout(r,t):e.style.display="none"});r()},n.fireClick=function(e){if("function"==typeof MouseEvent){var t=new MouseEvent("click",{view:C,bubbles:!1,cancelable:!0});e.dispatchEvent(t)}else if(S.createEvent){var n=S.createEvent("MouseEvents");n.initEvent("click",!1,!1),e.dispatchEvent(n)}else S.createEventObject?e.fireEvent("onclick"):"function"==typeof e.onclick&&e.onclick()},n.stopEventPropagation=function(e){"function"==typeof e.stopPropagation?(e.stopPropagation(),e.preventDefault()):C.event&&C.event.hasOwnProperty("cancelBubble")&&(C.event.cancelBubble=!0)}},{}],5:[function(e,t,n){Object.defineProperty(n,"__esModule",{value:!0});var d=e("./handle-dom"),f=e("./handle-swal-dom");n.default=function(e,t,n){var r=e||C.event,o=r.keyCode||r.which,i=n.querySelector("button.confirm"),a=n.querySelector("button.cancel"),s=n.querySelectorAll("button[tabindex]");if(-1!==[9,13,32,27].indexOf(o)){for(var l=r.target||r.srcElement,u=-1,c=0;c<s.length;c++)if(l===s[c]){u=c;break}9===o?(l=-1===u?i:u===s.length-1?s[0]:s[u+1],d.stopEventPropagation(r),l.focus(),t.confirmButtonColor&&f.setFocusStyle(l,t.confirmButtonColor)):13===o?("INPUT"===l.tagName&&(l=i).focus(),l=-1===u?i:x):27===o&&!0===t.allowEscapeKey?(l=a,d.fireClick(l,r)):l=x}},t.exports=n.default},{"./handle-dom":4,"./handle-swal-dom":6}],6:[function(e,t,n){var r=function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(n,"__esModule",{value:!0});var o=e("./utils"),i=e("./handle-dom"),a=r(e("./default-params")),s=r(e("./injected-html")),l=function(){var e=S.createElement("div");for(e.innerHTML=s.default;e.firstChild;)S.body.appendChild(e.firstChild)},u=function(e){function t(){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}(function(){var e=S.querySelector(".sweet-alert");return e||(l(),e=u()),e}),c=function(){var e=u();return e?e.querySelector("input"):void 0},d=function(){return S.querySelector(".sweet-overlay")},f=function(e){if(e&&13===e.keyCode)return!1;var t=u(),n=t.querySelector(".sa-input-error");i.removeClass(n,"show");var r=t.querySelector(".sa-error-container");i.removeClass(r,"show")};n.sweetAlertInitialize=l,n.getModal=u,n.getOverlay=d,n.getInput=c,n.setFocusStyle=function(e,t){var n=o.hexToRgb(t);e.style.boxShadow="0 0 2px rgba("+n+", 0.8), inset 0 0 0 1px rgba(0, 0, 0, 0.05)"},n.openModal=function(e){var t=u();i.fadeIn(d(),10),i.show(t),i.addClass(t,"showSweetAlert"),i.removeClass(t,"hideSweetAlert"),C.previousActiveElement=S.activeElement,t.querySelector("button.confirm").focus(),setTimeout(function(){i.addClass(t,"visible")},500);var n=t.getAttribute("data-timer");if("null"!==n&&""!==n){var r=e;t.timeout=setTimeout(function(){r&&"true"===t.getAttribute("data-has-done-function")?r(null):sweetAlert.close()},n)}},n.resetInput=function(){var e=u(),t=c();i.removeClass(e,"show-input"),t.value=a.default.inputValue,t.setAttribute("type",a.default.inputType),t.setAttribute("placeholder",a.default.inputPlaceholder),f()},n.resetInputError=f,n.fixVerticalPosition=function(){u().style.marginTop=i.getTopMargin(u())}},{"./default-params":2,"./handle-dom":4,"./injected-html":7,"./utils":9}],7:[function(e,t,n){Object.defineProperty(n,"__esModule",{value:!0});n.default='<div class="sweet-overlay" tabIndex="-1"></div><div class="sweet-alert"><div class="sa-icon sa-error">\n      <span class="sa-x-mark">\n        <span class="sa-line sa-left"></span>\n        <span class="sa-line sa-right"></span>\n      </span>\n    </div><div class="sa-icon sa-warning">\n      <span class="sa-body"></span>\n      <span class="sa-dot"></span>\n    </div><div class="sa-icon sa-info"></div><div class="sa-icon sa-success">\n      <span class="sa-line sa-tip"></span>\n      <span class="sa-line sa-long"></span>\n\n      <div class="sa-placeholder"></div>\n      <div class="sa-fix"></div>\n    </div><div class="sa-icon sa-custom"></div><h2>Title</h2>\n    <p>Text</p>\n    <fieldset>\n      <input type="text" tabIndex="3" />\n      <div class="sa-input-error"></div>\n    </fieldset><div class="sa-error-container">\n      <div class="icon">!</div>\n      <p>Not valid!</p>\n    </div><div class="sa-button-container">\n      <button class="cancel" tabIndex="2">Cancel</button>\n      <div class="sa-confirm-button-container">\n        <button class="confirm" tabIndex="1">OK</button><div class="la-ball-fall">\n          <div></div>\n          <div></div>\n          <div></div>\n        </div>\n      </div>\n    </div></div>',t.exports=n.default},{}],8:[function(e,t,n){Object.defineProperty(n,"__esModule",{value:!0});var g=e("./utils"),m=e("./handle-swal-dom"),v=e("./handle-dom"),y=["error","warning","info","success","input","prompt"];n.default=function(o){var i=m.getModal(),e=i.querySelector("h2"),t=i.querySelector("p"),n=i.querySelector("button.cancel"),r=i.querySelector("button.confirm");if(e.innerHTML=o.html?o.title:v.escapeHtml(o.title).split("\n").join("<br>"),t.innerHTML=o.html?o.text:v.escapeHtml(o.text||"").split("\n").join("<br>"),o.text&&v.show(t),o.customClass)v.addClass(i,o.customClass),i.setAttribute("data-custom-class",o.customClass);else{var a=i.getAttribute("data-custom-class");v.removeClass(i,a),i.setAttribute("data-custom-class","")}if(v.hide(i.querySelectorAll(".sa-icon")),o.type&&!g.isIE8()){var s=function(){for(var e=!1,t=0;t<y.length;t++)if(o.type===y[t]){e=!0;break}if(!e)return logStr("Unknown alert type: "+o.type),{v:!1};var n=x;-1!==["success","error","warning","info"].indexOf(o.type)&&(n=i.querySelector(".sa-icon.sa-"+o.type),v.show(n));var r=m.getInput();switch(o.type){case"success":v.addClass(n,"animate"),v.addClass(n.querySelector(".sa-tip"),"animateSuccessTip"),v.addClass(n.querySelector(".sa-long"),"animateSuccessLong");break;case"error":v.addClass(n,"animateErrorIcon"),v.addClass(n.querySelector(".sa-x-mark"),"animateXMark");break;case"warning":v.addClass(n,"pulseWarning"),v.addClass(n.querySelector(".sa-body"),"pulseWarningIns"),v.addClass(n.querySelector(".sa-dot"),"pulseWarningIns");break;case"input":case"prompt":r.setAttribute("type",o.inputType),r.value=o.inputValue,r.setAttribute("placeholder",o.inputPlaceholder),v.addClass(i,"show-input"),setTimeout(function(){r.focus(),r.addEventListener("keyup",swal.resetInputError)},400)}}();if("object"==typeof s)return s.v}if(o.imageUrl){var l=i.querySelector(".sa-icon.sa-custom");l.style.backgroundImage="url("+o.imageUrl+")",v.show(l);var u=80,c=80;if(o.imageSize){var d=o.imageSize.toString().split("x"),f=d[0],p=d[1];f&&p?(u=f,c=p):logStr("Parameter imageSize expects value with format WIDTHxHEIGHT, got "+o.imageSize)}l.setAttribute("style",l.getAttribute("style")+"width:"+u+"px; height:"+c+"px")}i.setAttribute("data-has-cancel-button",o.showCancelButton),o.showCancelButton?n.style.display="inline-block":v.hide(n),i.setAttribute("data-has-confirm-button",o.showConfirmButton),o.showConfirmButton?r.style.display="inline-block":v.hide(r),o.cancelButtonText&&(n.innerHTML=v.escapeHtml(o.cancelButtonText)),o.confirmButtonText&&(r.innerHTML=v.escapeHtml(o.confirmButtonText)),o.confirmButtonColor&&(r.style.backgroundColor=o.confirmButtonColor,r.style.borderLeftColor=o.confirmLoadingButtonColor,r.style.borderRightColor=o.confirmLoadingButtonColor,m.setFocusStyle(r,o.confirmButtonColor)),i.setAttribute("data-allow-outside-click",o.allowOutsideClick);var h=!!o.doneFunction;i.setAttribute("data-has-done-function",h),o.animation?"string"==typeof o.animation?i.setAttribute("data-animation",o.animation):i.setAttribute("data-animation","pop"):i.setAttribute("data-animation","none"),i.setAttribute("data-timer",o.timer)},t.exports=n.default},{"./handle-dom":4,"./handle-swal-dom":6,"./utils":9}],9:[function(e,t,n){Object.defineProperty(n,"__esModule",{value:!0});n.extend=function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e},n.hexToRgb=function(e){var t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?parseInt(t[1],16)+", "+parseInt(t[2],16)+", "+parseInt(t[3],16):null},n.isIE8=function(){return C.attachEvent&&!C.addEventListener},n.logStr=function(e){C.console&&C.console.log("SweetAlert: "+e)},n.colorLuminance=function(e,t){(e=String(e).replace(/[^0-9a-f]/gi,"")).length<6&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]),t=t||0;var n,r,o="#";for(r=0;r<3;r++)n=parseInt(e.substr(2*r,2),16),o+=("00"+(n=Math.round(Math.min(Math.max(0,n+n*t),255)).toString(16))).substr(n.length);return o}},{}]},{},[1]),"function"==typeof define&&define.amd?define(function(){return sweetAlert}):"undefined"!=typeof module&&module.exports&&(module.exports=sweetAlert)}(window,document),function(d){"use strict";var e='[data-toggle="context"]',i=function(e,t){this.$element=d(e),this.before=t.before||this.before,this.onItem=t.onItem||this.onItem,this.scopes=t.scopes||null,t.target&&this.$element.data("target",t.target),this.listen()};i.prototype={constructor:i,show:function(e){var t,n,r={relatedTarget:this,target:e.currentTarget};if(!this.isDisabled()&&(this.closemenu(),!1!==this.before.call(this,e,d(e.currentTarget))))return(t=this.getMenu()).trigger(d.Event("show.bs.context",r)),n=this.getPosition(e,t),"li:not(.divider)",t.attr("style","").css(n).addClass("open").on("click.context.data-api","li:not(.divider)",d.proxy(this.onItem,this,d(e.currentTarget))).trigger("shown.bs.context",r),d("html").on("click.context.data-api",t.selector,d.proxy(this.closemenu,this)),!1},closemenu:function(e){var t,n;(t=this.getMenu()).hasClass("open")&&(n={relatedTarget:this},t.trigger(d.Event("hide.bs.context",n)),"li:not(.divider)",t.removeClass("open").off("click.context.data-api","li:not(.divider)").trigger("hidden.bs.context",n),d("html").off("click.context.data-api",t.selector),e&&e.stopPropagation())},keydown:function(e){27==e.which&&this.closemenu(e)},before:function(e){return!0},onItem:function(e){return!0},listen:function(){this.$element.on("contextmenu.context.data-api",this.scopes,d.proxy(this.show,this)),d("html").on("click.context.data-api",d.proxy(this.closemenu,this)),d("html").on("keydown.context.data-api",d.proxy(this.keydown,this))},destroy:function(){this.$element.off(".context.data-api").removeData("context"),d("html").off(".context.data-api")},isDisabled:function(){return this.$element.hasClass("disabled")||this.$element.attr("disabled")},getMenu:function(){var e,t=this.$element.data("target");return t||(t=(t=this.$element.attr("href"))&&t.replace(/.*(?=#[^\s]*$)/,"")),(e=d(t))&&e.length?e:this.$element.find(t)},getPosition:function(e,t){var n,r,o,i=e.clientX,a=e.clientY,s=d(window).width(),l=d(window).height(),u=t.find(".dropdown-menu").outerWidth(),c=t.find(".dropdown-menu").outerHeight();return n=l<a+c?{top:a-c+d(window).scrollTop()}:{top:a+d(window).scrollTop()},r=s<i+u&&0<i-u?{left:i-u+d(window).scrollLeft()}:{left:i+d(window).scrollLeft()},o=t.offsetParent().offset(),r.left=r.left-o.left,n.top=n.top-o.top,d.extend({position:"absolute","z-index":9999},n,r)}},d.fn.contextmenu=function(r,o){return this.each(function(){var e=d(this),t=e.data("context"),n="object"==typeof r&&r;t||e.data("context",t=new i(e,n)),"string"==typeof r&&t[r].call(t,o)})},d.fn.contextmenu.Constructor=i,d(document).on("contextmenu.context.data-api",function(){d(e).each(function(){var e=d(this).data("context");e&&e.closemenu()})}).on("contextmenu.context.data-api",e,function(e){d(this).contextmenu("show",e),e.preventDefault(),e.stopPropagation()})}(jQuery);
/*
 * @version    1.4.0
 * @date       2015-10-26
 * @stability  3 - Stable
 * <AUTHOR> Rooden (https://github.com/litejs/natural-compare-lite)
 * @license    MIT License
 */
var naturalCompare=function(e,t){var r,n,o=1,i=0,a=0,s=String.alphabet;function l(e,t,n){if(n){for(r=t;(n=l(e,r))<76&&65<n;)++r;return+e.slice(t-1,r)}return-1<(n=s&&s.indexOf(e.charAt(t)))?n+76:(n=e.charCodeAt(t)||0)<45||127<n?n:n<46?65:n<48?n-1:n<58?n+18:n<65?n-11:n<91?n+11:n<97?n-37:n<123?n+5:n-63}if((e+="")!=(t+=""))for(;o;)if(n=l(e,i++),o=l(t,a++),n<76&&o<76&&66<n&&66<o&&(n=l(e,i,i),o=l(t,a,i=r),a=r),n!=o)return n<o?-1:1;return 0};try{module.exports=naturalCompare}catch(e){String.naturalCompare=naturalCompare}
/*!
 * angular-translate - v2.19.0 - 2021-09-02
 * 
 * Copyright (c) 2021 The angular-translate team, Pascal Precht; Licensed MIT
 */function Base64(){var u,e;angular.module("utf8-base64",[]).constant("base64",(u={alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",lookup:null,ie:/MSIE /.test(navigator.userAgent),ieo:/MSIE [67]/.test(navigator.userAgent),encode:function(e){var t,n,r,o,i=u.toUtf8(e),a=-1,s=i.length,l=[,,,];if(u.ie){for(t=[];++a<s;)n=i[a],r=i[++a],l[0]=n>>2,l[1]=(3&n)<<4|r>>4,isNaN(r)?l[2]=l[3]=64:(o=i[++a],l[2]=(15&r)<<2|o>>6,l[3]=isNaN(o)?64:63&o),t.push(u.alphabet.charAt(l[0]),u.alphabet.charAt(l[1]),u.alphabet.charAt(l[2]),u.alphabet.charAt(l[3]));return t.join("")}for(t="";++a<s;)n=i[a],r=i[++a],l[0]=n>>2,l[1]=(3&n)<<4|r>>4,isNaN(r)?l[2]=l[3]=64:(o=i[++a],l[2]=(15&r)<<2|o>>6,l[3]=isNaN(o)?64:63&o),t+=u.alphabet[l[0]]+u.alphabet[l[1]]+u.alphabet[l[2]]+u.alphabet[l[3]];return t},decode:function(e){if((e=e.replace(/\s/g,"")).length%4)throw new Error("InvalidLengthError: decode failed: The string to be decoded is not the correct length for a base64 encoded string.");if(/[^A-Za-z0-9+\/=\s]/g.test(e))throw new Error("InvalidCharacterError: decode failed: The string contains characters invalid in a base64 encoded string.");var t,n=u.fromUtf8(e),r=0,o=n.length;if(u.ieo){for(t=[];r<o;)n[r]<128?t.push(String.fromCharCode(n[r++])):191<n[r]&&n[r]<224?t.push(String.fromCharCode((31&n[r++])<<6|63&n[r++])):t.push(String.fromCharCode((15&n[r++])<<12|(63&n[r++])<<6|63&n[r++]));return t.join("")}for(t="";r<o;)n[r]<128?t+=String.fromCharCode(n[r++]):191<n[r]&&n[r]<224?t+=String.fromCharCode((31&n[r++])<<6|63&n[r++]):t+=String.fromCharCode((15&n[r++])<<12|(63&n[r++])<<6|63&n[r++]);return t},toUtf8:function(e){var t,n=-1,r=e.length,o=[];if(/^[\x00-\x7f]*$/.test(e))for(;++n<r;)o.push(e.charCodeAt(n));else for(;++n<r;)(t=e.charCodeAt(n))<128?o.push(t):t<2048?o.push(t>>6|192,63&t|128):o.push(t>>12|224,t>>6&63|128,63&t|128);return o},fromUtf8:function(e){var t,n=-1,r=[],o=[,,,];if(!u.lookup){for(t=u.alphabet.length,u.lookup={};++n<t;)u.lookup[u.alphabet.charAt(n)]=n;n=-1}for(t=e.length;++n<t&&(o[0]=u.lookup[e.charAt(n)],o[1]=u.lookup[e.charAt(++n)],r.push(o[0]<<2|o[1]>>4),o[2]=u.lookup[e.charAt(++n)],64!==o[2])&&(r.push((15&o[1])<<4|o[2]>>2),o[3]=u.lookup[e.charAt(++n)],64!==o[3]);)r.push((3&o[2])<<6|o[3]);return r}},e={decode:function(e){var t=(e=e.replace(/-/g,"+").replace(/_/g,"/")).length%4;if(t){if(1===t)throw new Error("InvalidLengthError: Input base64url string is the wrong length to determine padding");e+=new Array(5-t).join("=")}return u.decode(e)},encode:function(e){return u.encode(e).replace(/\+/g,"-").replace(/\//g,"_").split("=",1)[0]}},{decode:u.decode,encode:u.encode,urldecode:e.decode,urlencode:e.encode}))}!function(e,t){"function"==typeof define&&define.amd?define([],function(){return t()}):"object"==typeof module&&module.exports?module.exports=t():t()}(0,function(){function e(t){"use strict";var n=t.storageKey(),r=t.storage(),e=function(){var e=t.preferredLanguage();angular.isString(e)?t.use(e):r.put(n,t.use())};e.displayName="fallbackFromIncorrectStorageValue",r?r.get(n)?t.use(r.get(n)).catch(e):e():angular.isString(t.preferredLanguage())&&t.use(t.preferredLanguage())}function t(e,o,t,i){"use strict";var L,c,M,P,N,D,_,n,j,R,z,F,U,q,V,H,W={},X=[],G=e,K=[],Y="translate-cloak",Q=!1,Z=!1,J=!0,ee=".",te=!1,ne=!1,re=0,oe=!0,r="default",a={default:function(e){return(e||"").split("-").join("_")},java:function(e){var t=(e||"").split("-").join("_"),n=t.split("_");return 1<n.length?n[0].toLowerCase()+"_"+n[1].toUpperCase():t},bcp47:function(e){var t=(e||"").split("_").join("-"),n=t.split("-");switch(n.length){case 1:n[0]=n[0].toLowerCase();break;case 2:n[0]=n[0].toLowerCase(),4===n[1].length?n[1]=n[1].charAt(0).toUpperCase()+n[1].slice(1).toLowerCase():n[1]=n[1].toUpperCase();break;case 3:n[0]=n[0].toLowerCase(),n[1]=n[1].charAt(0).toUpperCase()+n[1].slice(1).toLowerCase(),n[2]=n[2].toUpperCase();break;default:return t}return n.join("-")},"iso639-1":function(e){return(e||"").split("_").join("-").split("-")[0].toLowerCase()}},s=function(){if(angular.isFunction(i.getLocale))return i.getLocale();var e,t,n=o.$get().navigator,r=["language","browserLanguage","systemLanguage","userLanguage"];if(angular.isArray(n.languages))for(e=0;e<n.languages.length;e++)if((t=n.languages[e])&&t.length)return t;for(e=0;e<r.length;e++)if((t=n[r[e]])&&t.length)return t;return null};s.displayName="angular-translate/service: getFirstBrowserLanguage";var ie=function(){var e=s()||"";return a[r]&&(e=a[r](e)),e};ie.displayName="angular-translate/service: getLocale";var ae=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},se=function(){return this.toString().replace(/^\s+|\s+$/g,"")},d=function(e){return angular.isString(e)?e.toLowerCase():e},le=function(e){if(e){for(var t,n=[],r=d(e),o=0,i=X.length;o<i;o++)n.push(d(X[o]));if(-1<(o=ae(n,r)))return X[o];if(c)for(var a in c)if(c.hasOwnProperty(a)){var s=!1,l=Object.prototype.hasOwnProperty.call(c,a)&&d(a)===d(e);if("*"===a.slice(-1)&&(s=d(a.slice(0,-1))===d(e.slice(0,a.length-1))),(l||s)&&(t=c[a],-1<ae(n,d(t))))return t}var u=e.split("_");return 1<u.length&&-1<ae(n,d(u[0]))?u[0]:void 0}},ue=function(e,t){if(!e&&!t)return W;if(e&&!t){if(angular.isString(e))return W[e]}else angular.isObject(W[e])||(W[e]={}),angular.extend(W[e],ce(t));return this};this.translations=ue,this.cloakClassName=function(e){return e?(Y=e,this):Y},this.allowNamespaces=function(e){return void 0===e?J:(J=e,this)},this.nestedObjectDelimeter=function(e){return e?(ee=e,this):ee};var ce=function(e,t,n,r){var o,i,a;if(t||(t=[]),n||(n={}),!J)return e;for(o in e)Object.prototype.hasOwnProperty.call(e,o)&&(a=e[o],angular.isObject(a)?ce(a,t.concat(o),n,o):(i=t.length?""+t.join(ee)+ee+o:o,t.length&&o===r&&(n[""+t.join(ee)]="@:"+i),n[i]=a));return n};ce.displayName="flatObject",this.addInterpolation=function(e){return K.push(e),this},this.useMessageFormatInterpolation=function(){return this.useInterpolation("$translateMessageFormatInterpolation")},this.useInterpolation=function(e){return R=e,this},this.useSanitizeValueStrategy=function(e){return t.useStrategy(e),this},this.preferredLanguage=function(e){return e?(de(e),this):L};var de=function(e){return e&&(L=e),L};this.translationNotFoundIndicator=function(e){return this.translationNotFoundIndicatorLeft(e),this.translationNotFoundIndicatorRight(e),this},this.translationNotFoundIndicatorLeft=function(e){return e?(U=e,this):U},this.translationNotFoundIndicatorRight=function(e){return e?(q=e,this):q},this.fallbackLanguage=function(e){return fe(e),this};var fe=function(e){return e?(angular.isString(e)?(P=!0,M=[e]):angular.isArray(e)&&(P=!1,M=e),angular.isString(L)&&ae(M,L)<0&&M.push(L),this):P?M[0]:M};this.use=function(e){if(e){if(!W[e]&&!z)throw new Error("$translateProvider couldn't find translationTable for langKey: '"+e+"'");return N=e,this}return N},this.resolveClientLocale=function(){return ie()};var pe=function(e){return e?(G=e,this):n?n+G:G};this.storageKey=pe,this.useUrlLoader=function(e,t){return this.useLoader("$translateUrlLoader",angular.extend({url:e},t))},this.useStaticFilesLoader=function(e){return this.useLoader("$translateStaticFilesLoader",e)},this.useLoader=function(e,t){return z=e,F=t||{},this},this.useLocalStorage=function(){return this.useStorage("$translateLocalStorage")},this.useCookieStorage=function(){return this.useStorage("$translateCookieStorage")},this.useStorage=function(e){return _=e,this},this.storagePrefix=function(e){return e?(n=e,this):e},this.useMissingTranslationHandlerLog=function(){return this.useMissingTranslationHandler("$translateMissingTranslationHandlerLog")},this.useMissingTranslationHandler=function(e){return j=e,this},this.usePostCompiling=function(e){return Q=!!e,this},this.forceAsyncReload=function(e){return Z=!!e,this},this.uniformLanguageTag=function(e){return e?angular.isString(e)&&(e={standard:e}):e={},r=e.standard,this},this.determinePreferredLanguage=function(e){var t=e&&angular.isFunction(e)?e():ie();return L=X.length&&le(t)||t,this},this.registerAvailableLanguageKeys=function(e,t){return e?(X=e,t&&(c=t),this):X},this.useLoaderCache=function(e){return!1===e?V=void 0:!0===e?V=!0:void 0===e?V="$translationCache":e&&(V=e),this},this.directivePriority=function(e){return void 0===e?re:(re=e,this)},this.statefulFilter=function(e){return void 0===e?oe:(oe=e,this)},this.postProcess=function(e){return H=e||void 0,this},this.keepContent=function(e){return ne=!!e,this},this.$get=["$log","$injector","$rootScope","$q",function(e,s,a,v){var i,y,b,w=s.get(R||"$translateDefaultInterpolation"),C=!1,S={},d={},x=function(e,a,s,l,u,c){!N&&L&&(N=L);var r=u&&u!==N?le(u)||u:N;if(u&&m(u),angular.isArray(e))return function(e){for(var r={},t=[],n=function(t){var n=v.defer(),e=function(e){r[t]=e,n.resolve([t,e])};return x(t,a,s,l,u,c).then(e,e),n.promise},o=0,i=e.length;o<i;o++)t.push(n(e[o]));return v.all(t).then(function(){return r})}(e);var t=v.defer();if(!e)throw new TypeError("translationId must be a not empty string");e=se.apply(e);var n=function(){var e=d[r]||d[L];if(y=0,_&&!e){var t=i.get(G);if(e=d[t],M&&M.length){var n=ae(M,t);y=0===n?1:0,ae(M,L)<0&&M.push(L)}}return e}();if(n){var o=function(){u||(r=N),h(e,a,s,l,r,c).then(t.resolve,t.reject)};o.displayName="promiseResolved",n.finally(o).catch(angular.noop)}else h(e,a,s,l,r,c).then(t.resolve,t.reject);return t.promise},T=function(e){return U&&(e=[U,e].join(" ")),q&&(e=[e,q].join(" ")),e},l=function(e){N=e,_&&i.put(x.storageKey(),N),a.$emit("$translateChangeSuccess",{language:e}),w.setLocale(N);var t=function(e,t){S[t].setLocale(N)};t.displayName="eachInterpolatorLocaleSetter",angular.forEach(S,t),a.$emit("$translateChangeEnd",{language:e})},u=function(n){if(!n)throw"No language key specified for loading.";var r=v.defer();a.$emit("$translateLoadingStart",{language:n}),C=!0;var e=V;"string"==typeof e&&(e=s.get(e));var t=angular.extend({},F,{key:n,$http:angular.extend({},{cache:e},F.$http)}),o=function(e){var t={};a.$emit("$translateLoadingSuccess",{language:n}),angular.isArray(e)?angular.forEach(e,function(e){angular.extend(t,ce(e))}):angular.extend(t,ce(e)),C=!1,r.resolve({key:n,table:t}),a.$emit("$translateLoadingEnd",{language:n})};o.displayName="onLoaderSuccess";var i=function(e){a.$emit("$translateLoadingError",{language:e}),r.reject(e),a.$emit("$translateLoadingEnd",{language:e})};return i.displayName="onLoaderError",s.get(z)(t).then(o,i),r.promise};if(_&&(!(i=s.get(_)).get||!i.put))throw new Error("Couldn't use storage '"+_+"', missing get() or put() method!");if(K.length){var t=function(e){var t=s.get(e);t.setLocale(L||N),S[t.getInterpolationIdentifier()]=t};t.displayName="interpolationFactoryAdder",angular.forEach(K,t)}var c=function(r,o,i,a,s){var l=v.defer(),e=function(e){if(Object.prototype.hasOwnProperty.call(e,o)&&null!==e[o]){a.setLocale(r);var t=e[o];if("@:"===t.substr(0,2))c(r,t.substr(2),i,a,s).then(l.resolve,l.reject);else{var n=a.interpolate(e[o],i,"service",s,o);n=E(o,e[o],n,i,r),l.resolve(n)}a.setLocale(N)}else l.reject()};return e.displayName="fallbackTranslationResolver",function(e){var t=v.defer();if(Object.prototype.hasOwnProperty.call(W,e))t.resolve(W[e]);else if(d[e]){var n=function(e){ue(e.key,e.table),t.resolve(e.table)};n.displayName="translationTableResolver",d[e].then(n,t.reject)}else t.reject();return t.promise}(r).then(e,l.reject),l.promise},f=function(e,t,n,r,o){var i,a=W[e];if(a&&Object.prototype.hasOwnProperty.call(a,t)&&null!==a[t]){if(r.setLocale(e),i=r.interpolate(a[t],n,"filter",o,t),i=E(t,a[t],i,n,e,o),!angular.isString(i)&&angular.isFunction(i.$$unwrapTrustedValue)){var s=i.$$unwrapTrustedValue();if("@:"===s.substr(0,2))return f(e,s.substr(2),n,r,o)}else if("@:"===i.substr(0,2))return f(e,i.substr(2),n,r,o);r.setLocale(N)}return i},k=function(e,t,n,r){return j?s.get(j)(e,N,t,n,r):e},$=function(e,t,n,r,o,i){var a=v.defer();if(e<M.length){var s=M[e];c(s,t,n,r,i).then(function(e){a.resolve(e)},function(){return $(e+1,t,n,r,o,i).then(a.resolve,a.reject)})}else if(o)a.resolve(o);else{var l=k(t,n,o);j&&l?a.resolve(l):a.reject(T(t))}return a.promise},p=function(e,t,n,r,o){var i;if(e<M.length){var a=M[e];(i=f(a,t,n,r,o))||""===i||(i=p(e+1,t,n,r))}return i},h=function(e,t,n,r,o,i){var a,s,l,u,c,d=v.defer(),f=o?W[o]:W,p=n?S[n]:w;if(f&&Object.prototype.hasOwnProperty.call(f,e)&&null!==f[e]){var h=f[e];if("@:"===h.substr(0,2))x(h.substr(2),t,n,r,o,i).then(d.resolve,d.reject);else{var g=p.interpolate(h,t,"service",i,e);g=E(e,h,g,t,o),d.resolve(g)}}else{var m;j&&!C&&(m=k(e,t,r)),o&&M&&M.length?(a=e,s=t,l=p,u=r,c=i,$(0<b?b:y,a,s,l,u,c)).then(function(e){d.resolve(e)},function(e){d.reject(T(e))}):j&&!C&&m?r?d.resolve(r):d.resolve(m):r?d.resolve(r):d.reject(T(e))}return d.promise},g=function(e,t,n,r,o){var i,a=r?W[r]:W,s=w;if(S&&Object.prototype.hasOwnProperty.call(S,n)&&(s=S[n]),a&&Object.prototype.hasOwnProperty.call(a,e)&&null!==a[e]){var l=a[e];i="@:"===l.substr(0,2)?g(l.substr(2),t,n,r,o):(i=s.interpolate(l,t,"filter",o,e),E(e,l,i,t,r,o))}else{var u;j&&!C&&(u=k(e,t,o)),i=r&&M&&M.length?p((y=0)<b?b:y,e,t,s,o):j&&!C&&u?u:T(e)}return i},E=function(e,t,n,r,o,i){var a=H;return a&&("string"==typeof a&&(a=s.get(a)),a)?a(e,t,n,r,o,i):n},m=function(e){W[e]||!z||d[e]||(d[e]=u(e).then(function(e){return ue(e.key,e.table),e}))};x.preferredLanguage=function(e){return e&&de(e),L},x.cloakClassName=function(){return Y},x.allowNamespaces=function(){return J},x.nestedObjectDelimeter=function(){return ee},x.fallbackLanguage=function(e){if(null!=e){if(fe(e),z&&M&&M.length)for(var t=0,n=M.length;t<n;t++)d[M[t]]||(d[M[t]]=u(M[t]));x.use(x.use())}return P?M[0]:M},x.useFallbackLanguage=function(e){if(null!=e)if(e){var t=ae(M,e);-1<t&&(b=t)}else b=0},x.proposedLanguage=function(){return D},x.storage=function(){return i},x.negotiateLocale=le,x.use=function(t){if(!t)return N;var n=v.defer();n.promise.then(null,angular.noop),a.$emit("$translateChangeStart",{language:t});var e=le(t);return 0<X.length&&!e?v.reject(t):(e&&(t=e),D=t,!Z&&W[t]||!z||d[t]?d[t]?d[t].then(function(e){return D===e.key&&l(e.key),n.resolve(e.key),e},function(e){return!N&&M&&0<M.length&&M[0]!==e?x.use(M[0]).then(n.resolve,n.reject):n.reject(e)}):(n.resolve(t),l(t)):(d[t]=u(t).then(function(e){return ue(e.key,e.table),n.resolve(e.key),D===t&&l(e.key),e},function(e){return a.$emit("$translateChangeError",{language:e}),n.reject(e),a.$emit("$translateChangeEnd",{language:e}),v.reject(e)}),d[t].finally(function(){var e;D===(e=t)&&(D=void 0),d[e]=void 0}).catch(angular.noop)),n.promise)},x.resolveClientLocale=function(){return ie()},x.storageKey=function(){return pe()},x.isPostCompilingEnabled=function(){return Q},x.isForceAsyncReloadEnabled=function(){return Z},x.isKeepContent=function(){return ne},x.refresh=function(e){if(!z)throw new Error("Couldn't refresh translation table, no loader registered!");a.$emit("$translateRefreshStart",{language:e});var t=v.defer(),n={};function r(t){var e=u(t);return(d[t]=e).then(function(e){W[t]={},ue(t,e.table),n[t]=!0},angular.noop),e}if(t.promise.then(function(){for(var e in W)W.hasOwnProperty(e)&&(e in n||delete W[e]);N&&l(N)},angular.noop).finally(function(){a.$emit("$translateRefreshEnd",{language:e})}),e)W[e]?r(e).then(t.resolve,t.reject):t.reject();else{var o=M&&M.slice()||[];N&&-1===o.indexOf(N)&&o.push(N),v.all(o.map(r)).then(t.resolve,t.reject)}return t.promise},x.instant=function(e,t,n,r,o){var i=r&&r!==N?le(r)||r:N;if(null===e||angular.isUndefined(e))return e;if(r&&m(r),angular.isArray(e)){for(var a={},s=0,l=e.length;s<l;s++)a[e[s]]=x.instant(e[s],t,n,r,o);return a}if(angular.isString(e)&&e.length<1)return e;e&&(e=se.apply(e));var u,c,d=[];L&&d.push(L),i&&d.push(i),M&&M.length&&(d=d.concat(M));for(var f=0,p=d.length;f<p;f++){var h=d[f];if(W[h]&&void 0!==W[h][e]&&(u=g(e,t,n,i,o)),void 0!==u)break}return u||""===u||(U||q?u=T(e):(u=w.interpolate(e,t,"filter",o),j&&!C&&(c=k(e,t,o)),j&&!C&&c&&(u=c))),u},x.versionInfo=function(){return"2.19.0"},x.loaderCache=function(){return V},x.directivePriority=function(){return re},x.statefulFilter=function(){return oe},x.isReady=function(){return te};var n=v.defer();n.promise.then(function(){te=!0}),x.onReady=function(e){var t=v.defer();return angular.isFunction(e)&&t.promise.then(e),te?t.resolve():n.promise.then(t.resolve),t.promise},x.getAvailableLanguageKeys=function(){return 0<X.length?X:null},x.getTranslationTable=function(e){return(e=e||x.use())&&W[e]?angular.copy(W[e]):null};var r=a.$on("$translateReady",function(){n.resolve(),r(),r=null}),o=a.$on("$translateChangeEnd",function(){n.resolve(),o(),o=null});if(z){if(angular.equals(W,{})&&x.use()&&x.use(x.use()),M&&M.length)for(var A=function(e){return ue(e.key,e.table),a.$emit("$translateChangeEnd",{language:e.key}),e},O=0,I=M.length;O<I;O++){var B=M[O];!Z&&W[B]||(d[B]=u(B).then(A))}}else a.$emit("$translateReady",{language:x.use()});return x}]}function n(a,s){"use strict";var e={setLocale:function(e){},getInterpolationIdentifier:function(){return"default"},useSanitizeValueStrategy:function(e){return s.useStrategy(e),this},interpolate:function(e,t,n,r,o){var i;return t=t||{},t=s.sanitize(t,"params",r,n),angular.isNumber(e)?""+e:angular.isString(e)?(i=a(e)(t),s.sanitize(i,"text",r,n)):""}};return e}function r(C,S,x,T,k){"use strict";var $=function(e){return angular.isString(e)?e.toLowerCase():e};return{restrict:"AE",scope:!0,priority:C.directivePriority(),compile:function(e,h){var g=h.translateValues?h.translateValues:void 0,m=h.translateInterpolation?h.translateInterpolation:void 0,v=h.translateSanitizeStrategy?h.translateSanitizeStrategy:void 0,y=e[0].outerHTML.match(/translate-value-+/i),b="^(.*)("+S.startSymbol()+".*"+S.endSymbol()+")(.*)",w="^(.*)"+S.startSymbol()+"(.*)"+S.endSymbol()+"(.*)";return function(o,l,u){o.interpolateParams={},o.preText="",o.postText="",o.translateNamespace=function e(t){return t.translateNamespace?t.translateNamespace:t.$parent?e(t.$parent):void 0}(o);var i={},a=function(e){if(angular.isFunction(a._unwatchOld)&&(a._unwatchOld(),a._unwatchOld=void 0),angular.equals(e,"")||!angular.isDefined(e)){var t=function(){return this.toString().replace(/^\s+|\s+$/g,"")}.apply(l.text()).replace(/\n/g," "),n=t.match(b);if(angular.isArray(n)){o.preText=n[1],o.postText=n[3],i.translate=S(n[2])(o.$parent);var r=t.match(w);angular.isArray(r)&&r[2]&&r[2].length&&(a._unwatchOld=o.$watch(r[2],function(e){i.translate=e,c()}))}else i.translate=t||void 0}else i.translate=e;c()},e=function(t){u.$observe(t,function(e){i[t]=e,c()})};!function(e,t,n){if(t.translateValues&&angular.extend(e,T(t.translateValues)(o.$parent)),y)for(var r in n)Object.prototype.hasOwnProperty.call(t,r)&&"translateValue"===r.substr(0,14)&&"translateValues"!==r&&(e[$(r.substr(14,1))+r.substr(15)]=n[r])}(o.interpolateParams,u,h);var t=!0;for(var n in u.$observe("translate",function(e){void 0===e?a(""):""===e&&t||(i.translate=e,c()),t=!1}),u)u.hasOwnProperty(n)&&"translateAttr"===n.substr(0,13)&&13<n.length&&e(n);if(u.$observe("translateDefault",function(e){o.defaultText=e,c()}),v&&u.$observe("translateSanitizeStrategy",function(e){o.sanitizeStrategy=T(e)(o.$parent),c()}),g&&u.$observe("translateValues",function(e){e&&o.$parent.$watch(function(){angular.extend(o.interpolateParams,T(e)(o.$parent))})}),y){var r=function(n){u.$observe(n,function(e){var t=$(n.substr(14,1))+n.substr(15);o.interpolateParams[t]=e})};for(var s in u)Object.prototype.hasOwnProperty.call(u,s)&&"translateValue"===s.substr(0,14)&&"translateValues"!==s&&r(s)}var c=function(){for(var e in i)i.hasOwnProperty(e)&&void 0!==i[e]&&d(e,i[e],o,o.interpolateParams,o.defaultText,o.translateNamespace)},d=function(t,e,n,r,o,i){e?(i&&"."===e.charAt(0)&&(e=i+e),C(e,r,m,o,n.translateLanguage,n.sanitizeStrategy).then(function(e){f(e,n,!0,t)},function(e){f(e,n,!1,t)})):f(e,n,!1,t)},f=function(e,t,n,r){if(n||void 0!==t.defaultText&&(e=t.defaultText),"translate"===r){(n||!n&&!C.isKeepContent()&&void 0===u.translateKeepContent)&&l.empty().append(t.preText+e+t.postText);var o=C.isPostCompilingEnabled(),i=void 0!==h.translateCompile,a=i&&"false"!==h.translateCompile;(o&&!i||a)&&x(l.contents())(t)}else{var s=u.$attr[r];"data-"===s.substr(0,5)&&(s=s.substr(5)),s=s.substr(15),l.attr(s,e)}};(g||y||u.translateDefault)&&o.$watch("interpolateParams",c,!0),o.$on("translateLanguageChanged",c);var p=k.$on("$translateChangeSuccess",c);l.text().length?u.translate?a(u.translate):a(""):u.translate&&a(u.translate),c(),o.$on("$destroy",p)}}}}function o(u,c){"use strict";return{restrict:"A",priority:u.directivePriority(),link:function(n,r,o){var i,a,s,l={},e=function(){angular.forEach(i,function(e,t){e&&(l[t]=!0,n.translateNamespace&&"."===e.charAt(0)&&(e=n.translateNamespace+e),u(e,a,o.translateInterpolation,void 0,n.translateLanguage,s).then(function(e){r.attr(t,e)},function(e){r.attr(t,e)}))}),angular.forEach(l,function(e,t){i[t]||(r.removeAttr(t),delete l[t])})};d(n,o.translateAttr,function(e){i=e},e),d(n,o.translateValues,function(e){a=e},e),d(n,o.translateSanitizeStrategy,function(e){s=e},e),o.translateValues&&n.$watch(o.translateValues,e,!0),n.$on("translateLanguageChanged",e);var t=c.$on("$translateChangeSuccess",e);e(),n.$on("$destroy",t)}}}function d(e,t,n,r){"use strict";t&&("::"===t.substr(0,2)?t=t.substr(2):e.$watch(t,function(e){n(e),r()},!0),n(e.$eval(t)))}function i(a,s){"use strict";return{compile:function(e){var i=function(e){e.addClass(a.cloakClassName())};return i(e),function(e,t,n){var r=function(e){e.removeClass(a.cloakClassName())}.bind(this,t),o=i.bind(this,t);n.translateCloak&&n.translateCloak.length?(n.$observe("translateCloak",function(e){a(e).then(r,o)}),s.$on("$translateChangeSuccess",function(){a(n.translateCloak).then(r,o)})):a.onReady(r)}}}}function a(){"use strict";return{restrict:"A",scope:!0,compile:function(){return{pre:function(e,t,n){e.translateNamespace=function e(t){return t.translateNamespace?t.translateNamespace:t.$parent?e(t.$parent):void 0}(e),e.translateNamespace&&"."===n.translateNamespace.charAt(0)?e.translateNamespace+=n.translateNamespace:e.translateNamespace=n.translateNamespace}}}}}function s(){"use strict";return{restrict:"A",scope:!0,compile:function(){return function(t,e,n){n.$observe("translateLanguage",function(e){t.translateLanguage=e}),t.$watch("translateLanguage",function(){t.$broadcast("translateLanguageChanged")})}}}}function l(i,a){"use strict";var e=function(e,t,n,r){if(!angular.isObject(t)){var o=this||{__SCOPE_IS_NOT_AVAILABLE:"More info at https://github.com/angular/angular.js/commit/8863b9d04c722b278fa93c5d66ad1e578ad6eb1f"};t=i(t)(o)}return a.instant(e,t,n,r)};return a.statefulFilter()&&(e.$stateful=!0),e}function u(e){"use strict";return e("translations")}return e.$inject=["$translate"],t.$inject=["$STORAGE_KEY","$windowProvider","$translateSanitizationProvider","pascalprechtTranslateOverrider"],n.$inject=["$interpolate","$translateSanitization"],r.$inject=["$translate","$interpolate","$compile","$parse","$rootScope"],o.$inject=["$translate","$rootScope"],i.$inject=["$translate","$rootScope"],l.$inject=["$parse","$translate"],u.$inject=["$cacheFactory"],angular.module("pascalprecht.translate",["ng"]).run(e),e.displayName="runTranslate",angular.module("pascalprecht.translate").provider("$translateSanitization",function(){"use strict";var n,r,f,p=null,h=!1,g=!1;(f={sanitize:function(e,t){return"text"===t&&(e=i(e)),e},escape:function(e,t){return"text"===t&&(e=o(e)),e},sanitizeParameters:function(e,t){return"params"===t&&(e=s(e,i)),e},escapeParameters:function(e,t){return"params"===t&&(e=s(e,o)),e},sce:function(e,t,n){return"text"===t?e=a(e):"params"===t&&"filter"!==n&&(e=s(e,o)),e},sceParameters:function(e,t){return"params"===t&&(e=s(e,a)),e}}).escaped=f.escapeParameters,this.addStrategy=function(e,t){return f[e]=t,this},this.removeStrategy=function(e){return delete f[e],this},this.useStrategy=function(e){return h=!0,p=e,this},this.$get=["$injector","$log",function(u,c){var t,d={};return u.has("$sanitize")&&(n=u.get("$sanitize")),u.has("$sce")&&(r=u.get("$sce")),{useStrategy:(t=this,function(e){t.useStrategy(e)}),sanitize:function(e,t,n,r){if(p||h||g||(c.warn("pascalprecht.translate.$translateSanitization: No sanitization strategy has been configured. This can have serious security implications. See http://angular-translate.github.io/docs/#/guide/19_security for details."),g=!0),n||null===n||(n=p),!n)return e;r||(r="service");var o,i,a,s,l=angular.isArray(n)?n:[n];return o=e,i=t,a=r,s=l,angular.forEach(s,function(t){if(angular.isFunction(t))o=t(o,i,a);else if(angular.isFunction(f[t]))o=f[t](o,i,a);else{if(!angular.isString(f[t]))throw new Error("pascalprecht.translate.$translateSanitization: Unknown sanitization strategy: '"+t+"'");if(!d[f[t]])try{d[f[t]]=u.get(f[t])}catch(e){throw d[f[t]]=function(){},new Error("pascalprecht.translate.$translateSanitization: Unknown sanitization strategy: '"+t+"'")}o=d[f[t]](o,i,a)}}),o}}}];var o=function(e){var t=angular.element("<div></div>");return t.text(e),t.html()},i=function(e){if(!n)throw new Error("pascalprecht.translate.$translateSanitization: Error cannot find $sanitize service. Either include the ngSanitize module (https://docs.angularjs.org/api/ngSanitize) or use a sanitization strategy which does not depend on $sanitize, such as 'escape'.");return n(e)},a=function(e){if(!r)throw new Error("pascalprecht.translate.$translateSanitization: Error cannot find $sce service.");return r.trustAsHtml(e)},s=function(e,n,r){if(angular.isDate(e))return e;if(angular.isObject(e)){var o=angular.isArray(e)?[]:{};if(r){if(-1<r.indexOf(e))throw new Error("pascalprecht.translate.$translateSanitization: Error cannot interpolate parameter due recursive object")}else r=[];return r.push(e),angular.forEach(e,function(e,t){angular.isFunction(e)||(o[t]=s(e,n,r))}),r.splice(-1,1),o}return angular.isNumber(e)?e:!0===e||!1===e?e:angular.isUndefined(e)||null===e?e:n(e)}}),angular.module("pascalprecht.translate").constant("pascalprechtTranslateOverrider",{}).provider("$translate",t),t.displayName="displayName",angular.module("pascalprecht.translate").factory("$translateDefaultInterpolation",n),n.displayName="$translateDefaultInterpolation",angular.module("pascalprecht.translate").constant("$STORAGE_KEY","NG_TRANSLATE_LANG_KEY"),angular.module("pascalprecht.translate").directive("translate",r),r.displayName="translateDirective",angular.module("pascalprecht.translate").directive("translateAttr",o),o.displayName="translateAttrDirective",angular.module("pascalprecht.translate").directive("translateCloak",i),i.displayName="translateCloakDirective",angular.module("pascalprecht.translate").directive("translateNamespace",a),a.displayName="translateNamespaceDirective",angular.module("pascalprecht.translate").directive("translateLanguage",s),s.displayName="translateLanguageDirective",angular.module("pascalprecht.translate").filter("translate",l),l.displayName="translateFilterFactory",angular.module("pascalprecht.translate").factory("$translationCache",u),u.displayName="$translationCache","pascalprecht.translate"}),function(){"use strict";function T(e){return angular.isUndefined(e)||null===e}function e(x,e){if(void 0===e){if("function"!=typeof require)throw new Error("Moment cannot be found by angular-moment! Please reference to: https://github.com/urish/angular-moment");e=function(){try{return require("moment")}catch(e){throw new Error("Please install moment via npm. Please reference to: https://github.com/urish/angular-moment")}}()}return x.module("angularMoment",[]).constant("angularMomentConfig",{preprocess:null,timezone:null,format:null,statefulFilters:!0}).constant("moment",e).constant("amTimeAgoConfig",{withoutSuffix:!1,serverTime:null,titleFormat:null,fullDateThreshold:null,fullDateFormat:null,fullDateThresholdUnit:"day"}).directive("amTimeAgo",["$window","moment","amMoment","amTimeAgoConfig",function(b,w,C,S){return function(e,a,t){function s(){var e;if(l)e=l;else if(S.serverTime){var t=(new Date).getTime()-g+S.serverTime;e=w(t)}else e=w();return e}function n(){u&&(b.clearTimeout(u),u=null)}function r(e){v&&a.attr("datetime",e)}function o(){if(n(),i){var e=C.preprocessDate(i);(function e(t){var n=s().diff(t,h),r=f&&f<=n;if(r?a.text(t.format(p)):a.text(t.from(s(),c)),d&&y&&a.attr("title",t.format(d)),!r){var o=Math.abs(s().diff(t,"minute")),i=3600;o<1?i=1:o<60?i=30:o<180&&(i=300),u=b.setTimeout(function(){e(t)},1e3*i)}})(e),r(e.toISOString())}}var i,l,u=null,c=S.withoutSuffix,d=S.titleFormat,f=S.fullDateThreshold,p=S.fullDateFormat,h=S.fullDateThresholdUnit,g=(new Date).getTime(),m=t.amTimeAgo,v="TIME"===a[0].nodeName.toUpperCase(),y=!a.attr("title");e.$watch(m,function(e){return T(e)||""===e?(n(),void(i&&(a.text(""),r(""),i=null))):(i=e,void o())}),x.isDefined(t.amFrom)&&e.$watch(t.amFrom,function(e){l=T(e)||""===e?null:w(e),o()}),x.isDefined(t.amWithoutSuffix)&&e.$watch(t.amWithoutSuffix,function(e){"boolean"==typeof e?(c=e,o()):c=S.withoutSuffix}),t.$observe("amFullDateThreshold",function(e){f=e,o()}),t.$observe("amFullDateFormat",function(e){p=e,o()}),t.$observe("amFullDateThresholdUnit",function(e){h=e,o()}),e.$on("$destroy",function(){n()}),e.$on("amMoment:localeChanged",function(){o()})}}]).service("amMoment",["moment","$rootScope","$log","angularMomentConfig",function(r,o,t,n){var i=null;this.changeLocale=function(e,t){var n=r.locale(e,t);return x.isDefined(e)&&o.$broadcast("amMoment:localeChanged"),n},this.changeTimezone=function(e){r.tz&&r.tz.setDefault?(r.tz.setDefault(e),o.$broadcast("amMoment:timezoneChanged")):t.warn("angular-moment: changeTimezone() works only with moment-timezone.js v0.3.0 or greater."),n.timezone=e,i=e},this.preprocessDate=function(e){return i!==n.timezone&&this.changeTimezone(n.timezone),n.preprocess?n.preprocess(e):r(!isNaN(parseFloat(e))&&isFinite(e)?parseInt(e,10):e)}}]).filter("amParse",["moment",function(n){return function(e,t){return n(e,t)}}]).filter("amFromUnix",["moment",function(t){return function(e){return t.unix(e)}}]).filter("amUtc",["moment",function(t){return function(e){return t.utc(e)}}]).filter("amUtcOffset",["amMoment",function(n){return function(e,t){return n.preprocessDate(e).utcOffset(t)}}]).filter("amLocal",["moment",function(t){return function(e){return t.isMoment(e)?e.local():null}}]).filter("amTimezone",["amMoment","angularMomentConfig","$log",function(r,e,o){return function(e,t){var n=r.preprocessDate(e);return t?n.tz?n.tz(t):(o.warn("angular-moment: named timezone specified but moment.tz() is undefined. Did you forget to include moment-timezone.js ?"),n):n}}]).filter("amCalendar",["moment","amMoment","angularMomentConfig",function(e,o,t){function n(e,t,n){if(T(e))return"";var r=o.preprocessDate(e);return r.isValid()?r.calendar(t,n):""}return n.$stateful=t.statefulFilters,n}]).filter("amDifference",["moment","amMoment","angularMomentConfig",function(a,s,e){function t(e,t,n,r){if(T(e))return"";var o=s.preprocessDate(e),i=T(t)?a():s.preprocessDate(t);return o.isValid()&&i.isValid()?o.diff(i,n,r):""}return t.$stateful=e.statefulFilters,t}]).filter("amDateFormat",["moment","amMoment","angularMomentConfig",function(e,r,t){function n(e,t){if(T(e))return"";var n=r.preprocessDate(e);return n.isValid()?n.format(t):""}return n.$stateful=t.statefulFilters,n}]).filter("amDurationFormat",["moment","angularMomentConfig",function(r,e){function t(e,t,n){return T(e)?"":r.duration(e,t).humanize(n)}return t.$stateful=e.statefulFilters,t}]).filter("amTimeAgo",["moment","amMoment","angularMomentConfig",function(i,a,e){function t(e,t,n){var r,o;return T(e)?"":(e=a.preprocessDate(e),(r=i(e)).isValid()?(o=i(n),!T(n)&&o.isValid()?r.from(o,t):r.fromNow(t)):"")}return t.$stateful=e.statefulFilters,t}]).filter("amSubtract",["moment","angularMomentConfig",function(r,e){function t(e,t,n){return T(e)?"":r(e).subtract(parseInt(t,10),n)}return t.$stateful=e.statefulFilters,t}]).filter("amAdd",["moment","angularMomentConfig",function(r,e){function t(e,t,n){return T(e)?"":r(e).add(parseInt(t,10),n)}return t.$stateful=e.statefulFilters,t}]).filter("amStartOf",["moment","angularMomentConfig",function(n,e){function t(e,t){return T(e)?"":n(e).startOf(t)}return t.$stateful=e.statefulFilters,t}]).filter("amEndOf",["moment","angularMomentConfig",function(n,e){function t(e,t){return T(e)?"":n(e).endOf(t)}return t.$stateful=e.statefulFilters,t}]),"angularMoment"}var t=window&&window.process&&window.process.type;"function"==typeof define&&define.amd?define(["angular","moment"],e):"undefined"!=typeof module&&module&&module.exports&&"function"==typeof require&&!t?module.exports=e(require("angular"),require("moment")):e(angular,("undefined"!=typeof global&&void 0!==global.moment?global:window).moment)}(),function(e,t){if("function"==typeof define&&define.amd)define(["module","exports","angular","ws"],t);else if("undefined"!=typeof exports)t(module,exports,require("angular"),require("ws"));else{var n={exports:{}};t(n,n.exports,e.angular,e.ws),e.angularWebsocket=n.exports}}(this,function(e,t,n,r){"use strict";function o(r,i,n,a){function o(e,t,n){n||!m(t)||v(t)||(n=t,t=void 0),this.protocols=t,this.url=e||"Missing URL",this.ssl=/(wss)/i.test(this.url),this.scope=n&&n.scope||r,this.rootScopeFailover=n&&n.rootScopeFailover&&!0,this.useApplyAsync=n&&n.useApplyAsync||!1,this.initialTimeout=n&&n.initialTimeout||500,this.maxTimeout=n&&n.maxTimeout||3e5,this.reconnectIfNotNormalClose=n&&n.reconnectIfNotNormalClose||!1,this.binaryType=n&&n.binaryType||"blob",this._reconnectAttempts=0,this.sendQueue=[],this.onOpenCallbacks=[],this.onMessageCallbacks=[],this.onErrorCallbacks=[],this.onCloseCallbacks=[],d(this._readyStateConstants),e?this._connect():this._setInternalState(0)}return o.prototype._readyStateConstants={CONNECTING:0,OPEN:1,CLOSING:2,CLOSED:3,RECONNECT_ABORTED:4},o.prototype._normalCloseCode=1e3,o.prototype._reconnectableStatusCodes=[4e3],o.prototype.safeDigest=function(e){e&&!this.scope.$$phase&&this.scope.$digest()},o.prototype.bindToScope=function(e){var t=this;return e&&(this.scope=e,this.rootScopeFailover&&this.scope.$on("$destroy",function(){t.scope=r})),t},o.prototype._connect=function(e){!e&&this.socket&&this.socket.readyState===this._readyStateConstants.OPEN||(this.socket=a.create(this.url,this.protocols),this.socket.onmessage=l.default.bind(this,this._onMessageHandler),this.socket.onopen=l.default.bind(this,this._onOpenHandler),this.socket.onerror=l.default.bind(this,this._onErrorHandler),this.socket.onclose=l.default.bind(this,this._onCloseHandler),this.socket.binaryType=this.binaryType)},o.prototype.fireQueue=function(){for(;this.sendQueue.length&&this.socket.readyState===this._readyStateConstants.OPEN;){var e=this.sendQueue.shift();this.socket.send(p(e.message)||"blob"!=this.binaryType?e.message:JSON.stringify(e.message)),e.deferred.resolve()}},o.prototype.notifyOpenCallbacks=function(e){for(var t=0;t<this.onOpenCallbacks.length;t++)this.onOpenCallbacks[t].call(this,e)},o.prototype.notifyCloseCallbacks=function(e){for(var t=0;t<this.onCloseCallbacks.length;t++)this.onCloseCallbacks[t].call(this,e)},o.prototype.notifyErrorCallbacks=function(e){for(var t=0;t<this.onErrorCallbacks.length;t++)this.onErrorCallbacks[t].call(this,e)},o.prototype.onOpen=function(e){return this.onOpenCallbacks.push(e),this},o.prototype.onClose=function(e){return this.onCloseCallbacks.push(e),this},o.prototype.onError=function(e){return this.onErrorCallbacks.push(e),this},o.prototype.onMessage=function(e,t){if(!h(e))throw new Error("Callback must be a function");if(t&&g(t.filter)&&!p(t.filter)&&!(t.filter instanceof RegExp))throw new Error("Pattern must be a string or regular expression");return this.onMessageCallbacks.push({fn:e,pattern:t?t.filter:void 0,autoApply:!t||t.autoApply}),this},o.prototype._onOpenHandler=function(e){this._reconnectAttempts=0,this.notifyOpenCallbacks(e),this.fireQueue()},o.prototype._onCloseHandler=function(e){var t=this;t.useApplyAsync?t.scope.$applyAsync(function(){t.notifyCloseCallbacks(e)}):(t.notifyCloseCallbacks(e),t.safeDigest(!0)),(this.reconnectIfNotNormalClose&&e.code!==this._normalCloseCode||-1<this._reconnectableStatusCodes.indexOf(e.code))&&this.reconnect()},o.prototype._onErrorHandler=function(e){var t=this;t.useApplyAsync?t.scope.$applyAsync(function(){t.notifyErrorCallbacks(e)}):(t.notifyErrorCallbacks(e),t.safeDigest(!0))},o.prototype._onMessageHandler=function(e){function t(e,t,n){n=b.call(arguments,2),o.useApplyAsync?o.scope.$applyAsync(function(){e.apply(o,n)}):(e.apply(o,n),o.safeDigest(t))}for(var n,r,o=this,i=0;i<o.onMessageCallbacks.length;i++)(n=(r=o.onMessageCallbacks[i]).pattern)?p(n)&&e.data===n?t(r.fn,r.autoApply,e):n instanceof RegExp&&n.exec(e.data)&&t(r.fn,r.autoApply,e):t(r.fn,r.autoApply,e)},o.prototype.close=function(e){return!e&&this.socket.bufferedAmount||this.socket.close(),this},o.prototype.send=function(t){function r(e){return o.sendQueue.splice(o.sendQueue.indexOf(t),1),n.reject(e),o}var n=i.defer(),o=this,e=function e(t){t.cancel=r;var n=t.then;return t.then=function(){return e(n.apply(this,arguments))},t}(n.promise);return o.readyState===o._readyStateConstants.RECONNECT_ABORTED?n.reject("Socket connection has been closed"):(o.sendQueue.push({message:t,deferred:n}),o.fireQueue()),a.isMocked&&a.isMocked()&&a.isConnected(this.url)&&this._onMessageHandler(a.mockSend()),e},o.prototype.reconnect=function(){this.close();var e=this._getBackoffDelay(++this._reconnectAttempts),t=e/1e3;return console.log("Reconnecting in "+t+" seconds"),n(l.default.bind(this,this._connect),e),this},o.prototype._getBackoffDelay=function(e){var t=Math.random()+1,n=this.initialTimeout,r=e,o=this.maxTimeout;return Math.floor(Math.min(t*n*Math.pow(2,r),o))},o.prototype._setInternalState=function(e){if(Math.floor(e)!==e||e<0||4<e)throw new Error("state must be an integer between 0 and 4, got: "+e);f||(this.readyState=e||this.socket.readyState),this._internalConnectionState=e,y(this.sendQueue,function(e){e.deferred.reject("Message cancelled due to closed socket connection")})},f&&f(o.prototype,"readyState",{get:function(){return this._internalConnectionState||this.socket.readyState},set:function(){throw new Error("The readyState property is read-only")}}),function(e,t,n){return new o(e,t,n)}}function i(n){this.create=function(e,t){if(!/wss?:\/\//.exec(e))throw new Error("Invalid url provided");return t?new a(e,t):new a(e)},this.createWebSocketBackend=function(e,t){return n.warn("Deprecated: Please use .create(url, protocols)"),this.create(e,t)}}Object.defineProperty(t,"__esModule",{value:!0});var a,s,l=(s=n)&&s.__esModule?s:{default:s},u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e};if("object"===(void 0===t?"undefined":u(t))&&"function"==typeof require)try{a=r.Client||r.client||r}catch(e){}a=a||window.WebSocket||window.MozWebSocket;var c=l.default.noop,d=Object.freeze?Object.freeze:c,f=Object.defineProperty,p=l.default.isString,h=l.default.isFunction,g=l.default.isDefined,m=l.default.isObject,v=l.default.isArray,y=l.default.forEach,b=Array.prototype.slice;Array.prototype.indexOf||(Array.prototype.indexOf=function(e){var t=this.length>>>0,n=Number(arguments[1])||0;for((n=n<0?Math.ceil(n):Math.floor(n))<0&&(n+=t);n<t;n++)if(n in this&&this[n]===e)return n;return-1}),l.default.module("ngWebSocket",[]).factory("$websocket",["$rootScope","$q","$timeout","$websocketBackend",o]).factory("WebSocket",["$rootScope","$q","$timeout","WebsocketBackend",o]).service("$websocketBackend",["$log",i]).service("WebSocketBackend",["$log",i]),l.default.module("angular-websocket",["ngWebSocket"]),t.default=l.default.module("ngWebSocket"),e.exports=t.default}),"undefined"!=typeof module&&module.exports?module.exports=new Base64:Base64()
/**
 * An Angular module that gives you access to the browsers local storage
 * @version v0.7.1 - 2017-06-21
 * @link https://github.com/grevory/angular-local-storage
 * <AUTHOR> <<EMAIL>>
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */,function(e,t){var E=t.isDefined,A=t.isUndefined,O=t.isNumber,I=t.isObject,B=t.isArray,L=t.isString,M=t.extend,P=t.toJson;t.module("LocalStorageModule",[]).provider("localStorageService",function(){this.prefix="ls",this.storageType="localStorage",this.cookie={expiry:30,path:"/",secure:!1},this.defaultToCookie=!0,this.notify={setItem:!0,removeItem:!1},this.setPrefix=function(e){return this.prefix=e,this},this.setStorageType=function(e){return this.storageType=e,this},this.setDefaultToCookie=function(e){return this.defaultToCookie=!!e,this},this.setStorageCookie=function(e,t,n){return this.cookie.expiry=e,this.cookie.path=t,this.cookie.secure=n,this},this.setStorageCookieDomain=function(e){return this.cookie.domain=e,this},this.setNotify=function(e,t){return this.notify={setItem:e,removeItem:t},this},this.$get=["$rootScope","$window","$document","$parse","$timeout",function(l,i,u,a,n){function e(e){if(e||(e=i.event),o.setItem&&L(e.key)&&g(e.key)){var t=r(e.key);n(function(){l.$broadcast("LocalStorageModule.notification.changed",{key:t,newvalue:e.newValue,storageType:c.storageType})})}}var s,c=this,d=c.prefix,f=c.cookie,o=c.notify,p=c.storageType;u?u[0]&&(u=u[0]):u=document,"."!==d.substr(-1)&&(d=d?d+".":"");var h=function(e){return d+e},r=function(e){return e.replace(new RegExp("^"+d,"g"),"")},g=function(e){return 0===e.indexOf(d)},t=function(){try{var e=p in i&&null!==i[p],t=h("__"+Math.round(1e7*Math.random()));return e&&((s=i[p]).setItem(t,""),s.removeItem(t)),e}catch(e){return c.defaultToCookie&&(p="cookie"),l.$broadcast("LocalStorageModule.notification.error",e.message),!1}},m=t(),v=function(t,n,e){var r=k();try{if($(e),n=A(n)?null:P(n),!m&&c.defaultToCookie||"cookie"===c.storageType)return m||l.$broadcast("LocalStorageModule.notification.warning","LOCAL_STORAGE_NOT_SUPPORTED"),o.setItem&&l.$broadcast("LocalStorageModule.notification.setitem",{key:t,newvalue:n,storageType:"cookie"}),C(t,n);try{s&&s.setItem(h(t),n),o.setItem&&l.$broadcast("LocalStorageModule.notification.setitem",{key:t,newvalue:n,storageType:c.storageType})}catch(e){return l.$broadcast("LocalStorageModule.notification.error",e.message),C(t,n)}return!0}finally{$(r)}},y=function(e,t){var n=k();try{if($(t),!m&&c.defaultToCookie||"cookie"===c.storageType)return m||l.$broadcast("LocalStorageModule.notification.warning","LOCAL_STORAGE_NOT_SUPPORTED"),S(e);var r=s?s.getItem(h(e)):null;if(!r||"null"===r)return null;try{return JSON.parse(r)}catch(e){return r}}finally{$(n)}},b=function(){var e=k();try{var t,n,r=0;for(1<=arguments.length&&("localStorage"===arguments[arguments.length-1]||"sessionStorage"===arguments[arguments.length-1])&&(r=1,$(arguments[arguments.length-1])),t=0;t<arguments.length-r;t++)if(n=arguments[t],!m&&c.defaultToCookie||"cookie"===c.storageType)m||l.$broadcast("LocalStorageModule.notification.warning","LOCAL_STORAGE_NOT_SUPPORTED"),o.removeItem&&l.$broadcast("LocalStorageModule.notification.removeitem",{key:n,storageType:"cookie"}),x(n);else try{s.removeItem(h(n)),o.removeItem&&l.$broadcast("LocalStorageModule.notification.removeitem",{key:n,storageType:c.storageType})}catch(e){l.$broadcast("LocalStorageModule.notification.error",e.message),x(n)}}finally{$(e)}},w=function(){try{return i.navigator.cookieEnabled||"cookie"in u&&(0<u.cookie.length||-1<(u.cookie="test").indexOf.call(u.cookie,"test"))}catch(e){return l.$broadcast("LocalStorageModule.notification.error",e.message),!1}}(),C=function(e,t,n,r){if(A(t))return!1;if((B(t)||I(t))&&(t=P(t)),!w)return l.$broadcast("LocalStorageModule.notification.error","COOKIES_NOT_SUPPORTED"),!1;try{var o="",i=new Date,a="";if(null===t?(i.setTime(i.getTime()+-864e5),o="; expires="+i.toGMTString(),t=""):O(n)&&0!==n?(i.setTime(i.getTime()+24*n*60*60*1e3),o="; expires="+i.toGMTString()):0!==f.expiry&&(i.setTime(i.getTime()+24*f.expiry*60*60*1e3),o="; expires="+i.toGMTString()),e){var s="; path="+f.path;f.domain&&(a="; domain="+f.domain),"boolean"==typeof r?!0===r&&(a+="; secure"):!0===f.secure&&(a+="; secure"),u.cookie=h(e)+"="+encodeURIComponent(t)+o+s+a}}catch(e){return l.$broadcast("LocalStorageModule.notification.error",e.message),!1}return!0},S=function(e){if(!w)return l.$broadcast("LocalStorageModule.notification.error","COOKIES_NOT_SUPPORTED"),!1;for(var t=u.cookie&&u.cookie.split(";")||[],n=0;n<t.length;n++){for(var r=t[n];" "===r.charAt(0);)r=r.substring(1,r.length);if(0===r.indexOf(h(e)+"=")){var o=decodeURIComponent(r.substring(d.length+e.length+1,r.length));try{var i=JSON.parse(o);return"number"==typeof i?o:i}catch(e){return o}}}return null},x=function(e){C(e,null)},T=function(){for(var e=null,t=d.length,n=u.cookie.split(";"),r=0;r<n.length;r++){for(e=n[r];" "===e.charAt(0);)e=e.substring(1,e.length);var o=e.substring(t,e.indexOf("="));x(o)}},k=function(){return p},$=function(e){return e&&p!==e&&(p=e,m=t()),m};m&&(i.addEventListener?(i.addEventListener("storage",e,!1),l.$on("$destroy",function(){i.removeEventListener("storage",e)})):i.attachEvent&&(i.attachEvent("onstorage",e),l.$on("$destroy",function(){i.detachEvent("onstorage",e)})));return{isSupported:m,getStorageType:k,setStorageType:$,setPrefix:function(e){d=e},set:v,add:v,get:y,keys:function(e){var t=k();try{if($(e),!m)return l.$broadcast("LocalStorageModule.notification.warning","LOCAL_STORAGE_NOT_SUPPORTED"),[];var n=d.length,r=[];for(var o in s)if(o.substr(0,n)===d)try{r.push(o.substr(n))}catch(e){return l.$broadcast("LocalStorageModule.notification.error",e.Description),[]}return r}finally{$(t)}},remove:b,clearAll:function(e,t){var n=k();try{$(t);var r=d?new RegExp("^"+d):new RegExp,o=e?new RegExp(e):new RegExp;if(!m&&c.defaultToCookie||"cookie"===c.storageType)return m||l.$broadcast("LocalStorageModule.notification.warning","LOCAL_STORAGE_NOT_SUPPORTED"),T();if(!m&&!c.defaultToCookie)return!1;var i=d.length;for(var a in s)if(r.test(a)&&o.test(a.substr(i)))try{b(a.substr(i))}catch(e){return l.$broadcast("LocalStorageModule.notification.error",e.message),T()}return!0}finally{$(n)}},bind:function(e,t,n,r,o){var i=y(r=r||t,o);return null===i&&E(n)?i=n:I(i)&&I(n)&&(i=M(i,n)),a(t).assign(e,i),e.$watch(t,function(e){v(r,e,o)},I(e[t]))},deriveKey:h,underiveKey:r,length:function(e){var t=k();try{$(e);for(var n=0,r=i[p],o=0;o<r.length;o++)0===r.key(o).indexOf(d)&&n++;return n}finally{$(t)}},defaultToCookie:this.defaultToCookie,cookie:{isSupported:w,set:C,add:C,get:S,remove:x,clearAll:T}}}]})}(window,window.angular),
/**
 * angular-ui-notification - Angular.js service providing simple notifications using Bootstrap 3 styles with css transitions for animating
 * <AUTHOR>
 * @version v0.3.6
 * @link https://github.com/alexcrack/angular-ui-notification
 * @license MIT
 */
angular.module("ui-notification",[]),angular.module("ui-notification").provider("Notification",function(){this.options={delay:5e3,startTop:10,startRight:10,verticalSpacing:10,horizontalSpacing:10,positionX:"right",positionY:"top",replaceMessage:!1,templateUrl:"angular-ui-notification.html",onClose:void 0,closeOnClick:!0,maxCount:0,container:"body",priority:10},this.setOptions=function(e){if(!angular.isObject(e))throw new Error("Options should be an object!");this.options=angular.extend({},this.options,e)},this.$get=["$timeout","$http","$compile","$templateCache","$rootScope","$injector","$sce","$q","$window",function(u,r,c,o,i,e,h,a,g){var m=this.options,v=m.startTop,y=m.startRight,b=m.verticalSpacing,w=m.horizontalSpacing,s=m.delay,C=[],S=!1,t=function(p,e){function t(e){function t(t){["-webkit-transition","-o-transition","transition"].forEach(function(e){o.css(e,t)})}var n=p.scope.$new();n.message=h.trustAsHtml(p.message),n.title=h.trustAsHtml(p.title),n.t=p.type.substr(0,1),n.delay=p.delay,n.onClose=p.onClose;var d=function(e,t){return e._priority-t._priority},f=function(e,t){return t._priority-e._priority},r=function(){var e=0,t=0,n=y,r=[];"top"===p.positionY?C.sort(d):"bottom"===p.positionY&&C.sort(f);for(var o=C.length-1;0<=o;o--){var i=C[o];if(p.replaceMessage&&o<C.length-1)i.addClass("killed");else{var a=parseInt(i[0].offsetHeight),s=parseInt(i[0].offsetWidth),l=r[i._positionY+i._positionX];u+a>window.innerHeight&&(l=v,t++,e=0);var u=l?0===e?l:l+b:v,c=n+t*(w+s);i.css(i._positionY,u+"px"),"center"==i._positionX?i.css("left",parseInt(window.innerWidth/2-s/2)+"px"):i.css(i._positionX,c+"px"),r[i._positionY+i._positionX]=u+a,0<m.maxCount&&C.length>m.maxCount&&0===o&&i.scope().kill(!0),e++}}},o=c(e)(n);o._positionY=p.positionY,o._positionX=p.positionX,o._priority=p.priority,o.addClass(p.type);var i=function(e){("click"===(e=e.originalEvent||e).type||"opacity"===e.propertyName&&1<=e.elapsedTime)&&(n.onClose&&n.$apply(n.onClose(o)),o.remove(),C.splice(C.indexOf(o),1),n.$destroy(),r())};p.closeOnClick&&(o.addClass("clickable"),o.bind("click",i)),o.bind("webkitTransitionEnd oTransitionEnd otransitionend transitionend msTransitionEnd",i),angular.isNumber(p.delay)&&u(function(){o.addClass("killed")},p.delay),t("none"),angular.element(document.querySelector(p.container)).append(o);var a=-(parseInt(o[0].offsetHeight)+50);if(o.css(o._positionY,a+"px"),C.push(o),"center"==p.positionX){var s=parseInt(o[0].offsetWidth);o.css("left",parseInt(window.innerWidth/2-s/2)+"px")}u(function(){t("")}),n._templateElement=o,n.kill=function(e){e?(n.onClose&&n.$apply(n.onClose(n._templateElement)),C.splice(C.indexOf(n._templateElement),1),n._templateElement.remove(),n.$destroy(),u(r)):n._templateElement.addClass("killed")},u(r),S||(angular.element(g).bind("resize",function(e){u(r)}),S=!0),l.resolve(n)}var l=a.defer();"object"==typeof p&&null!==p||(p={message:p}),p.scope=p.scope?p.scope:i,p.template=p.templateUrl?p.templateUrl:m.templateUrl,p.delay=angular.isUndefined(p.delay)?s:p.delay,p.type=e||p.type||m.type||"",p.positionY=p.positionY?p.positionY:m.positionY,p.positionX=p.positionX?p.positionX:m.positionX,p.replaceMessage=p.replaceMessage?p.replaceMessage:m.replaceMessage,p.onClose=p.onClose?p.onClose:m.onClose,p.closeOnClick=null!==p.closeOnClick&&void 0!==p.closeOnClick?p.closeOnClick:m.closeOnClick,p.container=p.container?p.container:m.container,p.priority=p.priority?p.priority:m.priority;var n=o.get(p.template);return n?t(n):r.get(p.template,{cache:!0}).then(function(e){t(e.data)}).catch(function(e){throw new Error("Template ("+p.template+") could not be loaded. "+e)}),l.promise};return t.primary=function(e){return this(e,"primary")},t.error=function(e){return this(e,"error")},t.success=function(e){return this(e,"success")},t.info=function(e){return this(e,"info")},t.warning=function(e){return this(e,"warning")},t.clearAll=function(){angular.forEach(C,function(e){e.addClass("killed")})},t}]}),angular.module("ui-notification").run(["$templateCache",function(e){e.put("angular-ui-notification.html",'<div class="ui-notification"><h3 ng-show="title" ng-bind-html="title"></h3><div class="message" ng-bind-html="message"></div></div>')}]),angular.module("angularBittorrentPeerid",[]),angular.module("angularBittorrentPeerid").run(function(){"function"!=typeof String.prototype.endsWith&&(String.prototype.endsWith=function(e){return this.slice(-e.length)===e}),"function"!=typeof String.prototype.startsWith&&(String.prototype.startsWith=function(e,t){return t=t||0,this.slice(t,t+e.length)===e})}),angular.module("angularBittorrentPeerid").factory("peeridUtils",function(){function i(e){var t=e.charCodeAt(0);return t>="0".charCodeAt(0)&&t<="9".charCodeAt(0)}function a(e){var t=e.toLowerCase().charCodeAt(0);return t>="a".charCodeAt(0)&&t<="z".charCodeAt(0)}function s(e,t){t=t||0;for(var n=""+(255&e);n.length<t;)n="0"+n;return n}return{getUtf8Data:function(e){for(var t=[],n=0;n<e.length;n++){var r=e.charCodeAt(n);r<128?t.push(r):r<2048?t.push(r>>6|192,63&r|128):t.push(r>>12|224,r>>6&63|128,63&r|128)}return t},isAzStyle:function(e){return"-"===e.charAt(0)&&("-"===e.charAt(7)||!!["FG","LH","NE","KT","SP"].includes(e.substring(1,3)))},isShadowStyle:function(e){if("-"!==e.charAt(5))return!1;if(!a(e.charAt(0)))return!1;if(!i(e.charAt(1))&&"-"!==e.charAt(1))return!1;for(var t,n=4;0<n&&"-"===e.charAt(n);n--);for(var r=1;r<=n;r++){var o=e.charAt(r);if("-"===o)return!1;if(null===(i(t=o)||a(t)||"."===t))return!1}return!0},isMainlineStyle:function(e){return"-"===e.charAt(2)&&"-"===e.charAt(7)&&("-"===e.charAt(4)||"-"===e.charAt(5))},isPossibleSpoofClient:function(e){return e.endsWith("UDP0")||e.endsWith("HTTPBT")},decodeNumericValueOfByte:s,getAzStyleVersionNumber:function(e,t){return"function"==typeof t?t(e):null},getShadowStyleVersionNumber:function(e){return null},decodeBitSpiritClient:function(e,t){if("BS"!==e.substring(2,4))return null;var n=""+t[1];return"0"===n&&(n=1),{client:"BitSpirit",version:n}},decodeBitCometClient:function(e,t){var n="";if(e.startsWith("exbc"))n="";else if(e.startsWith("FUTB"))n="(Solidox Mod)";else{if(!e.startsWith("xUTB"))return null;n="(Mod 2)"}var r="LORD"===e.substring(6,10),o=r?"BitLord":"BitComet",i=s(t[4]),a=r&&"0"!==i?1:2;return{client:o+(n?" "+n:""),version:i+"."+s(t[5],a)}},identifyAwkwardClient:function(e,t){var n,r=20;for(n=0;n<20;++n)if(0<t[n]){r=n;break}if(0===r){var o=!0;for(n=0;n<16;++n)if(0===t[n]){o=!1;break}if(o){for(n=16;n<20;++n)if(t[n]!==(t[n%16]^t[15-n%16])){o=!1;break}if(o)return{client:"Shareaza"}}}return 9===r&&3===t[9]&&3===t[10]&&3===t[11]?{client:"I2PSnark"}:12===r&&97===t[12]&&97===t[13]?{client:"Experimental",version:"3.2.1b2"}:12===r&&0===t[12]&&0===t[13]?{client:"Experimental",version:"3.1"}:12===r?{client:"Mainline"}:null}}}),angular.module("angularBittorrentPeerid").provider("bittorrentPeeridService",function(){var l={},u={},c={},r={},d={},f=[],o=function(e){return e[0]+"."+e[1]+"."+e[2]},e=function(e){return isNaN(e[2])?e[0]+"."+e[1]+".1"+"ABCDE".indexOf(e[2]):e[0]+"."+e[1]+"."+e[2]},t=function(e){var t=e[3];return t="B"===t?"Beta":"A"===t?"Alpha":"",e[0]+"."+e[1]+"."+e[2]+" "+t},i=function(e){return e[0]+"."+e[1]+"."+e[2]+"."+e[3]},n=function(e){return e[0]+e[1]+"."+e[2]+e[3]},a=function(e){return e[1]+"."+e[2]+e[3]},s=function(e){return"0"===e[0]&&"0"===e[1]&&"0"===e[2]?"0."+e[3]:"0"===e[0]&&"0"===e[1]?"0."+e[2]+e[3]:e[0]+"."+e[1]+e[2]+("Z"===e[3]||"X"===e[3]?"+":"")},p=function(e){var t="";return"0"===e[0]?t+=e[1]+".":t+=""+e[0]+e[1]+".","0"===e[2]?t+=e[3]:t+=""+e[2]+e[3],t},h=this.addAzStyle=function(e,t,n){n=n||i,l[e]=t,u[t]=n},g=this.addShadowStyle=function(e,t,n){n=n||o,c[e]=t,r[t]=n},m=this.addMainlineStyle=function(e,t){d[e]=t},v=this.addSimpleClient=function(e,t,n,r){"number"!=typeof n&&void 0!==n||(r=n,n=t,t=void 0),f.push({id:n,client:e,version:t,position:r||0})};h("A~","Ares",o),h("AG","Ares",o),h("AN","Ares",i),h("AR","Ares"),h("AV","Avicora"),h("AX","BitPump",n),h("AT","Artemis"),h("AZ","Vuze",i),h("BB","BitBuddy","1.234"),h("BC","BitComet",a),h("BE","BitTorrent SDK"),h("BF","BitFlu","NO_VERSION"),h("BG","BTG",i),h("bk","BitKitten (libtorrent)"),h("BR","BitRocket","1.2(34)"),h("BS","BTSlave"),h("BT","BitTorrent",t),h("BW","BitWombat"),h("BX","BittorrentX"),h("CB","Shareaza Plus"),h("CD","Enhanced CTorrent",n),h("CT","CTorrent","1.2.34"),h("DP","Propogate Data Client"),h("DE","Deluge",e),h("EB","EBit"),h("ES","Electric Sheep",o),h("FC","FileCroc"),h("FG","FlashGet",a),h("FX","Freebox BitTorrent"),h("FT","FoxTorrent/RedSwoosh"),h("GR","GetRight","1.2"),h("GS","GSTorrent"),h("HL","Halite",o),h("HN","Hydranode"),h("KG","KGet"),h("KT","KTorrent","1.2.3=[RD].4"),h("LC","LeechCraft"),h("LH","LH-ABC"),h("LK","linkage",o),h("LP","Lphant",n),h("LT","libtorrent (Rasterbar)","2.33.4"),h("lt","libTorrent (Rakshasa)","2.33.4"),h("LW","LimeWire","NO_VERSION"),h("MO","MonoTorrent"),h("MP","MooPolice",o),h("MR","Miro"),h("MT","MoonlightTorrent"),h("NE","BT Next Evolution",o),h("NX","Net Transport"),h("OS","OneSwarm",i),h("OT","OmegaTorrent"),h("PC","CacheLogic","12.3-4"),h("PT","Popcorn Time"),h("PD","Pando"),h("PE","PeerProject"),h("pX","pHoeniX"),h("qB","qBittorrent",e),h("QD","qqdownload"),h("RT","Retriever"),h("RZ","RezTorrent"),h("S~","Shareaza alpha/beta"),h("SB","SwiftBit"),h("SD","迅雷在线 (Xunlei)"),h("SG","GS Torrent",i),h("SN","ShareNET"),h("SP","BitSpirit",o),h("SS","SwarmScope"),h("ST","SymTorrent","2.34"),h("st","SharkTorrent"),h("SZ","Shareaza"),h("TG","Torrent GO"),h("TN","Torrent.NET"),h("TR","Transmission",s),h("TS","TorrentStorm"),h("TT","TuoTu",o),h("UL","uLeecher!"),h("UE","µTorrent Embedded",t),h("UT","µTorrent",t),h("UM","µTorrent Mac",t),h("UW","µTorrent Web",t),h("WD","WebTorrent Desktop",p),h("WT","Bitlet"),h("WW","WebTorrent",p),h("WY","FireTorrent"),h("VG","哇嘎 (Vagaa)",i),h("XL","迅雷在线 (Xunlei)"),h("XT","XanTorrent"),h("XF","Xfplay",s),h("XX","XTorrent","1.2.34"),h("XC","XTorrent","1.2.34"),h("ZT","ZipTorrent"),h("7T","aTorrent"),h("ZO","Zona",i),h("#@","Invalid PeerID"),g("A","ABC"),g("O","Osprey Permaseed"),g("Q","BTQueue"),g("R","Tribler"),g("S","Shad0w"),g("T","BitTornado"),g("U","UPnP NAT"),m("M","Mainline"),m("Q","Queen Bee"),v("µTorrent","1.7.0 RC","-UT170-"),v("Azureus","1","Azureus"),v("Azureus","*******","Azureus",5),v("Aria","2","-aria2-"),v("BitTorrent Plus!","II","PRC.P---"),v("BitTorrent Plus!","P87.P---"),v("BitTorrent Plus!","S587Plus"),v("BitTyrant (Azureus Mod)","AZ2500BT"),v("Blizzard Downloader","BLZ"),v("BTGetit","BG",10),v("BTugaXP","btuga"),v("BTugaXP","BTuga",5),v("BTugaXP","oernu"),v("Deadman Walking","BTDWV-"),v("Deadman","Deadman Walking-"),v("External Webseed","Ext"),v("G3 Torrent","-G3"),v("GreedBT","2.7.1","271-"),v("Hurricane Electric","arclight"),v("HTTP Seed","-WS"),v("JVtorrent","10-------"),v("Limewire","LIME"),v("Martini Man","martini"),v("Pando","Pando"),v("PeerApp","PEERAPP"),v("SimpleBT","btfans",4),v("Swarmy","a00---0"),v("Swarmy","a02---0"),v("Teeweety","T00---0"),v("TorrentTopia","346-"),v("XanTorrent","DansClient"),v("MediaGet","-MG1"),v("MediaGet","2.1","-MG21"),v("Amazon AWS S3","S3-"),v("BitTorrent DNA","DNA"),v("Opera","OP"),v("Opera","O"),v("Burst!","Mbrst"),v("TurboBT","turbobt"),v("BT Protocol Daemon","btpd"),v("Plus!","Plus"),v("XBT","XBT"),v("BitsOnWheels","-BOW"),v("eXeem","eX"),v("MLdonkey","-ML"),v("Bitlet","BitLet"),v("AllPeers","AP"),v("BTuga Revolution","BTM"),v("Rufus","RS",2),v("BitMagnet","BM",2),v("QVOD","QVOD"),v("Top-BT","TB"),v("Tixati","TIX"),v("folx","-FL"),v("µTorrent Mac","-UM"),v("µTorrent","-UT"),this.$get=["peeridUtils",function(s){return{parseClient:function(e){var t,n,r=s.getUtf8Data(e),o=null;if(s.isPossibleSpoofClient(e))return(o=s.decodeBitSpiritClient(e,r))?o:(o=s.decodeBitCometClient(e,r))?o:{client:"BitSpirit?"};if(s.isAzStyle(e)&&(o=l[e.substring(1,3)])){var i=(t=e,(n=u[o])?s.getAzStyleVersionNumber(t.substring(3,7),n):null);return o.startsWith("ZipTorrent")&&e.startsWith("bLAde",8)?{client:"Unknown [Fake: ZipTorrent]",version:i}:"µTorrent"===o&&"6.0 Beta"===i?{client:"Mainline",version:"6.0 Beta"}:o.startsWith("libTorrent (Rakshasa)")?{client:o+" / rTorrent*",version:i}:{client:o,version:i}}if(s.isShadowStyle(e)&&(o=c[e.substring(0,1)]))return{client:o};if(s.isMainlineStyle(e)&&(o=d[e.substring(0,1)]))return{client:o};if(o=s.decodeBitSpiritClient(e,r))return o;if(o=s.decodeBitCometClient(e,r))return o;var a=function(e){for(var t=0;t<f.length;++t){var n=f[t];if(e.startsWith(n.id,n.position))return n}return null}(e);return a?{client:o=a.client,version:a.version}:(o=s.identifyAwkwardClient(e,r))?o:{client:"unknown"}}}}]}),angular.module("cgBusy",[]),angular.module("cgBusy").factory("_cgBusyTrackerFactory",["$timeout","$q",function(r,o){return function(){var t={promises:[],delayPromise:null,durationPromise:null,delayJustFinished:!1,reset:function(e){t.minDuration=e.minDuration,t.promises=[],angular.forEach(e.promises,function(e){e&&!e.$cgBusyFulfilled&&n(e)}),0!==t.promises.length&&(t.delayJustFinished=!1,e.delay&&(t.delayPromise=r(function(){t.delayPromise=null,t.delayJustFinished=!0},parseInt(e.delay,10))),e.minDuration&&(t.durationPromise=r(function(){t.durationPromise=null},parseInt(e.minDuration,10)+(e.delay?parseInt(e.delay,10):0))))},isPromise:function(e){return void 0!==(e&&(e.then||e.$then||e.$promise&&e.$promise.then))},callThen:function(e,t,n){var r;e.then||e.$then?r=e:e.$promise?r=e.$promise:e.denodeify&&(r=o.when(e)),(r.then||r.$then).call(r,t,n)}},n=function(e){if(!t.isPromise(e))throw new Error("cgBusy expects a promise (or something that has a .promise or .$promise");-1===t.promises.indexOf(e)&&(t.promises.push(e),t.callThen(e,function(){e.$cgBusyFulfilled=!0,-1!==t.promises.indexOf(e)&&t.promises.splice(t.promises.indexOf(e),1)},function(){e.$cgBusyFulfilled=!0,-1!==t.promises.indexOf(e)&&t.promises.splice(t.promises.indexOf(e),1)}))};return t.active=function(){return!t.delayPromise&&(t.delayJustFinished?(t.delayJustFinished=!1,0===t.promises.length&&(t.durationPromise=null),0<t.promises.length):!!t.durationPromise||0<t.promises.length)},t}}]),angular.module("cgBusy").value("cgBusyDefaults",{}),angular.module("cgBusy").directive("cgBusy",["$compile","$templateCache","cgBusyDefaults","$http","_cgBusyTrackerFactory",function(d,f,p,h,g){return{restrict:"A",link:function(e,r,t){var n=r.css("position");("static"===n||""===n||void 0===n)&&r.css("position","relative");var o,i,a,s,l,u=g(),c={templateUrl:"angular-busy.html",delay:0,minDuration:0,backdrop:!0,message:"Please Wait...",wrapperClass:"cg-busy cg-busy-animation"};angular.extend(c,p),e.$watchCollection(t.cgBusy,function(n){if(n||(n={promise:null}),angular.isString(n))throw new Error("Invalid value for cg-busy. cgBusy no longer accepts string ids to represent promises/trackers.");(angular.isArray(n)||u.isPromise(n))&&(n={promise:n}),(n=angular.extend(angular.copy(c),n)).templateUrl||(n.templateUrl=c.templateUrl),angular.isArray(n.promise)||(n.promise=[n.promise]),s||(s=e.$new()),s.$message=n.message,angular.equals(u.promises,n.promise)||u.reset({promises:n.promise,delay:n.delay,minDuration:n.minDuration}),s.$cgBusyIsActive=function(){return u.active()},o&&a===n.templateUrl&&l===n.backdrop||(o&&o.remove(),i&&i.remove(),a=n.templateUrl,l=n.backdrop,h.get(a,{cache:f}).then(function(e){if(n.backdrop=void 0===n.backdrop||n.backdrop,n.backdrop){i=d('<div class="cg-busy cg-busy-backdrop cg-busy-backdrop-animation ng-hide" ng-show="$cgBusyIsActive()"></div>')(s),r.append(i)}var t='<div class="'+n.wrapperClass+' ng-hide" ng-show="$cgBusyIsActive()">'+e.data+"</div>";o=d(t)(s),angular.element(o.children()[0]).css("position","absolute").css("top",0).css("left",0).css("right",0).css("bottom",0),r.append(o)},function(e){throw new Error("Template specified for cgBusy ("+n.templateUrl+") could not be loaded. "+e)}))},!0)}}}]),angular.module("cgBusy").run(["$templateCache",function(e){"use strict";e.put("angular-busy.html",'<div class="cg-busy-default-wrapper">\n\n   <div class="cg-busy-default-sign">\n\n      <div class="cg-busy-default-spinner">\n         <div class="bar1"></div>\n         <div class="bar2"></div>\n         <div class="bar3"></div>\n         <div class="bar4"></div>\n         <div class="bar5"></div>\n         <div class="bar6"></div>\n         <div class="bar7"></div>\n         <div class="bar8"></div>\n         <div class="bar9"></div>\n         <div class="bar10"></div>\n         <div class="bar11"></div>\n         <div class="bar12"></div>\n      </div>\n\n      <div class="cg-busy-default-text">{{$message}}</div>\n\n   </div>\n\n</div>')}]),angular.module("angularPromiseButtons",[]),angular.module("angularPromiseButtons").directive("promiseBtn",["angularPromiseButtons","$parse","$timeout","$compile",function(m,v,y,b){"use strict";return{restrict:"EA",priority:m.config.priority,scope:{promiseBtn:"=",promiseBtnOptions:"=?",ngDisabled:"=?"},link:function(i,a,n){function r(e){h.btnLoadingClass&&!h.addClassToCurrentBtnOnly&&e.addClass(h.btnLoadingClass),h.disableBtn&&!h.disableCurrentBtnOnly&&e.attr("disabled","disabled")}function o(e){h.minDuration&&!f||!p||(h.btnLoadingClass&&e.removeClass(h.btnLoadingClass),h.disableBtn&&!i.ngDisabled&&e.removeAttr("disabled"))}function s(e,t){i.$watch(e,function(e){p=f=!1,h.minDuration&&(d=y(function(){f=!0,o(t)},h.minDuration)),e&&e.then?(r(t),e.finally?e.finally(function(){p=!0,o(t)}):e.then(function(){p=!0,o(t)}).catch(function(){p=!0,o(t)})):e&&e.$promise&&(r(t),e.$promise.finally(function(){p=!0,o(t)}))})}function e(e){e.append(b(h.spinnerTpl)(i))}function t(e){h.addClassToCurrentBtnOnly&&e.on(h.CLICK_EVENT,function(){e.addClass(h.btnLoadingClass)}),h.disableCurrentBtnOnly&&e.on(h.CLICK_EVENT,function(){e.attr("disabled","disabled")})}function l(e,t,r){var o=n[t].split(";").map(function(e){return v(e)});a.off(e),a.on(e,function(n){i.$apply(function(){o.forEach(function(e){var t=e(i.$parent,{$event:n});c||(c=s(function(){return t},r))})})})}function u(e,t,n){0<h.priority?l(e,t,n):i.$evalAsync(function(){l(e,t,n)})}var c,d,f,p,h=m.config;if(n.promiseBtn)e(a),t(a),s(function(){return i.promiseBtn},a);else if(n.hasOwnProperty(h.CLICK_ATTR))e(a),t(a),u(h.CLICK_EVENT,h.CLICK_ATTR,a);else if(n.hasOwnProperty(h.SUBMIT_ATTR)){var g=function(e){for(var t=[],n=a.find(m.config.BTN_SELECTOR),r=0;r<n.length;r++){var o=n[r];"submit"===angular.element(o).attr("type")&&t.push(o)}return angular.element(t)}();e(g),t(g),u(h.SUBMIT_EVENT,h.SUBMIT_ATTR,g)}i.$watch("promiseBtnOptions",function(e){angular.isObject(e)&&(h=angular.extend({},h,e))},!0),i.$on("$destroy",function(){y.cancel(d)})}}}]),angular.module("angularPromiseButtons").provider("angularPromiseButtons",function(){"use strict";var t={spinnerTpl:'<span class="btn-spinner"></span>',priority:10,disableBtn:!0,btnLoadingClass:"is-loading",addClassToCurrentBtnOnly:!1,disableCurrentBtnOnly:!1,minDuration:!1,CLICK_EVENT:"click",CLICK_ATTR:"ngClick",SUBMIT_EVENT:"submit",SUBMIT_ATTR:"ngSubmit",BTN_SELECTOR:"button"};return{extendConfig:function(e){t=angular.extend(t,e)},$get:function(){return{config:t}}}}),function(e,t){"function"==typeof define&&define.amd?define(["angular"],t):"object"==typeof module&&module.exports?module.exports=t(require("angular")):e.angularClipboard=t(e.angular)}(this,function(o){return o.module("angular-clipboard",[]).factory("clipboard",["$document","$window",function(l,u){return{copyText:function(e,t){var n,r,o=u.pageXOffset||l[0].documentElement.scrollLeft,i=u.pageYOffset||l[0].documentElement.scrollTop,a=t&&t.container||l[0].body,s=(n=e,(r=l[0].createElement("textarea")).style.position="absolute",r.style.fontSize="12pt",r.style.border="0",r.style.padding="0",r.style.margin="0",r.style.left="-10000px",r.style.top=(u.pageYOffset||l[0].documentElement.scrollTop)+"px",r.textContent=n,r);a.appendChild(s),function(e){try{l[0].body.style.webkitUserSelect="initial";var t=l[0].getSelection();t.removeAllRanges();var n=document.createRange();n.selectNodeContents(e),t.addRange(n),e.select(),e.setSelectionRange(0,999999);try{if(!l[0].execCommand("copy"))throw"failure copy"}finally{t.removeAllRanges()}}finally{l[0].body.style.webkitUserSelect=""}}(s),u.scrollTo(o,i),a.removeChild(s)},supported:"queryCommandSupported"in l[0]&&l[0].queryCommandSupported("copy")}}]).directive("clipboard",["clipboard",function(r){return{restrict:"A",scope:{onCopied:"&",onError:"&",text:"=",supported:"=?"},link:function(t,n){t.supported=r.supported,n.on("click",function(e){try{r.copyText(t.text,n[0]),o.isFunction(t.onCopied)&&t.$evalAsync(t.onCopied())}catch(e){o.isFunction(t.onError)&&t.$evalAsync(t.onError({err:e}))}})}}}])}),angular.module("inputDropdown",[]).directive("inputDropdown",[function(){return{restrict:"E",scope:{defaultDropdownItems:"=",selectedItem:"=",allowCustomInput:"=",inputRequired:"=",disabled:"=",inputName:"@",inputClassName:"@",inputPlaceholder:"@",onlyShowNonEmptyDropdown:"@",filterListMethod:"&",valueChangedMethod:"&",itemSelectedMethod:"&"},template:'<div class="input-dropdown"><input type="text"name="{{inputName}}"placeholder="{{inputPlaceholder}}"autocomplete="off"ng-model="inputValue"class="{{inputClassName}}"ng-required="inputRequired"ng-change="inputChange()"ng-focus="inputFocus()"ng-blur="inputBlur($event)"ng-disabled="disabled"input-dropdown-validator><ul ng-show="dropdownVisible && dropdownItems && dropdownItems.length"><li ng-repeat="item in dropdownItems"ng-click="selectItem(item)"ng-mouseenter="setActive($index)"ng-mousedown="dropdownPressed()"ng-class="{\'active\': activeItemIndex === $index}"><span ng-if="item.readableName">{{item.readableName}}</span><span ng-if="!item.readableName">{{item}}</span></li></ul></div>',controller:["$scope",function(e){this.getSelectedItem=function(){return e.selectedItem},this.isRequired=function(){return e.inputRequired},this.customInputAllowed=function(){return e.allowCustomInput},this.getInput=function(){return e.inputValue}}],link:function(n,e){var t=!1,r=e.find("input").isolateScope();n.activeItemIndex=0,n.inputValue="",n.dropdownVisible=!1,n.dropdownItems=n.defaultDropdownItems||[],n.$watch("dropdownItems",function(e,t){angular.equals(e,t)||(n.allowCustomInput?n.setInputActive():n.setActive(0))}),n.$watch("selectedItem",function(e,t){r.updateInputValidity(),angular.equals(e,t)||e&&(n.inputValue="string"==typeof e?e:e.readableName)}),n.setInputActive=function(){n.setActive(-1)},n.setActive=function(e){n.activeItemIndex=e},n.inputChange=function(){if(n.selectedItem=null,o(),u(n.inputValue,"input"),n.inputValue){if(n.allowCustomInput&&r.updateInputValidity(),n.filterListMethod){var e=n.filterListMethod({userInput:n.inputValue});e&&e.then(function(e){n.dropdownItems=e})}}else n.dropdownItems=n.defaultDropdownItems||[]},n.inputFocus=function(){n.allowCustomInput?n.setInputActive():n.setActive(0),o()},n.inputBlur=function(e){t?t=!1:i()},n.dropdownPressed=function(){t=!0},n.selectItem=function(e){n.selectedItem=e,i(),n.dropdownItems=[e],u(e,"select"),n.itemSelectedMethod&&n.itemSelectedMethod({item:e})};var o=function(){(!n.onlyShowNonEmptyDropdown||n.dropdownItems&&n.dropdownItems.length)&&(n.dropdownVisible=!0)},i=function(){n.dropdownVisible=!1},a=function(){var e=n.activeItemIndex-1;0<=e?n.setActive(e):n.allowCustomInput&&n.setInputActive()},s=function(){var e=n.activeItemIndex+1;e<n.dropdownItems.length&&n.setActive(e)},l=function(){0<=n.activeItemIndex&&n.activeItemIndex<n.dropdownItems.length?n.selectItem(n.dropdownItems[n.activeItemIndex]):n.allowCustomInput&&n.activeItemIndex},u=function(e,t){n.valueChangedMethod&&n.valueChangedMethod({value:e,from:t})};e.bind("keydown keypress",function(e){switch(e.which){case 38:n.$apply(a);break;case 40:n.$apply(s);break;case 13:n.dropdownVisible&&n.dropdownItems&&0<n.dropdownItems.length&&-1!==n.activeItemIndex&&(e.preventDefault(),n.$apply(l));break;case 9:n.dropdownVisible&&n.dropdownItems&&0<n.dropdownItems.length&&-1!==n.activeItemIndex&&n.$apply(l)}})}}}]),angular.module("inputDropdown").directive("inputDropdownValidator",function(){return{require:["^inputDropdown","ngModel"],restrict:"A",scope:{},link:function(e,t,n,r){var o=r[0],i=r[1];e.updateInputValidity=function(){var e=o.getSelectedItem(),t=!1;o.isRequired()?o.customInputAllowed()&&o.getInput()?t=!0:e&&(t=!0):t=!0,i.$setValidity("itemSelectedValid",t)}}}}),function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).angularDragula=e()}}(function(){return function i(a,s,l){function u(n,e){if(!s[n]){if(!a[n]){var t="function"==typeof require&&require;if(!e&&t)return t(n,!0);if(c)return c(n,!0);var r=new Error("Cannot find module '"+n+"'");throw r.code="MODULE_NOT_FOUND",r}var o=s[n]={exports:{}};a[n][0].call(o.exports,function(e){var t=a[n][1][e];return u(t||e)},o,o.exports,i,a,s,l)}return s[n].exports}for(var c="function"==typeof require&&require,e=0;e<l.length;e++)u(l[e]);return u}({1:[function(e,t,n){"use strict";var r=e("./service"),o=e("./directive");t.exports=function(e){var t=e.module("dragula",["ng"]);return t.factory("dragulaService",r(e)),t.directive("dragula",o(e)),"dragula"}},{"./directive":2,"./service":13}],2:[function(e,t,n){"use strict";var u=e("dragula");t.exports=function(e){return["dragulaService",function(l){return{restrict:"A",scope:{dragulaScope:"=",dragulaModel:"="},link:function(e,t,n){var r,o=e.dragulaScope||e.$parent,i=t[0],a=e.$eval(n.dragula),s=l.find(o,a);s?(r=s.drake).containers.push(i):(r=u({containers:[i]}),l.add(o,a,r)),e.$watch("dragulaModel",function(e,t){if(e){if(r.models){var n=t?r.models.indexOf(t):-1;0<=n?r.models.splice(n,1,e):r.models.push(e)}else r.models=[e];l.handleModels(o,r)}})}}}]}},{dragula:10}],3:[function(e,t,n){t.exports=function(e,t){return Array.prototype.slice.call(e,t)}},{}],4:[function(e,t,n){"use strict";var r=e("ticky");t.exports=function(e,t,n){e&&r(function(){e.apply(n||null,t||[])})}},{ticky:11}],5:[function(e,t,n){"use strict";var s=e("atoa"),l=e("./debounce");t.exports=function(o,e){var i=e||{},a={};return void 0===o&&(o={}),o.on=function(e,t){return a[e]?a[e].push(t):a[e]=[t],o},o.once=function(e,t){return t._once=!0,o.on(e,t),o},o.off=function(e,t){var n=arguments.length;if(1===n)delete a[e];else if(0===n)a={};else{var r=a[e];if(!r)return o;r.splice(r.indexOf(t),1)}return o},o.emit=function(){var e=s(arguments);return o.emitterSnapshot(e.shift()).apply(this,e)},o.emitterSnapshot=function(r){var e=(a[r]||[]).slice(0);return function(){var t=s(arguments),n=this||o;if("error"===r&&!1!==i.throws&&!e.length)throw 1===t.length?t[0]:t;return e.forEach(function(e){i.async?l(e,t,n):e.apply(n,t),e._once&&o.off(r,e)}),o}},o}},{"./debounce":4,atoa:3}],6:[function(n,r,e){(function(u){"use strict";function c(e,t,n){var r=function(e,t,n){var r,o;for(r=0;r<d.length;r++)if((o=d[r]).element===e&&o.type===t&&o.fn===n)return r}(e,t,n);if(r){var o=d[r].wrapper;return d.splice(r,1),o}}var i=n("custom-event"),a=n("./eventmap"),s=u.document,e=function(e,t,n,r){return e.addEventListener(t,n,r)},t=function(e,t,n,r){return e.removeEventListener(t,n,r)},d=[];u.addEventListener||(e=function(e,t,n){return e.attachEvent("on"+t,(a=c(r=e,o=t,i=n)||(s=r,l=i,function(e){var t=e||u.event;t.target=t.target||t.srcElement,t.preventDefault=t.preventDefault||function(){t.returnValue=!1},t.stopPropagation=t.stopPropagation||function(){t.cancelBubble=!0},t.which=t.which||t.keyCode,l.call(s,t)}),d.push({wrapper:a,element:r,type:o,fn:i}),a));var r,o,i,a,s,l},t=function(e,t,n){var r=c(e,t,n);return r?e.detachEvent("on"+t,r):void 0}),r.exports={add:e,remove:t,fabricate:function(e,t,n){var r,o=-1===a.indexOf(t)?new i(t,{detail:n}):(s.createEvent?(r=s.createEvent("Event")).initEvent(t,!0,!0):s.createEventObject&&(r=s.createEventObject()),r);e.dispatchEvent?e.dispatchEvent(o):e.fireEvent("on"+t,o)}}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./eventmap":7,"custom-event":8}],7:[function(e,o,t){(function(e){"use strict";var t=[],n="",r=/^on/;for(n in e)r.test(n)&&t.push(n.slice(2));o.exports=t}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],8:[function(e,n,t){(function(e){var t=e.CustomEvent;n.exports=function(){try{var e=new t("cat",{detail:{foo:"bar"}});return"cat"===e.type&&"bar"===e.detail.foo}catch(e){}return!1}()?t:"function"==typeof document.createEvent?function(e,t){var n=document.createEvent("CustomEvent");return t?n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail):n.initCustomEvent(e,!1,!1,void 0),n}:function(e,t){var n=document.createEventObject();return n.type=e,t?(n.bubbles=Boolean(t.bubbles),n.cancelable=Boolean(t.cancelable),n.detail=t.detail):(n.bubbles=!1,n.cancelable=!1,n.detail=void 0),n}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],9:[function(e,t,n){"use strict";function r(e){var t=o[e];return t?t.lastIndex=0:o[e]=t=new RegExp(i+e+a,"g"),t}var o={},i="(?:^|\\s)",a="(?:\\s|$)";t.exports={add:function(e,t){var n=e.className;n.length?r(t).test(n)||(e.className+=" "+t):e.className=t},rm:function(e,t){e.className=e.className.replace(r(t)," ").trim()}}},{}],10:[function(e,t,n){(function(o){"use strict";function j(e,t,n,r){o.navigator.pointerEnabled?Q[t](e,{mouseup:"pointerup",mousedown:"pointerdown",mousemove:"pointermove"}[n],r):o.navigator.msPointerEnabled?Q[t](e,{mouseup:"MSPointerUp",mousedown:"MSPointerDown",mousemove:"MSPointerMove"}[n],r):(Q[t](e,{mouseup:"touchend",mousedown:"touchstart",mousemove:"touchmove"}[n],r),Q[t](e,n,r))}function R(e){if(void 0!==e.touches)return e.touches.length;if(void 0!==e.which&&0!==e.which)return e.which;if(void 0!==e.buttons)return e.buttons;var t=e.button;return void 0!==t?1&t?1:2&t?3:4&t?2:0:void 0}function z(e,t){return void 0!==o[t]?o[t]:ee.clientHeight?ee[e]:J.body[e]}function F(e,t,n){var r,o=e||{},i=o.className;return o.className+=" gu-hide",r=J.elementFromPoint(t,n),o.className=i,r}function U(){return!1}function q(){return!0}function V(e){return e.width||e.right-e.left}function H(e){return e.height||e.bottom-e.top}function W(e){return e.parentNode===J?null:e.parentNode}function X(e){return"INPUT"===e.tagName||"TEXTAREA"===e.tagName||"SELECT"===e.tagName||function e(t){return!!t&&("false"!==t.contentEditable&&("true"===t.contentEditable||e(W(t))))}(e)}function G(t){return t.nextElementSibling||function(){for(var e=t;(e=e.nextSibling)&&1!==e.nodeType;);return e}()}function K(e,t){var n,r=(n=t).targetTouches&&n.targetTouches.length?n.targetTouches[0]:n.changedTouches&&n.changedTouches.length?n.changedTouches[0]:n,o={pageX:"clientX",pageY:"clientY"};return e in o&&!(e in r)&&o[e]in r&&(e=o[e]),r[e]}var Y=e("contra/emitter"),Q=e("crossvent"),Z=e("./classes"),J=document,ee=J.documentElement;t.exports=function(e,t){function a(e){return-1!==_.containers.indexOf(e)||D.isContainer(e)}function n(e){var t=e?"remove":"add";j(ee,t,"mousedown",o),j(ee,t,"mouseup",p)}function s(e){j(ee,e?"remove":"add","mousemove",i)}function l(e){var t=e?"remove":"add";Q[t](ee,"selectstart",r),Q[t](ee,"click",r)}function r(e){P&&e.preventDefault()}function o(e){if(A=e.clientX,O=e.clientY,1===R(e)&&!e.metaKey&&!e.ctrlKey){var t=e.target,n=u(t);n&&(P=n,s(),"mousedown"===e.type&&(X(t)?t.focus():e.preventDefault()))}}function i(e){if(P){if(0===R(e))return void p({});if(void 0===e.clientX||e.clientX!==A||void 0===e.clientY||e.clientY!==O){if(D.ignoreInputTextSelection){var t=K("clientX",e),n=K("clientY",e);if(X(J.elementFromPoint(t,n)))return}var r=P;s(!0),l(),d(),c(r);var o={left:(i=k.getBoundingClientRect()).left+z("scrollLeft","pageXOffset"),top:i.top+z("scrollTop","pageYOffset")};$=K("pageX",e)-o.left,E=K("pageY",e)-o.top,Z.add(L||k,"gu-transit"),function(){if(!x){var e=k.getBoundingClientRect();(x=k.cloneNode(!0)).style.width=V(e)+"px",x.style.height=H(e)+"px",Z.rm(x,"gu-transit"),Z.add(x,"gu-mirror"),D.mirrorContainer.appendChild(x),j(ee,"add","mousemove",w),Z.add(D.mirrorContainer,"gu-unselectable"),_.emit("cloned",x,k,"mirror")}}(),w(e)}}var i}function u(e){if(!(_.dragging&&x||a(e))){for(var t=e;W(e)&&!1===a(W(e));){if(D.invalid(e,t))return;if(!(e=W(e)))return}var n=W(e);if(n&&!D.invalid(e,t)&&D.moves(e,n,t,G(e)))return{item:e,source:n}}}function c(e){var t,n;t=e.item,n=e.source,("boolean"==typeof D.copy?D.copy:D.copy(t,n))&&(L=e.item.cloneNode(!0),_.emit("cloned",L,e.item,"copy")),T=e.source,k=e.item,I=B=G(e.item),_.dragging=!0,_.emit("drag",k,T)}function d(){if(_.dragging){var e=L||k;h(e,W(e))}}function f(){s(!(P=!1)),l(!0)}function p(e){if(f(),_.dragging){var t=L||k,n=K("clientX",e),r=K("clientY",e),o=b(F(x,n,r),n,r);o&&(L&&D.copySortSource||!L||o!==T)?h(t,o):D.removeOnSpill?g():m()}}function h(e,t){var n=W(e);L&&D.copySortSource&&t===T&&n.removeChild(k),y(t)?_.emit("cancel",e,T,T):_.emit("drop",e,t,T,B),v()}function g(){if(_.dragging){var e=L||k,t=W(e);t&&t.removeChild(e),_.emit(L?"cancel":"remove",e,t,T),v()}}function m(e){if(_.dragging){var t=0<arguments.length?e:D.revertOnSpill,n=L||k,r=W(n),o=y(r);!1===o&&t&&(L?r&&r.removeChild(L):T.insertBefore(n,I)),o||t?_.emit("cancel",n,T,T):_.emit("drop",n,r,T,B),v()}}function v(){var e=L||k;f(),x&&(Z.rm(D.mirrorContainer,"gu-unselectable"),j(ee,"remove","mousemove",w),W(x).removeChild(x),x=null),e&&Z.rm(e,"gu-transit"),M&&clearTimeout(M),_.dragging=!1,N&&_.emit("out",e,N,T),_.emit("dragend",e),T=k=L=I=B=M=N=null}function y(e,t){var n;return n=void 0!==t?t:x?B:G(L||k),e===T&&n===I}function b(n,r,o){function e(){if(!1===a(i))return!1;var e=C(i,n),t=S(i,e,r,o);return!!y(i,t)||D.accepts(k,i,T,t)}for(var i=n;i&&!e();)i=W(i);return i}function w(e){function t(e){_.emit(e,a,N,T)}if(x){e.preventDefault();var n=K("clientX",e),r=K("clientY",e),o=n-$,i=r-E;x.style.left=o+"px",x.style.top=i+"px";var a=L||k,s=F(x,n,r),l=b(s,n,r),u=null!==l&&l!==N;(u||null===l)&&(N&&t("out"),N=l,u&&t("over"));var c=W(a);if(l===T&&L&&!D.copySortSource)return void(c&&c.removeChild(a));var d,f=C(l,s);if(null!==f)d=S(l,f,n,r);else{if(!0!==D.revertOnSpill||L)return void(L&&c&&c.removeChild(a));d=I,l=T}(null===d&&u||d!==a&&d!==G(a))&&(B=d,l.insertBefore(a,d),_.emit("shadow",a,l,T))}}function C(e,t){for(var n=t;n!==e&&W(n)!==e;)n=W(n);return n===ee?null:n}function S(o,e,i,a){var t,s="horizontal"===D.direction;return e!==o?(t=e.getBoundingClientRect(),(s?i>t.left+V(t)/2:a>t.top+H(t)/2)?G(e):e):function(){var e,t,n,r=o.children.length;for(e=0;e<r;e++){if(n=(t=o.children[e]).getBoundingClientRect(),s&&n.left+n.width/2>i)return t;if(!s&&n.top+n.height/2>a)return t}return null}()}1===arguments.length&&!1===Array.isArray(e)&&(t=e,e=[]);var x,T,k,$,E,A,O,I,B,L,M,P,N=null,D=t||{};void 0===D.moves&&(D.moves=q),void 0===D.accepts&&(D.accepts=q),void 0===D.invalid&&(D.invalid=function(){return!1}),void 0===D.containers&&(D.containers=e||[]),void 0===D.isContainer&&(D.isContainer=U),void 0===D.copy&&(D.copy=!1),void 0===D.copySortSource&&(D.copySortSource=!1),void 0===D.revertOnSpill&&(D.revertOnSpill=!1),void 0===D.removeOnSpill&&(D.removeOnSpill=!1),void 0===D.direction&&(D.direction="vertical"),void 0===D.ignoreInputTextSelection&&(D.ignoreInputTextSelection=!0),void 0===D.mirrorContainer&&(D.mirrorContainer=J.body);var _=Y({containers:D.containers,start:function(e){var t=u(e);t&&c(t)},end:d,cancel:m,remove:g,destroy:function(){n(!0),p({})},canMove:function(e){return!!u(e)},dragging:!1});return!0===D.removeOnSpill&&_.on("over",function(e){Z.rm(e,"gu-hide")}).on("out",function(e){_.dragging&&Z.add(e,"gu-hide")}),n(),_}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./classes":9,"contra/emitter":5,crossvent:6}],11:[function(e,t,n){var r;r="function"==typeof setImmediate?function(e){setImmediate(e)}:function(e){setTimeout(e,0)},t.exports=r},{}],12:[function(e,t,n){"use strict";var i=e("atoa"),a=["cancel","cloned","drag","dragend","drop","out","over","remove","shadow","drop-model","remove-model"];t.exports=function(t,n,r){function o(e){return t.isElement(e)?t.element(e):e}a.forEach(function(t){n.drake.on(t,function(){var e=i(arguments).map(o);e.unshift(n.name+"."+t),r.$emit.apply(r,e)})})}},{atoa:3}],13:[function(e,t,n){"use strict";var s=e("dragula"),l="$$dragula",u=e("./replicate-events");t.exports=function(f){return[function(){function o(n,a){var s,l,u,c;a.registered||(a.on("remove",function(e,t){a.models&&(c=a.models[a.containers.indexOf(t)],n.$applyAsync(function(){c.splice(l,1),a.emit("remove-model",e,t)}))}),a.on("drag",function(e,t){l=d(s=e,t)}),a.on("drop",function(r,o,i){a.models&&(u=d(r,o),n.$applyAsync(function(){if(c=a.models[a.containers.indexOf(i)],o===i)c.splice(u,0,c.splice(l,1)[0]);else{var e=s===r,t=a.models[a.containers.indexOf(o)],n=e?c[l]:f.copy(c[l]);e&&c.splice(l,1),t.splice(u,0,n),o.removeChild(r)}a.emit("drop-model",r,o,i)}))}),a.registered=!0)}function i(e){var t=e[l];return t||(t=e[l]={bags:[]}),t}function d(e,t){return Array.prototype.indexOf.call(f.element(t).children(),e)}function r(e,t,n){var r=a(e,t);if(r)throw new Error('Bag named: "'+t+'" already exists in same angular scope.');return r={name:t,drake:n},i(e).bags.push(r),u(f,r,e),n.models&&o(e,n),r}function a(e,t){for(var n=i(e).bags,r=0;r<n.length;r++)if(n[r].name===t)return n[r]}return{add:r,find:a,options:function(e,t,n){o(e,r(e,t,s(n)).drake)},destroy:function(e,t){var n=i(e).bags,r=a(e,t),o=n.indexOf(r);n.splice(o,1),r.drake.destroy()},handleModels:o}}]}},{"./replicate-events":12,dragula:10}]},{},[1])(1)}),function(e,t){"use strict";"function"==typeof define&&define.amd?define(["angular","sweetalert"],t):"object"==typeof module&&module.exports?module.exports=t(require("angular"),require("sweetalert")):t(e.angular,e.swal)}(this,function(e,o){"use strict";e.module("oitozero.ngSweetAlert",[]).factory("SweetAlert",["$rootScope",function(r){return{swal:function(e,t,n){r.$evalAsync(function(){o(e,"function"==typeof t?function(e){r.$evalAsync(function(){t(e)})}:t,n)})},success:function(e,t){r.$evalAsync(function(){o(e,t,"success")})},error:function(e,t){r.$evalAsync(function(){o(e,t,"error")})},warning:function(e,t){r.$evalAsync(function(){o(e,t,"warning")})},info:function(e,t){r.$evalAsync(function(){o(e,t,"info")})},showInputError:function(e){r.$evalAsync(function(){o.showInputError(e)})},close:function(){r.$evalAsync(function(){o.close()})}}}])});