[global]
AriaNg Version=Versione di AriaNg
Operation Result=Risultato dell'operazione
Operation Succeeded=Operazione completata con successo
is connected=Connesso
Error=Errore
OK=OK
Confirm=Conferma
Cancel=Annulla
Close=Chiudi
True=Vero
False=Falso
DEBUG=Debug
INFO=Informazioni
WARN=Avviso
ERROR=Errore
Connecting=Connessione in corso
Connected=Connesso
Disconnected=Disconnesso
Reconnecting=Riconnessione in corso
Waiting to reconnect=In attesa di riconnessione
Global=Globale
New=Nuovo
Start=Avvia
Pause=Pausa
Retry=Riprova
Retry Selected Tasks=Riprova le attività selezionate
Delete=Elimina
Select All=Seleziona tutto
Select None=Deseleziona tutto
Select Invert=Inverti selezione
Select All Failed Tasks=Seleziona tutte le attività fallite
Select All Completed Tasks=Seleziona tutte le attività completate
Select All Tasks=Seleziona tutte le attività
Display Order=Ordine di visualizzazione
Copy Download Url=Copia URL download
Copy Magnet Link=Copia link magnetico
Help=Aiuto
Search=Cerca
Default=Predefinito
Expand=Espandi
Collapse=Comprimi
Expand All=Espandi tutto
Collapse All=Comprimi tutto
Open=Apri
Save=Salva
Import=Importa
Remove Task=Rimuovi attività
Remove Selected Task=Rimuovi attività selezionata
Clear Stopped Tasks=Cancella attività terminate
Click to view task detail=Clicca per visualizzare i dettagli dell'attività
By File Name=Per nome file
By File Size=Per dimensione file
By Progress=Per progressione
By Selected Status=Per stato selezionato
By Remaining=Per tempo rimanente
By Download Speed=Per velocità di download
By Upload Speed=Per velocità di upload
By Peer Address=Per indirizzo peer
By Client Name=Per nome client
Filters=Filtri
Download=Download
Upload=Upload
Downloading=In download
Pending Verification=In attesa di verifica
Verifying=Verifica in corso
Seeding=Seeding in corso
Waiting=In attesa
Paused=In pausa
Completed=Completato
Error Occurred=Errore verificatosi
Removed=Rimosso
Finished / Stopped=Terminato / Interrotto
Uncompleted=Non completato
Click to pin=Clicca per fissare
Settings=Impostazioni
AriaNg Settings=Impostazioni AriaNg
Aria2 Settings=Impostazioni Aria2
Basic Settings=Impostazioni di base
HTTP/FTP/SFTP Settings=Impostazioni HTTP/FTP/SFTP
HTTP Settings=Impostazioni HTTP
FTP/SFTP Settings=Impostazioni FTP/SFTP
BitTorrent Settings=Impostazioni BitTorrent
Metalink Settings=Impostazioni Metalink
RPC Settings=Impostazioni RPC
Advanced Settings=Impostazioni avanzate
AriaNg Debug Console=Console di debug di AriaNg
Aria2 Status=Stato di Aria2
File Name=Nome file
File Size=Dimensione file
Progress=Progressione
Share Ratio=Rapporto di condivisione
Remaining=Tempo rimanente
Download Speed=Velocità di download
Upload Speed=Velocità di upload
Links=Collegamenti
Torrent File=File torrent
Metalink File=File Metalink
File Name:=Nome file:
Options=Opzioni
Overview=Panoramica
Pieces=Informazioni sui blocchi
Files=Elenco dei file
Peers=Stato della connessione
Task Name=Nome dell'attività
Task Size=Dimensione dell'attività
Task Status=Stato dell'attività
Error Description=Descrizione dell'errore
Health Percentage=Percentuale di integrità
Info Hash=Valore hash
Seeders=Numero di seed
Connections=Numero di connessioni
Seed Creation Time=Ora di creazione del seed
Download Url=URL di download
Download Dir=Percorso di download
BT Tracker Servers=Server tracker BT
Copy=Copia
(Choose Files)=(Scegli file)
Videos=Video
Audios=Audio
Pictures=Immagini
Documents=Documenti
Applications=Applicazioni
Archives=File archivio
Other=Altri
Custom=Personalizzato
Custom Choose File=Scegli file personalizzato
Address=Indirizzo
Client=Client
Status=Stato
Speed=Velocità
(local)=(Locale)
No Data=Nessun dato
No connected peers=Nessun peer connesso
Failed to change some tasks state.=Impossibile modificare lo stato di alcune attività.
Confirm Retry=Conferma riprova
Are you sure you want to retry the selected task? AriaNg will create same task after clicking OK.=Sei sicuro di voler riprovare l'attività selezionata? AriaNg creerà la stessa attività dopo aver cliccato su OK.
Failed to retry this task.=Impossibile riprovare questa attività.
{successCount} tasks have been retried and {failedCount} tasks are failed.={{successCount}} attività sono state riprovate e {{failedCount}} attività sono fallite.
Confirm Remove=Conferma eliminazione
Are you sure you want to remove the selected task?=Sei sicuro di voler eliminare l'attività selezionata?
Failed to remove some task(s).=Impossibile eliminare alcune attività.
Confirm Clear=Conferma cancellazione
Are you sure you want to clear stopped tasks?=Sei sicuro di voler cancellare le attività terminate?
Download Links:=Collegamenti per il download:
Download Now=Scarica ora
Download Later=Scarica manualmente
Open Torrent File=Apri file torrent
Open Metalink File=Apri file Metalink
Support multiple URLs, one URL per line.=Supporta più URL, un URL per riga.
Your browser does not support loading file!=Il tuo browser non supporta il caricamento di file!
The selected file type is invalid!=Il tipo di file selezionato non è valido!
Failed to load file!=Impossibile caricare il file!
Download Completed=Download completato
BT Download Completed=Download BT completato
Download Error=Errore di download
AriaNg Url=URL di AriaNg
Command API Url=URL dell'API della riga di comando
Export Command API=Esporta API della riga di comando
Export=Esporta
Copied=Copiato
Pause After Task Created=Pausa dopo la creazione dell'attività
Language=Lingua
Theme=Tema
Light=Chiaro
Dark=Scuro
Follow system settings=Segui le impostazioni di sistema
Debug Mode=Modalità debug
Page Title=Titolo pagina
Preview=Anteprima
Tips: You can use the "noprefix" tag to ignore the prefix, "nosuffix" tag to ignore the suffix, and "scale\=n" tag to set the decimal precision.=Suggerimenti: puoi utilizzare il tag "noprefix" per ignorare il prefisso, il tag "nosuffix" per ignorare il suffisso e il tag "scale\=n" per impostare la precisione decimale.
Example: ${downspeed:noprefix:nosuffix:scale\=1}=Esempio: ${downspeed:noprefix:nosuffix:scale\=1}
Updating Page Title Interval=Aggiornamento intervallo titolo pagina
Enable Browser Notification=Abilita notifica browser
Browser Notification Sound=Suono notifica browser
Browser Notification Frequency=Frequenza notifica browser
Unlimited=Illimitato
High (Up to 10 Notifications / 1 Minute)=Alta (Fino a 10 notifiche/minuto)
Middle (Up to 1 Notification / 1 Minute)=Media (Fino a 1 notifica/minuto)
Low (Up to 1 Notification / 5 Minutes)=
WebSocket Auto Reconnect Interval=
Aria2 RPC Alias=
Aria2 RPC Address=
Aria2 RPC Protocol=
Aria2 RPC Http Request Method=
POST method only supports aria2 v1.15.2 and above.=
Aria2 RPC Request Headers=
Support multiple request headers, one header per line, each line containing "header name: header value".=
Aria2 RPC Secret Token=
Activate=
Reset Settings=
Confirm Reset=
Are you sure you want to reset all settings?=
Clear Settings History=
Are you sure you want to clear all settings history?=
Delete RPC Setting=
Add New RPC Setting=
Are you sure you want to remove rpc setting "{rpcName}"?=
Updating Global Stat Interval=
Updating Task Information Interval=
Keyboard Shortcuts=
Supported Keyboard Shortcuts=
Set Focus On Search Box=
Swipe Gesture=
Change Tasks Order by Drag-and-drop=
Action After Creating New Tasks=
Navigate to Task List Page=
Navigate to Task Detail Page=
Action After Retrying Task=
Navigate to Downloading Tasks Page=
Stay on Current Page=
Remove Old Tasks After Retrying=
Confirm Task Removal=
Include Prefix When Copying From Task Details=
Show Pieces Info In Task Detail Page=
Pieces Amount is Less than or Equal to {value}=
RPC List Display Order=
Each Task List Page Uses Independent Display Order=
Recently Used=
RPC Alias=
Import / Export AriaNg Settings=
Import Settings=
Export Settings=
AriaNg settings data=
Confirm Import=
Are you sure you want to import all settings?=
Invalid settings data format!=
Data has been copied to clipboard.=
Supported Placeholder=
AriaNg Title=
Current RPC Alias=
Downloading Count=
Waiting Count=
Stopped Count=
You have disabled notification in your browser. You should change your browser's settings before you enable this function.=
Language resource has been updated, please reload the page for the changes to take effect.=
Configuration has been modified, please reload the page for the changes to take effect.=
Reload AriaNg=
Show Secret=
Hide Secret=
Aria2 Version=
Enabled Features=
Operations=
Reconnect=
Save Session=
Shutdown Aria2=
Confirm Shutdown=
Are you sure you want to shutdown aria2?=
Session has been saved successfully.=
Aria2 has been shutdown successfully.=
Toggle Navigation=
Shortcut=
Global Rate Limit=
Loading=
More Than One Day=
Unknown=
Bytes=
Hours=
Minutes=
Seconds=
Milliseconds=
Http=
Http (Disabled)=
Https=
WebSocket=
WebSocket (Disabled)=
WebSocket (Security)=
Http and WebSocket would be disabled when accessing AriaNg via Https.=
POST=
GET=
Enabled=
Disabled=
Always=
Never=
BitTorrent=
Changes to the settings take effect after refreshing page.=
Logging Time=
Log Level=
Auto Refresh=
Refresh Now=
Clear Logs=
Are you sure you want to clear debug logs?=
Show Detail=
Log Detail=
Aria2 RPC Debug=
Aria2 RPC Request Method=
Aria2 RPC Request Parameters=
Aria2 RPC Response=
Execute=
RPC method is illegal!=
AriaNg does not support this RPC method!=
RPC request parameters are invalid!=
Type is illegal!=
Parameter is invalid!=
Option value cannot be empty!=
Input number is invalid!=
Input number is below min value!=
Input number is above max value!=
Input value is invalid!=
Protocol is invalid!=
RPC host cannot be empty!=
RPC secret is not base64 encoded!=La chiave RPC non è codificata in base64!
URL is not base64 encoded!=L'URL non è codificato in base64!
Tap to configure and get started with AriaNg.=Tocca per configurare e iniziare a utilizzare AriaNg.
Cannot initialize WebSocket!=Impossibile inizializzare WebSocket!
Cannot connect to aria2!=Impossibile connettersi a aria2!
Access Denied!=Accesso negato!
You cannot use AriaNg because this browser does not meet the minimum requirements for data storage.=Non puoi utilizzare AriaNg perché questo browser non soddisfa i requisiti minimi di archiviazione dati.

[error]
unknown=Errore sconosciuto.
operation.timeout=Operazione scaduta.
resource.notfound=Impossibile trovare la risorsa specificata.
resource.notfound.max-file-not-found=Impossibile trovare la risorsa specificata. Vedere l'opzione --max-file-not-found.
download.aborted.lowest-speed-limit=Il download è stato interrotto a causa della velocità di download troppo bassa. Vedere l'opzione --lowest-speed-limit.
network.problem=Problema di rete.
resume.notsupported=Il server non supporta la ripresa del download.
space.notenough=Spazio libero su disco insufficiente.
piece.length.different=La lunghezza del blocco è diversa da quella nel file di controllo .aria2. Vedere l'opzione --allow-piece-length-change.
download.sametime=aria2 ha già scaricato un altro file identico.
download.torrent.sametime=aria2 ha già scaricato un altro file torrent con lo stesso hash.
file.exists=Il file esiste già. Vedere l'opzione --allow-overwrite.
file.rename.failed=Errore durante la ridenominazione del file. Vedere l'opzione --auto-file-renaming.
file.open.failed=Errore durante l'apertura del file.
file.create.failed=Errore durante la creazione del file o l'eliminazione di un file esistente.
io.error=Errore del file system.
directory.create.failed=Impossibile creare la directory specificata.
name.resolution.failed=Errore durante la risoluzione del nome.
metalink.file.parse.failed=Errore durante l'analisi del file Metalink.
ftp.command.failed=Errore durante l'esecuzione del comando FTP.
http.response.header.bad=Intestazione di risposta HTTP non valida o non riconosciuta.
redirects.toomany=Troppi reindirizzamenti per l'indirizzo specificato.
http.authorization.failed=Autenticazione HTTP fallita.
bencoded.file.parse.failed=Errore durante l'analisi del file torrent.
torrent.file.corrupted=Il file torrent ".torrent" specificato è danneggiato o mancante di informazioni necessarie ad aria2.
magnet.uri.bad=L'indirizzo magnet specificato non è valido.
option.bad=Errore di impostazione.
server.overload=Il server remoto è troppo occupato per gestire la richiesta corrente.
rpc.request.parse.failed=Errore durante l'analisi della richiesta RPC.
checksum.failed=Errore di verifica del checksum del file.

[languages]
Czech=Ceco
German=Tedesco
English=Inglese
Spanish=Spagnolo
French=Francese
Italian=Italiano
Polish=Polacco
Russian=Russo
Simplified Chinese=Cinese Semplificato
Traditional Chinese=Cinese Tradizionale

[format]
longdate=DD/MM/YYYY HH:mm:ss
time.millisecond={{value}} millisecondo
time.milliseconds={{value}} millisecondi
time.second={{value}} secondo
time.seconds={{value}} secondi
time.minute={{value}} minuto
time.minutes={{value}} minuti
time.hour={{value}} ora
time.hours={{value}} ore
requires.aria2-version=Richiede la versione {{version}} di aria2
task.new.download-links=Collegamenti per il download ({{count}} collegamenti):
task.pieceinfo=Completati: {{completed}}, Totale: {{total}} blocchi
task.error-occurred=Si è verificato un errore ({{errorcode}})
task.verifying-percent=Verifica in corso ({{verifiedPercent}}%)
settings.file-count=({{count}} file)
settings.total-count=(Totale: {{count}})
debug.latest-logs=Ultimi {{count}} log

[rpc.error]
unauthorized=Autenticazione fallita!

[option]
true=Sì
false=No
default=Predefinito
none=Nessuno
hide=Nascondi
full=Completo
http=Http
https=Https
ftp=Ftp
mem=Solo memoria
get=GET
tunnel=TUNNEL
plain=Testo semplice
arc4=ARC4
binary=Binario
ascii=ASCII
debug=Debug
info=Info
notice=Avviso
warn=Avvertenza
error=Errore
adaptive=Adattivo
epoll=epoll
falloc=falloc
feedback=Feedback
geom=Geometria
inorder=In ordine
kqueue=kqueue
poll=poll
port=port
prealloc=prealloc
random=Casuale
select=select
trunc=trunc
SSLv3=SSLv3
TLSv1=TLSv1
TLSv1.1=TLSv1.1
TLSv1.2=TLSv1.2

[options]
dir.name=Percorso di download
dir.description=
log.name=File di registro
log.description=Percorso del file di registro. Se impostato su "-", il registro viene scritto su stdout. Se impostato su una stringa vuota (""), il registro non verrà memorizzato su disco.
max-concurrent-downloads.name=Numero massimo di download simultanei
max-concurrent-downloads.description=
check-integrity.name=Verifica integrità
check-integrity.description=Verifica l'integrità del file tramite la convalida hash di ogni blocco o del file completo. Questa opzione è valida solo per collegamenti BT, Metalink e per collegamenti HTTP(S)/FTP che hanno impostato l'opzione --checksum.
continue.name=Riprendi download
continue.description=Riprende il download dei file parzialmente scaricati. Abilitando questa opzione, è possibile riprendere il download dei file scaricati sequenzialmente da un browser o da altri programmi. Questa opzione è attualmente supportata solo per i file scaricati tramite HTTP(S)/FTP.
all-proxy.name=Server proxy
all-proxy.description=Imposta l'indirizzo del server proxy per tutti i protocolli. È anche possibile sovrascrivere questa opzione per protocolli specifici utilizzando le opzioni --http-proxy, --https-proxy e --ftp-proxy. Questa impostazione influenzerà tutti i download. Il formato dell'indirizzo del server proxy è [http://][UTENTE:PASSWORD@]HOST[:PORTA].
all-proxy-user.name=Nome utente del server proxy
all-proxy-user.description=
all-proxy-passwd.name=Password del server proxy
all-proxy-passwd.description=
checksum.name=Checksum
checksum.description=Imposta il checksum. Il formato del valore dell'opzione è TIPO=DIGEST. TIPO è il tipo di hash supportato elencato negli algoritmi hash di aria2c -v. DIGEST è il digest esadecimale. Ad esempio, impostare il digest sha-1 sarebbe simile a questo: sha-1=0192ba11326fe2298c8cb4de616f4d4140213838. Questa opzione è valida solo per i download HTTP(S)/FTP.
connect-timeout.name=Timeout connessione
connect-timeout.description=Imposta il timeout (in secondi) per stabilire la connessione a un server HTTP/FTP/proxy. Una volta stabilita la connessione, questa opzione non avrà più effetto, si prega di utilizzare l'opzione --timeout.
dry-run.name=Esecuzione di prova
dry-run.description=Se impostato su "sì", aria2 verificherà solo se il file remoto esiste senza scaricare il contenuto del file. Questa opzione è valida solo per i download HTTP/FTP. Se impostato su true, i download BT verranno interrotti immediatamente.
lowest-speed-limit.name=Limite minimo di velocità
lowest-speed-limit.description=Chiude la connessione quando la velocità di download è inferiore al valore impostato (in B/s). 0 indica nessun limite di velocità minimo. È possibile aggiungere unità di misura come K o M (1K=1024, 1M=1024K). Questa opzione non influisce sui download BT.
max-connection-per-server.name=Numero massimo di connessioni per server
max-connection-per-server.description=
max-file-not-found.name=Tentativi di riprova per file non trovati
max-file-not-found.description=Se aria2 riceve lo stato "file non trovato" da un server HTTP/FTP remoto più volte rispetto al numero impostato in questa opzione, il download fallirà. Impostare a 0 disabiliterà questa opzione. Questa opzione influisce solo sui server HTTP/FTP. I tentativi di riprova verranno registrati insieme al numero di tentativi di riprova, quindi è necessario impostare l'opzione --max-tries.
max-tries.name=Numero massimo di tentativi
max-tries.description=Imposta il numero massimo di tentativi. 0 indica nessuna limitazione.
min-split-size.name=Dimensione minima della suddivisione dei file
min-split-size.description=aria2 non dividerà i file più piccoli di 2*DIMENSIONE byte. Ad esempio, se la dimensione del file è di 20 MB e DIMENSIONE è 10M, aria2 dividerà il file in 2 segmenti [0-10MB) e [10MB-20MB) e utilizzerà 2 fonti per il download (se --split >= 2). Se DIMENSIONE è 15M, poiché 2*15M > 20MB, aria2 non dividerà il file e utilizzerà 1 fonte per il download. È possibile aggiungere unità di misura come K o M (1K=1024, 1M=1024K). I valori ammissibili vanno da: 1M-1024M.
netrc-path.name=Percorso del file .netrc
netrc-path.description=
no-netrc.name=Disabilita netrc
no-netrc.description=
no-proxy.name=Elenco dei server proxy da non utilizzare
no-proxy.description=Imposta gli hostname, i nomi di dominio, gli indirizzi di rete con o senza subnet mask da non utilizzare il server proxy. Utilizzare la virgola per separare più voci.
out.name=Nome del file
out.description=Nome del file scaricato. È sempre relativo al percorso impostato dall'opzione --dir. Questa opzione non è valida quando viene utilizzata l'opzione --force-sequential.
proxy-method.name=Metodo di richiesta del server proxy
proxy-method.description=Imposta il metodo da utilizzare per richiedere il server proxy. Il metodo può essere impostato su GET o TUNNEL. I download HTTPS ignoreranno questa opzione e utilizzeranno sempre TUNNEL.
remote-time.name=Ottieni il tempo del file dal server
remote-time.description=Ottiene il timestamp del file remoto da un servizio HTTP/FTP e lo imposta sul file locale, se disponibile
reuse-uri.name=Riutilizza URI
reuse-uri.description=Quando tutti gli URI forniti sono stati utilizzati, continua a utilizzare gli URI già utilizzati.
retry-wait.name=Tempo di attesa per il ritentativo
retry-wait.description=Imposta l'intervallo di tempo (in secondi) tra i tentativi di ritentativo. Quando impostato su un valore maggiore di 0, aria2 ritenterà quando riceve una risposta 503 dal server HTTP.
server-stat-of.name=Salva stato server
server-stat-of.description=Specifica il nome del file per salvare lo stato del server. È possibile utilizzare il parametro --server-stat-if per leggere i dati salvati.
server-stat-timeout.name=Timeout stato server
server-stat-timeout.description=Specifica il tempo di scadenza dello stato del server (in secondi).
split.name=Connessioni per download
split.description=Utilizza N connessioni per il download. Se vengono forniti più indirizzi URI di N, verranno utilizzati i primi N indirizzi e gli indirizzi rimanenti saranno utilizzati come backup. Se gli indirizzi URI forniti sono meno di N, verranno utilizzati ripetutamente per garantire che siano attive N connessioni contemporaneamente. Il numero di connessioni allo stesso server è limitato dall'opzione --max-connection-per-server.
stream-piece-selector.name=Algoritmo selezione pezzi
stream-piece-selector.description=Specifica l'algoritmo di selezione pezzi per il download HTTP/FTP. I pezzi sono segmenti di lunghezza fissa durante il download parallelo. Se impostato su "predefinito", aria2 selezionerà i pezzi riducendo il numero di connessioni. Poiché la creazione di connessioni è costosa, questo è il comportamento predefinito ragionevole. Se impostato su "sequenziale", aria2 selezionerà i pezzi con l'indice più basso. Un indice di 0 indica il primo pezzo del file. Questo è utile per lo streaming video. L'opzione --enable-http-pipelining contribuisce a ridurre i costi di riconnessione. Si noti che aria2 dipende dall'opzione --min-split-size, quindi è necessario impostare un valore ragionevole per --min-split-size. Se impostato su "casuale", aria2 selezionerà casualmente un pezzo. Come "sequenziale", dipende dall'opzione --min-split-size. Se impostato su "geometrico", aria2 selezionerà prima il pezzo con l'indice più basso, quindi riserverà spazio per i pezzi selezionati in precedenza utilizzando una crescita esponenziale. Ciò ridurrà il numero di connessioni mentre i primi pezzi del file saranno scaricati per primi. Questo è utile anche per lo streaming video.
timeout.name=Timeout
timeout.description=
uri-selector.name=Algoritmo selezione URI
uri-selector.description=Specifica l'algoritmo di selezione URI. I valori possibili includono "sequenziale", "feedback" e "adattivo". Se impostato su "sequenziale", gli URI verranno utilizzati nell'ordine in cui compaiono nell'elenco. Se impostato su "feedback", aria2 selezionerà il server con la velocità di download più rapida dall'elenco di URI, ignorando i mirror non validi. La velocità di download precedentemente misurata sarà parte del file di stato del server, vedere le opzioni --server-stat-of e --server-stat-if. Se impostato su "adattivo", verrà selezionato il miglior mirror e la connessione in sospeso. Si noti che i mirror restituiti non sono stati testati e verranno testati nuovamente solo se tutti i mirror sono già stati testati. Ad esempio, "feedback" utilizza il file di stato del server.
check-certificate.name=Verifica certificato
check-certificate.description=
http-accept-gzip.name=Accetta GZip
http-accept-gzip.description=Se l'intestazione di risposta del server remoto include Content-Encoding: gzip o Content-Encoding: deflate, invia le intestazioni di richiesta che contengono Accept: deflate, gzip e decomprimi la risposta.
http-auth-challenge.name=Sfida autenticazione HTTP
http-auth-challenge.description=Invia intestazioni di richiesta di autenticazione HTTP solo quando richiesto dal server. Se impostato su "no", invierà sempre le intestazioni di richiesta di autenticazione. Eccezione: se il nome utente e la password sono inclusi nell'URI, questa opzione verrà ignorata e le intestazioni di richiesta di autenticazione verranno inviate sempre.
http-no-cache.name=Disabilita cache
http-no-cache.description=Le intestazioni di richiesta inviate conterranno Cache-Control: no-cache e Pragma: no-cache per evitare la memorizzazione nella cache. Se impostato su "no", le suddette intestazioni di richiesta non verranno inviate e l'opzione --header potrà essere utilizzata per aggiungere l'intestazione Cache-Control.
http-user.name=Nome utente HTTP predefinito
http-user.description=
http-passwd.name=Password HTTP predefinita
http-passwd.description=
http-proxy.name=Server proxy HTTP
http-proxy.description=
http-proxy-user.name=Nome utente server proxy HTTP
http-proxy-user.description=
http-proxy-passwd.name=Password server proxy HTTP
http-proxy-passwd.description=
https-proxy.name=Server proxy HTTPS
https-proxy.description=
https-proxy-user.name=Nome utente server proxy HTTPS
https-proxy-user.description=
https-proxy-passwd.name=Password server proxy HTTPS
https-proxy-passwd.description=
referer.name=Referer
referer.description=Imposta il referer della richiesta HTTP (Referer). Questa opzione influisce su tutti i download HTTP/HTTPS. Se impostata su *, il referer verrà impostato sul link di download. Questa opzione può essere utilizzata insieme all'opzione --parameterized-uri.
enable-http-keep-alive.name=Abilita keep-alive HTTP
enable-http-keep-alive.description=Abilita il keep-alive HTTP/1.1.
enable-http-pipelining.name=Abilita il pipelining HTTP
enable-http-pipelining.description=Abilita il pipelining HTTP/1.1.
header.name=Header personalizzati
header.description=Aggiunge il contenuto dell'intestazione di richiesta HTTP. Ogni riga rappresenta un'opzione, contenente "nome intestazione: valore intestazione".
save-cookies.name=Percorso salvataggio cookie
save-cookies.description=Salva i cookie in un file nel formato Mozilla/Firefox(1.x/2.x)/Netscape. Se il file esiste già, verrà sovrascritto. I cookie scaduti verranno comunque salvati, ma il loro tempo di scadenza verrà impostato a 0.
use-head.name=Utilizza metodo HEAD
use-head.description=Utilizza il metodo HEAD durante la prima richiesta al server HTTP
user-agent.name=Agente Utente Personalizzato
user-agent.description=
ftp-user.name=Nome Utente FTP Predefinito
ftp-user.description=
ftp-passwd.name=Password FTP Predefinita
ftp-passwd.description=Se l'URI contiene solo il nome utente senza la password, aria2 cercherà prima la password nel file .netrc. Se trova la password nel file .netrc, la userà. Altrimenti, userà la password impostata con questa opzione.
ftp-pasv.name=Modalità Passiva
ftp-pasv.description=Usa la modalità passiva nell'FTP. Se impostata su "no", verrà utilizzata la modalità attiva. Questa opzione non si applica al trasferimento SFTP.
ftp-proxy.name=Server Proxy FTP
ftp-proxy.description=
ftp-proxy-user.name=Nome Utente Server Proxy FTP
ftp-proxy-user.description=
ftp-proxy-passwd.name=Password Server Proxy FTP
ftp-proxy-passwd.description=
ftp-type.name=Tipo di Trasferimento
ftp-type.description=
ftp-reuse-connection.name=Riutilizzo Connessione
ftp-reuse-connection.description=
ssh-host-key-md.name=Checksum Chiave Pubblica SSH
ssh-host-key-md.description=Imposta il checksum della chiave pubblica SSH del server. Il formato dell'opzione è TIPO=DIGEST. TIPO è il tipo di hash. I tipi di hash supportati sono sha-1 e md5. DIGEST è il digest esadecimale. Ad esempio: sha-1=b030503d4de4539dc7885e6f0f5e256704edf4c3. Questa opzione può essere utilizzata per verificare la chiave pubblica del server quando si utilizza SFTP. Se questa opzione non è impostata, verrà mantenuta la verifica predefinita senza alcuna azione.
bt-detach-seed-only.name=Separare solo i Task di Seed
bt-detach-seed-only.description=Escludi i task di sola seed durante il calcolo dei task di download attivi (vedi opzione -j). Ciò significa che se il parametro è impostato su -j3 e attualmente ci sono 3 task attivi, di cui uno è in modalità seed, verrà escluso (cioè il numero diventerà 2) e il prossimo task in coda verrà avviato. Tuttavia, è importante notare che nei metodi RPC, i task di seed sono comunque considerati attivi.
bt-enable-hook-after-hash-check.name=Abilita Evento di Fine Controllo Hash
bt-enable-hook-after-hash-check.description=Consente l'esecuzione di un comando dopo il completamento del controllo hash dei download BT (vedi opzione -V). Per impostazione predefinita, quando il controllo hash ha successo, verrà eseguito il comando impostato tramite --on-bt-download-complete. Per disabilitare questo comportamento, impostare su "no".
bt-enable-lpd.name=Abilita Discovery Nodi Locali (LPD)
bt-enable-lpd.description=
bt-exclude-tracker.name=Escludi Indirizzi Server BT
bt-exclude-tracker.description=Indirizzi server BT esclusi separati da virgola. È possibile utilizzare * per corrispondere a tutti gli indirizzi, quindi escludere tutti gli indirizzi del server. Quando si utilizza * nella riga di comando della shell, è necessario utilizzare un carattere di escape o le virgolette.
bt-external-ip.name=Indirizzo IP Esterno
bt-external-ip.description=Specifica l'indirizzo IP esterno utilizzato per i download BitTorrent e DHT. Potrebbe essere inviato ai server BitTorrent. Per DHT, questa opzione segnalerà ai nodi locali di essere in fase di download di un determinato torrent. Questo è essenziale per l'uso di DHT in reti private. Anche se si chiama "esterno", accetta vari tipi di indirizzi IP.
bt-force-encryption.name=Forza Crittografia
bt-force-encryption.description=I contenuti dei messaggi BT devono essere crittografati con arc4. Questa opzione è un modo rapido per impostare --bt-require-crypto --bt-min-crypto-level=arc4. Non modifica il contenuto di queste due opzioni. Se impostato su "sì", rifiuterà gli handshake BT precedenti e utilizzerà solo handshake sfocati e messaggi crittografati.
bt-hash-check-seed.name=Controlla Hash Prima del Seed
bt-hash-check-seed.description=Se impostato su "sì", aria2 continuerà il seeding solo dopo aver completato il controllo hash e il completamento del file utilizzando l'opzione --check-integrity. Se si desidera controllare solo i file quando sono danneggiati o incompleti, impostare su "no". Questa opzione è valida solo per i download BT.
bt-load-saved-metadata.name=Carica Metadata Salvati
bt-load-saved-metadata.description=Quando si utilizza il download magnetico, tenta prima di caricare il file salvato con l'opzione --bt-save-metadata prima di scaricare i metadati dal DHT. Se il caricamento del file ha successo, i metadati non verranno scaricati dal DHT.
bt-max-open-files.name=Numero Massimo File Aperti
bt-max-open-files.description=Imposta il numero massimo di file aperti globalmente per i download BT/Metalink.
bt-max-peers.name=Numero Massimo Peer
bt-max-peers.description=Imposta il numero massimo di peer connessi per ogni download BT. 0 significa nessun limite.
bt-metadata-only.name=Scarica Solo Metadati
bt-metadata-only.description=Scarica solo i file torrent. I file descritti nel file torrent non verranno scaricati. Questa opzione è valida solo per i link magnetici.
bt-min-crypto-level.name=Livello Minimo Crittografia
bt-min-crypto-level.description=Imposta il livello minimo della crittografia. Se un peer offre più metodi di crittografia, aria2 selezionerà il livello minimo che soddisfi il livello specificato.
bt-prioritize-piece.name=Priorità Download
bt-prioritize-piece.description=Tenta di scaricare prima i pezzi all'inizio o alla fine di ogni file. Questa opzione è utile per la visualizzazione anticipata dei file. I parametri possono includere due parole chiave: head e tail. Se sono presenti entrambe le parole chiave, devono essere separate da una virgola. Ogni parola chiave può includere un parametro SIZE. Ad esempio, specificando head=SIZE, le prime SIZE dei dati di ciascun file avranno una priorità maggiore. tail=SIZE significa gli ultimi SIZE dati di ciascun file. SIZE può includere K o M (1K=1024, 1M=1024K).
bt-remove-unselected-file.name=Rimuovi File Non Selezionati
bt-remove-unselected-file.description=Dopo il completamento del task BT, rimuove i file non selezionati. Per selezionare i file da scaricare, utilizzare l'opzione --select-file. Se non vengono selezionati file, tutti i file saranno considerati come da scaricare per impostazione predefinita. Questa opzione rimuoverà direttamente i file dal disco, quindi utilizzarla con attenzione.
bt-require-crypto.name=Crittografia Obbligatoria
bt-require-crypto.description=Se impostata su "sì", aria non accetterà gli handshake BitTorrent precedenti (\19protocollo BitTorrent) e stabilirà una connessione solo sfocata. Pertanto, aria2 utilizzerà sempre handshake sfocati.
bt-request-peer-speed-limit.name=Velocità Download Desiderata dai Peer
bt-request-peer-speed-limit.description=Se la velocità di download complessiva di un download BT è inferiore al valore impostato con questa opzione, aria2 aumenterà temporaneamente il numero di connessioni per aumentare la velocità di download. In alcune circostanze, impostare una velocità di download desiderata può migliorare la velocità di download. È possibile aggiungere unità K o M (1K=1024, 1M=1024K).
bt-save-metadata.name=Salva File Torrent
bt-save-metadata.description=Salva il file torrent come file ".torrent". Questa opzione è valida solo per i link magnetici. Il nome del file è l'hash esadecimale codificato in formato esadecimale seguito dall'estensione ".torrent". Viene salvato nella stessa directory dei file scaricati. Se esiste già un file con lo stesso nome, il file torrent non verrà salvato.
bt-seed-unverified.name=Non Verificare File Già Scaricati
bt-seed-unverified.description=Non controlla il valore hash di ogni pezzo dei file scaricati in precedenza.
bt-stop-timeout.name=Timeout di Arresto Automatico Senza Velocità
bt-stop-timeout.description=Quando la velocità di download di un task BT rimane a 0 per il tempo impostato con questa opzione, il download si ferma. Se impostato su 0, questa funzionalità sarà disabilitata.
bt-tracker.name=Indirizzo del Tracker BT
bt-tracker.description=Indirizzi server BT separati da virgola. Questi indirizzi non sono influenzati dall'opzione --bt-exclude-tracker, perché vengono aggiunti solo dopo che l'opzione --bt-exclude-tracker ha escluso altri indirizzi.
bt-tracker-connect-timeout.name=Timeout di connessione al server BT
bt-tracker-connect-timeout.description=Imposta il timeout di connessione al server BT in secondi. Dopo che la connessione è stata stabilita, questa opzione non ha più effetto, utilizzare l'opzione --bt-tracker-timeout.
bt-tracker-interval.name=Intervallo di connessione al server BT
bt-tracker-interval.description=Imposta l'intervallo di richiesta al server BT in secondi. Questa opzione sovrascriverà completamente l'intervallo minimo e l'intervallo restituiti dal server, aria2 utilizzerà solo il valore di questa opzione. Se impostato su 0, aria2 deciderà l'intervallo in base alla risposta del server e all'avanzamento del download.
bt-tracker-timeout.name=Timeout del server BT
bt-tracker-timeout.description=
dht-file-path.name=File DHT (IPv4)
dht-file-path.description=Modifica il percorso del file della tabella di routing DHT IPv4.
dht-file-path6.name=File DHT (IPv6)
dht-file-path6.description=Modifica il percorso del file della tabella di routing DHT IPv6.
dht-listen-port.name=Porta di ascolto DHT
dht-listen-port.description=Imposta la porta UDP utilizzata da DHT (IPv4, IPv6) e dal server UDP. Più porte possono essere separate da virgole ",", ad esempio: 6881,6885. È anche possibile utilizzare un trattino "-" per indicare un intervallo: 6881-6999, oppure entrambi insieme: 6881-6889, 6999.
dht-message-timeout.name=Timeout del messaggio DHT
dht-message-timeout.description=
enable-dht.name=Abilita DHT (IPv4)
enable-dht.description=Abilita la funzione DHT IPv4. Questa opzione abilita anche il supporto del server UDP. Se il torrent è contrassegnato come privato, aria2 non abiliterà DHT anche se questa opzione è impostata su "Sì".
enable-dht6.name=Abilita DHT (IPv6)
enable-dht6.description=Abilita la funzione DHT IPv6. Se il torrent è contrassegnato come privato, aria2 non abiliterà DHT anche se questa opzione è impostata su "Sì". Usa l'opzione --dht-listen-port per impostare la porta di ascolto.
enable-peer-exchange.name=Abilita lo scambio di peer
enable-peer-exchange.description=Abilita l'estensione di scambio di peer. Se il torrent è contrassegnato come privato, aria2 non abiliterà questa funzione anche se questa opzione è impostata su "Sì".
follow-torrent.name=Scarica i file nel torrent
follow-torrent.description=Se impostato su "Sì" o "Solo memoria", quando un file con suffisso .torrent o con tipo di contenuto application/x-bittorrent viene completato, aria2 leggerà e scaricherà i file menzionati nel file torrent. Se impostato su "Solo memoria", il file torrent non verrà scritto sul disco, ma solo memorizzato nella memoria. Se impostato su "No", il file .torrent verrà scaricato sul disco, ma non verrà letto e i file in esso contenuti non verranno scaricati.
listen-port.name=Porta di ascolto
listen-port.description=Imposta la porta TCP per i download BT. Più porte possono essere separate da virgole ",", ad esempio: 6881,6885. È anche possibile utilizzare un trattino "-" per indicare un intervallo: 6881-6999, oppure entrambi insieme: 6881-6889, 6999.
max-overall-upload-limit.name=Velocità massima di upload globale
max-overall-upload-limit.description=Imposta la velocità massima di upload globale in byte/secondo. 0 indica che non ci sono limiti. Puoi aumentare il valore aggiungendo le unità K o M (1K=1024, 1M=1024K).
max-upload-limit.name=Velocità massima di upload
max-upload-limit.description=Imposta la velocità massima di upload per ogni task in byte/secondo. 0 indica che non ci sono limiti. Puoi aumentare il valore aggiungendo le unità K o M (1K=1024, 1M=1024K).
peer-id-prefix.name=Prefisso ID nodo
peer-id-prefix.description=Specifica il prefisso per l'ID del nodo. L'ID del nodo in BT ha una lunghezza di 20 byte. Se supera i 20 byte, verranno utilizzati solo i primi 20 byte. Se è più breve di 20 byte, verranno aggiunti dati casuali per raggiungere i 20 byte.
peer-agent.name=Peer Agent
peer-agent.description=Specifica la stringa utilizzata per la versione del client del nodo durante l'handshake esteso di BT.
seed-ratio.name=Rapporto di condivisione minimo
seed-ratio.description=Specifica il rapporto di condivisione. La condivisione termina quando il rapporto di condivisione raggiunge il valore impostato in questa opzione. Si consiglia vivamente di impostare questa opzione su un valore maggiore o uguale a 1.0. Se non si desidera limitare il rapporto di condivisione, è possibile impostarlo su 0.0. Se si imposta anche l'opzione --seed-time, la condivisione terminerà quando una delle due condizioni sarà soddisfatta.
seed-time.name=Tempo di condivisione minimo
seed-time.description=Specifica il tempo di condivisione in minuti (in formato decimale). Se questa opzione è impostata su 0, la condivisione non avverrà dopo il completamento del download del task BT.
follow-metalink.name=Scarica i file nel Metalink
follow-metalink.description=Se impostato su "Sì" o "Solo memoria", quando un file con suffisso .meta4 o .metalink o con tipo di contenuto application/metalink4+xml o application/metalink+xml viene completato, aria2 leggerà e scaricherà i file menzionati nel file Metalink. Se impostato su "Solo memoria", il file Metalink non verrà scritto sul disco, ma solo memorizzato nella memoria. Se impostato su "No", il file .metalink verrà scaricato sul disco, ma non verrà letto e i file in esso contenuti non verranno scaricati.
metalink-base-uri.name=URI di base
metalink-base-uri.description=Specifica l'URI di base per risolvere gli indirizzi URI relativi metalink:url e metalink:metaurl nei file Metalink memorizzati sul disco locale. Se l'URI rappresenta una directory, deve terminare con /.
metalink-language.name=Lingua
metalink-language.description=
metalink-location.name=Posizione del server preferita
metalink-location.description=La posizione preferita del server. Puoi utilizzare un elenco separato da virgole, ad esempio: jp,us.
metalink-os.name=Sistema operativo
metalink-os.description=Il sistema operativo del file da scaricare.
metalink-version.name=Numero di versione
metalink-version.description=Il numero di versione del file da scaricare.
metalink-preferred-protocol.name=Protocollo preferito
metalink-preferred-protocol.description=Specifica il protocollo preferito da utilizzare. Può essere impostato su http, https, ftp o "no". Se impostato su "no", questa opzione viene disattivata.
metalink-enable-unique-protocol.name=Usa solo un protocollo univoco
metalink-enable-unique-protocol.description=Se un file Metalink è disponibile su più protocolli e questa opzione è impostata su "Sì", aria2 ne utilizzerà solo uno. Usa il parametro --metalink-preferred-protocol per specificare il protocollo preferito.
enable-rpc.name=Abilita il server JSON-RPC/XML-RPC
enable-rpc.description=
pause-metadata.name=Sospendi dopo il download del file torrent
pause-metadata.description=Sospende i download successivi dopo il download del file torrent. In aria2, ci sono 3 tipi di download di file torrent: (1) Download di file .torrent. (2) File torrent scaricati tramite magnet link. (3) Download di file Metalink. Questi file torrent, una volta scaricati, continueranno a scaricare in base al contenuto del file. Questa opzione metterà in pausa questi download successivi. Questa opzione è efficace solo se l'opzione --enable-rpc è abilitata.
rpc-allow-origin-all.name=Accetta tutte le richieste remote
rpc-allow-origin-all.description=Aggiunge il campo Access-Control-Allow-Origin all'intestazione di risposta RPC con il valore *.
rpc-listen-all.name=Ascolta su tutte le schede di rete
rpc-listen-all.description=Ascolta le richieste JSON-RPC/XML-RPC su tutte le schede di rete, se impostato su "No", ascolta solo le richieste dalla rete locale.
rpc-listen-port.name=Porta di ascolto
rpc-listen-port.description=
rpc-max-request-size.name=Dimensione massima della richiesta
rpc-max-request-size.description=Imposta la dimensione massima della richiesta JSON-RPC/XML-RPC. Se aria2 rileva che la richiesta supera il numero di byte impostato, la connessione verrà chiusa direttamente.
rpc-save-upload-metadata.name=Salva i file torrent caricati
rpc-save-upload-metadata.description=Salva i file torrent o Metalink caricati nella directory specificata dall'opzione dir. Il nome del file è composto da metadati hash SHA-1 e da un'estensione. Per i file torrent, l'estensione è '.torrent'. Per Metalink è '.meta4'. Se questa opzione è impostata su "No", i download aggiunti tramite i metodi aria2.addTorrent() o aria2.addMetalink() non possono essere salvati con l'opzione --save-session.
rpc-secure.name=Abilita SSL/TLS
rpc-secure.description=RPC verrà trasmesso tramite crittografia SSL/TLS. Il client RPC deve utilizzare il protocollo https per connettersi al server. Per i client WebSocket, utilizzare il protocollo wss. Usa le opzioni --rpc-certificate e --rpc-private-key per impostare il certificato e la chiave privata del server.
allow-overwrite.name=Consenti la sovrascrittura
allow-overwrite.description=Riscarica il file dall'inizio se il file di controllo corrispondente non esiste. Vedere l'opzione --auto-file-renaming.
allow-piece-length-change.name=Consenti la modifica della lunghezza del pezzo
allow-piece-length-change.description=Se impostato su "No", aria2 interromperà il download se la lunghezza del pezzo è diversa da quella nel file di controllo. Se impostato su "Sì", puoi continuare, ma parte della progressione del download verrà persa.
always-resume.name=Ripristina sempre
always-resume.description=Ripristina sempre il download interrotto. Se impostato su "Sì", aria2 tenta sempre di ripristinare il download interrotto, in caso contrario interrompe il download. Se impostato su "No", per URI che non supportano il download interrotto o aria2 incontra N URI che non supportano il download interrotto (N è il valore impostato dall'opzione --max-resume-failure-tries), aria2 scaricherà il file dall'inizio. Vedere il parametro --max-resume-failure-tries.
async-dns.name=DNS asincrono
async-dns.description=
auto-file-renaming.name=Ridenominazione automatica dei file
auto-file-renaming.description=Rinomina i file
auto-save-interval.name=Intervallo di salvataggio automatico
auto-save-interval.description=Salva automaticamente il file di controllo (*.aria2) ogni tot secondi specificati. Se impostato su 0, il file di controllo non verrà salvato automaticamente durante il download. Indipendentemente dal valore impostato, aria2 salverà il file di controllo al termine del task. Il valore può essere impostato da 0 a 600.
conditional-get.name=Download condizionale
conditional-get.description=Scarica il file solo se è più vecchio del file locale. Questa funzione funziona solo per i download HTTP(S). Se la dimensione del file è già stata specificata in Metalink, la funzione non avrà effetto. Inoltre, questa funzione ignorerà l'intestazione di risposta Content-Disposition. Se esiste un file di controllo, questa opzione verrà ignorata. Questa funzione utilizza l'intestazione di richiesta If-Modified-Since per ottenere il file più recente. Quando viene recuperato il tempo di modifica del file locale, questa funzione utilizzerà il nome del file fornito dall'utente (vedere l'opzione --out), oppure il nome del file nell'URI se l'opzione --out non è specificata. Per sovrascrivere un file esistente, è necessario utilizzare il parametro --allow-overwrite.
conf-path.name=Percorso del file di configurazione
conf-path.description=
console-log-level.name=Livello di log della console
console-log-level.description=
content-disposition-default-utf8.name=Usa UTF-8 per elaborare Content-Disposition
content-disposition-default-utf8.description=Usa il set di caratteri UTF-8 invece di ISO-8859-1 per elaborare la stringa nell'intestazione "Content-Disposition", ad esempio, il parametro del nome file, ma non il nome file della versione estesa.
daemon.name=Abilita il processo in background
daemon.description=
deferred-input.name=Caricamento ritardato
deferred-input.description=Se impostato su "Sì", aria2 non leggerà tutti gli indirizzi URI nel file specificato dall'opzione --input-file all'avvio, ma li leggerà solo quando necessario. Se il file di input contiene un gran numero di URI da scaricare, questa opzione può ridurre l'utilizzo della memoria. Se impostato su "No", aria2 leggerà tutti gli URI all'avvio. L'opzione --deferred-input verrà disattivata quando si utilizza --save-session.
disable-ipv6.name=Disabilita IPv6
disable-ipv6.description=
disk-cache.name=Cache disco
disk-cache.description=Abilita la cache disco. Se impostato su 0, la cache disco verrà disattivata. Questa funzione memorizza nella cache i dati scaricati nella memoria, fino a un massimo di byte specificati in questa opzione. La memoria cache di archiviazione viene creata dall'istanza aria2 e condivisa da tutti i download. Poiché i dati vengono scritti in unità più grandi e riordinati in base all'offset del file, un vantaggio della cache disco è la riduzione dell'I/O del disco. Se viene richiamato il controllo dell'hash e i dati sono memorizzati nella cache in memoria, non sarà necessario leggerli dal disco. La dimensione può includere K o M (1K=1024, 1M=1024K).
download-result.name=Risultato del download
download-result.description=Questa opzione modifica il formato del risultato del download. Se impostato su "Default", stamperà GID, stato, velocità media di download e percorso/URI. Se sono coinvolti più file, verrà stampato solo il percorso/URI del primo file richiesto, gli altri verranno ignorati. Se impostato su "Completo", stamperà GID, stato, velocità media di download, avanzamento del download e percorso/URI. In questo caso, l'avanzamento del download e il percorso/URI verranno stampati su una riga per ciascun file. Se impostato su "Nascosto", il risultato del download verrà nascosto.
dscp.name=DSCP
dscp.description=Imposta il valore DSCP del campo TOS nei pacchetti IP in uscita BT per QoS. Questo parametro imposta solo i bit DSCP del campo TOS, non l'intero campo. Se si ottengono valori da /usr/include/netinet/ip.h, è necessario dividerli per 4 (altrimenti i valori saranno errati, ad esempio la classe CS1 diventerà CS4). Se si utilizzano valori comuni da RFC, documentazione del fornitore di rete, Wikipedia o altre fonti, è possibile utilizzarli direttamente.
rlimit-nofile.name=Numero massimo di file descrittori aperti
rlimit-nofile.description=Imposta il soft limit per il numero di file descrittori aperti. Questa opzione è efficace solo se: a. Il sistema la supporta (POSIX). b. Il limite non supera l'hard limit. c. Il limite specificato è maggiore del soft limit corrente. Questo è equivalente all'impostazione di ulimit, tranne per il fatto che non può abbassare il limite. Questa opzione è efficace solo se il sistema supporta l'API rlimit.
enable-color.name=Usa colori nell'output del terminale
enable-color.description=
enable-mmap.name=Abilita MMap
enable-mmap.description=Memorizza i file mappati in memoria. Se lo spazio del file non è preallocato, questa opzione non è valida. Vedere --file-allocation.
event-poll.name=Metodo di polling degli eventi
event-poll.description=Imposta il metodo di polling degli eventi. I valori possibili includono epoll, kqueue, port, poll e select. Per epoll, kqueue, port e poll, sono disponibili solo se il sistema li supporta. La maggior parte delle distribuzioni Linux supporta epoll. Vari sistemi *BSD, incluso Mac OS X, supportano kqueue. Open Solaris supporta la porta. Il valore predefinito varia a seconda del sistema operativo utilizzato.
file-allocation.name=Metodo di allocazione file
file-allocation.description=Specifica il metodo di allocazione dei file. "Nessuno" non alloca preventivamente lo spazio del file. "Prealloc" allocherà lo spazio prima dell'inizio del download. Questo richiederà del tempo in base alle dimensioni del file. Se si utilizza un file system più recente, come ext4 (con supporto esteso), btrfs, xfs o NTFS (solo build MinGW), "falloc" è la scelta migliore. Può allocare file di grandi dimensioni (numerosi GiB) quasi istantaneamente. Non utilizzare falloc su file system legacy, come ext3 e FAT32, poiché richiede lo stesso tempo di prealloc e blocca aria2 fino al completamento dell'allocazione. falloc potrebbe non essere disponibile se il sistema non supporta la funzione posix_fallocate(3). "Trunc" utilizza la chiamata di sistema ftruncate(2) o un'implementazione specifica della piattaforma per troncare un file a una lunghezza specifica. Nei download BitTorrent con più file, se un file condivide le stesse sezioni con un file adiacente, anche i file adiacenti verranno allocati.
force-save.name=Forza il salvataggio
force-save.description=Salva l'attività anche se è stata completata o eliminata quando si utilizza l'opzione --save-session. In questo caso, questa opzione salverà anche il file di controllo. Questa opzione può salvare le attività BT che sono considerate completate ma che stanno ancora seedando.
save-not-found.name=Salva i file non trovati
save-not-found.description=Quando si utilizza l'opzione --save-session, salva l'attività di download anche se i file nell'attività non esistono. Questa opzione salverà anche questa situazione nel file di controllo.
hash-check-only.name=Solo controllo hash
hash-check-only.description=Se impostato su "Sì", termina il download in base al completamento del download dopo aver eseguito il controllo hash utilizzando l'opzione --check-integrity.
human-readable.name=Output leggibile in console
human-readable.description=Stampa dimensioni e velocità in un formato leggibile in console (ad esempio, 1.2Ki, 3.4Mi).
keep-unfinished-download-result.name=Conserva i risultati delle attività non completate
keep-unfinished-download-result.description=Conserva tutti i risultati delle attività di download non completate, anche se superano il numero impostato dall'opzione --max-download-result. Questo può aiutare a conservare tutti i download non completati nel file di sessione (vedere l'opzione --save-session). È importante notare che non c'è un limite al numero di attività non completate. Se non lo si desidera, disattivare questa opzione.
max-download-result.name=Numero massimo di risultati di download
max-download-result.description=Imposta il numero massimo di risultati di download da memorizzare in memoria. I risultati del download includono download completati/con errori/eliminati. I risultati del download vengono memorizzati in una coda FIFO, quindi è possibile memorizzare al massimo il numero specificato di risultati del download. Quando la coda è piena e viene creato un nuovo risultato di download, il risultato di download più vecchio viene rimosso dalla parte anteriore della coda e il nuovo viene inserito alla fine. Impostare un valore elevato per questa opzione può portare a un consumo elevato di memoria se si eseguono migliaia di download. Impostare su 0 per non memorizzare i risultati del download. Si noti che i download non completati saranno sempre conservati in memoria, indipendentemente dall'impostazione di questa opzione. Vedere l'opzione --keep-unfinished-download-result.
max-mmap-limit.name=Limite massimo di MMap
max-mmap-limit.description=Imposta la dimensione massima del file per abilitare MMap (vedere l'opzione --enable-mmap). La dimensione del file è determinata dalla somma di tutte le dimensioni dei file in un'attività di download. Ad esempio, se un download contiene 5 file, la dimensione del file sarà la dimensione totale di questi file. Se la dimensione del file supera la dimensione impostata per questa opzione, MMap verrà disattivato.
max-resume-failure-tries.name=Numero massimo di tentativi di ripresa di download interrotti
max-resume-failure-tries.description=Quando l'opzione --always-resume è impostata su "No", se aria2 rileva che N URI non supportano la ripresa di download interrotti, il download del file inizierà dall'inizio. Se N è impostato su 0, il download del file inizierà dall'inizio solo se tutti gli URI non supportano la ripresa di download interrotti. Vedere l'opzione --always-resume.
min-tls-version.name=Versione minima TLS
min-tls-version.description=Specifica la versione minima di SSL/TLS abilitata.
log-level.name=Livello di log
log-level.description=
optimize-concurrent-downloads.name=Ottimizza i download simultanei
optimize-concurrent-downloads.description=Ottimizza il numero di download simultanei in base alla larghezza di banda disponibile. aria2 utilizza la velocità di download precedentemente misurata per ottenere il numero di download simultanei tramite la regola N=A + B Log10 (la velocità è in Mbps). I coefficienti A e B possono essere personalizzati separati da due punti nel parametro. Il valore predefinito (A=5, B=25) può utilizzare 5 download simultanei su una rete da 1 Mbps e 50 su una rete da 100 Mbps. Il numero di download simultanei è limitato al massimo definito dal parametro --max-concurrent-downloads.
piece-length.name=Dimensione del blocco file
piece-length.description=Imposta la dimensione di allocazione per i download HTTP/FTP. aria2 divide i file in base a questo limite. Tutte le sezioni saranno multipli di questa lunghezza. Questa opzione non è valida per i download BitTorrent.
show-console-readout.name=
show-console-readout.description=
summary-interval.name=Intervallo di output del riepilogo del download
summary-interval.description=Imposta l'intervallo di output del riepilogo dell'avanzamento del download (in secondi). Impostare su 0 per disattivare l'output.
max-overall-download-limit.name=Velocità massima di download globale
max-overall-download-limit.description=Imposta la velocità massima di download globale (byte/secondo). 0 indica nessuna restrizione. È possibile aumentare il valore con le unità K o M (1K=1024, 1M=1024K).
max-download-limit.name=Velocità massima di download
max-download-limit.description=Imposta la velocità massima di download per ogni attività (byte/secondo). 0 indica nessuna restrizione. È possibile aumentare il valore con le unità K o M (1K=1024, 1M=1024K).
no-conf.name=Disabilita il file di configurazione
no-conf.description=
no-file-allocation-limit.name=Limite di allocazione file
no-file-allocation-limit.description=Non confrontare i file allocati con questo limite di dimensione. È possibile aumentare il valore con le unità K o M (1K=1024, 1M=1024K).
parameterized-uri.name=Abilita il supporto URI parametrizzato
parameterized-uri.description=Abilita il supporto URI parametrizzato. È possibile specificare un insieme di parti: http://{sv1,sv2,sv3}/foo.iso. È anche possibile utilizzare un contatore a passi per specificare sequenze numerizzate: http://host/image[000-100:2].img. Il contatore a passi è facoltativo. Se tutti gli URI non puntano allo stesso file, come nel secondo esempio sopra, è necessario utilizzare l'opzione -Z.
quiet.name=Disabilita l'output della console
quiet.description=
realtime-chunk-checksum.name=Controllo integrità dati in tempo reale
realtime-chunk-checksum.description=Se viene fornita la checksum del blocco dati, verifica i blocchi dati con la checksum durante il download.
remove-control-file.name=Rimuovi file di controllo
remove-control-file.description=Rimuove il file di controllo prima del download. In combinazione con l'opzione --allow-overwrite=true, il download del file inizierà sempre dall'inizio. Questa opzione può essere utile per gli utenti che utilizzano proxy che non supportano la ripresa di download interrotti.
save-session.name=File di salvataggio sessione
save-session.description=Salva i download con errori e non completati in un file specificato all'uscita. Puoi ricaricarli usando l'opzione --input-file quando riavvii aria2. Se desideri comprimere l'output con GZip, puoi aggiungere l'estensione .gz al nome del file. Si noti che i metadati dei download aggiunti tramite i metodi RPC aria2.addTorrent() e aria2.addMetalink() non verranno salvati se non vengono salvati nel file. I download eliminati con aria2.remove() e aria2.forceRemove() non verranno salvati.
save-session-interval.name=Intervallo di salvataggio sessione
save-session-interval.description=Salva i download con errori o non completati nel file specificato dall'opzione --save-session ogni tot secondi. Se impostato su 0, la sessione verrà salvata solo quando aria2 esce.
socket-recv-buffer-size.name=Dimensione buffer di ricezione socket
socket-recv-buffer-size.description=Imposta la dimensione massima del buffer di ricezione socket in byte. Se impostato su 0, questa opzione viene disattivata. Il valore di questa opzione viene impostato sul descrittore del file socket quando si chiama setsockopt() con l'opzione SO_RCVBUF.
stop.name=Tempo di arresto automatico
stop.description=
truncate-console-readout.name=Tronca l'output della console
truncate-console-readout.description=Tronca l'output della console su una singola riga.
