#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成aria2的腾讯会议文件下载器
支持使用aria2进行高速下载
"""

import json
import requests
import os
import time
import subprocess
from datetime import datetime
from tencent_meeting_downloader import TencentMeetingDownloader

class Aria2Downloader:
    def __init__(self, aria2_rpc_url="http://localhost:6800/jsonrpc", secret=None):
        """
        初始化aria2下载器
        :param aria2_rpc_url: aria2 RPC服务地址
        :param secret: aria2 RPC密钥（如果设置了的话）
        """
        self.rpc_url = aria2_rpc_url
        self.secret = secret
        self.session = requests.Session()
        
    def _call_aria2(self, method, params=None):
        """
        调用aria2 RPC方法
        """
        if params is None:
            params = []
            
        # 如果设置了密钥，添加到参数前面
        if self.secret:
            params.insert(0, f"token:{self.secret}")
            
        payload = {
            "jsonrpc": "2.0",
            "id": "1",
            "method": method,
            "params": params
        }
        
        try:
            response = self.session.post(self.rpc_url, json=payload, timeout=10)
            result = response.json()
            
            if "error" in result:
                print(f"aria2 RPC错误: {result['error']}")
                return None
                
            return result.get("result")
        except Exception as e:
            print(f"调用aria2 RPC失败: {str(e)}")
            return None
    
    def check_aria2_status(self):
        """
        检查aria2是否运行
        """
        try:
            version = self._call_aria2("aria2.getVersion")
            if version:
                print(f"✅ aria2已连接，版本: {version.get('version', 'unknown')}")
                return True
            else:
                print("❌ 无法连接到aria2")
                return False
        except:
            print("❌ aria2连接失败")
            return False
    
    def add_download(self, url, output_dir, filename, headers=None):
        """
        添加下载任务到aria2
        :param url: 下载链接
        :param output_dir: 输出目录（绝对路径）
        :param filename: 文件名
        :param headers: 请求头
        :return: 下载任务ID
        """
        # 确保使用绝对路径
        abs_output_dir = os.path.abspath(output_dir)

        options = {
            "dir": abs_output_dir,
            "out": filename,
            "max-connection-per-server": "16",
            "split": "16",
            "min-split-size": "1M",
            "continue": "true",
            "max-tries": "3",
            "retry-wait": "3"
        }
        
        # 添加请求头
        if headers:
            header_list = []
            for key, value in headers.items():
                header_list.append(f"{key}: {value}")
            options["header"] = header_list
        
        params = [[url], options]
        gid = self._call_aria2("aria2.addUri", params)
        
        if gid:
            print(f"✅ 已添加下载任务: {filename} (GID: {gid})")
            print(f"📁 下载目录: {abs_output_dir}")
            return gid
        else:
            print(f"❌ 添加下载任务失败: {filename}")
            return None
    
    def get_download_status(self, gid):
        """
        获取下载状态
        """
        return self._call_aria2("aria2.tellStatus", [gid])
    
    def wait_for_download(self, gid, timeout=300):
        """
        等待下载完成
        :param gid: 下载任务ID
        :param timeout: 超时时间（秒）
        :return: 是否成功
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = self.get_download_status(gid)
            if not status:
                return False
                
            state = status.get("status")
            
            if state == "complete":
                print(f"✅ 下载完成: {status.get('files', [{}])[0].get('path', 'unknown')}")
                return True
            elif state == "error":
                print(f"❌ 下载失败: {status.get('errorMessage', 'unknown error')}")
                return False
            elif state in ["active", "waiting", "paused"]:
                # 显示进度
                completed = int(status.get("completedLength", 0))
                total = int(status.get("totalLength", 0))
                if total > 0:
                    progress = (completed / total) * 100
                    speed = status.get("downloadSpeed", "0")
                    print(f"📥 下载中: {progress:.1f}% ({self._format_size(completed)}/{self._format_size(total)}) 速度: {self._format_speed(speed)}")
                
                time.sleep(2)
            else:
                print(f"⏸️ 下载状态: {state}")
                time.sleep(2)
        
        print(f"⏰ 下载超时: {timeout}秒")
        return False
    
    def _format_size(self, size_bytes):
        """格式化文件大小"""
        try:
            size = int(size_bytes)
            for unit in ['B', 'KB', 'MB', 'GB']:
                if size < 1024:
                    return f"{size:.1f}{unit}"
                size /= 1024
            return f"{size:.1f}TB"
        except:
            return "0B"
    
    def _format_speed(self, speed_bytes):
        """格式化下载速度"""
        try:
            speed = int(speed_bytes)
            return f"{self._format_size(speed)}/s"
        except:
            return "0B/s"


class TencentMeetingAria2Downloader(TencentMeetingDownloader):
    """
    集成aria2的腾讯会议下载器
    """
    
    def __init__(self, cookie, aria2_rpc_url="http://localhost:6800/jsonrpc", aria2_secret=None):
        super().__init__(cookie)
        self.aria2 = Aria2Downloader(aria2_rpc_url, aria2_secret)
        
    def download_file_with_aria2(self, url, filepath, filename=None):
        """
        使用aria2下载文件
        :param url: 下载链接
        :param filepath: 保存路径
        :param filename: 文件名（可选）
        :return: 是否成功
        """
        if not self.aria2.check_aria2_status():
            print("❌ aria2未运行，回退到普通下载")
            return self.download_file(url, filepath, filename)
        
        if filename:
            full_filename = filename
        else:
            # 从URL中提取文件名
            import urllib.parse
            parsed_url = urllib.parse.urlparse(url)
            full_filename = os.path.basename(parsed_url.path)
            if not full_filename:
                full_filename = "download_file"
        
        # 确保目录存在
        os.makedirs(filepath, exist_ok=True)
        
        print(f"🚀 使用aria2下载: {full_filename}")
        
        # 准备请求头
        download_headers = {
            'User-Agent': self.headers['User-Agent'],
            'Referer': 'https://meeting.tencent.com/'
        }
        
        # 添加下载任务
        gid = self.aria2.add_download(url, filepath, full_filename, download_headers)
        
        if gid:
            # 等待下载完成
            return self.aria2.wait_for_download(gid, timeout=600)  # 10分钟超时
        else:
            print("❌ 添加aria2下载任务失败，回退到普通下载")
            return self.download_file(url, filepath, filename)
    
    def download_single_meeting(self, meeting):
        """
        使用aria2下载单个会议的所有文件
        """
        start_time = meeting.get("start_time")
        title = meeting.get("title", "未知会议")
        record_id = meeting.get("record_id")
        uni_record_id = meeting.get("uni_record_id")
        meeting_id = meeting.get("meeting_info", {}).get("meeting_id")
        record_type = meeting.get("record_type")

        print(f"\n🎯 开始处理会议: {title}")
        print(f"📅 开始时间: {start_time}")
        print(f"📝 记录类型: {record_type}")

        # 创建会议目录
        meeting_dir = self.create_meeting_directory(start_time, title)

        # 生成日期前缀（用于文件命名）
        from datetime import datetime
        dt = datetime.fromtimestamp(int(start_time) / 1000)
        date_prefix = dt.strftime("%Y%m%d_%H%M%S")
        
        download_tasks = []
        
        # 下载视频和音频文件
        if record_type in ["cloud_record", "fast_record"]:
            print("🎬 获取视频下载链接...")
            video_links = self.get_video_download_urls(uni_record_id)
            
            for i, link_info in enumerate(video_links):
                # 下载视频
                video_url = link_info.get("link")
                video_filename = link_info.get("filename")
                if video_url and video_filename:
                    success = self.download_file_with_aria2(video_url, meeting_dir, video_filename)
                    download_tasks.append(("视频", video_filename, success))
                
                # 下载音频
                audio_url = link_info.get("audio_link")
                audio_filename = link_info.get("audio_filename")
                if audio_url and audio_filename:
                    # 添加日期前缀和文件扩展名
                    if not audio_filename.endswith('.m4a'):
                        audio_filename += '.m4a'
                    # 添加日期前缀
                    audio_filename_with_date = f"{date_prefix}_{audio_filename}"
                    success = self.download_file_with_aria2(audio_url, meeting_dir, audio_filename_with_date)
                    download_tasks.append(("音频", audio_filename_with_date, success))
        
        # 下载文档文件（转写记录等）
        if record_type in ["realtime_transcription", "cloud_record"]:
            print("📄 获取文档下载链接...")
            doc_urls = self.get_document_download_urls(
                meeting_id, record_id, uni_record_id, 
                ['word', 'pdf', 'txt']
            )
            
            for doc_type, urls in doc_urls.items():
                for i, url in enumerate(urls):
                    # 生成文件名（添加日期前缀）
                    filename = f"{date_prefix}_{title}_{doc_type}"
                    if len(urls) > 1:
                        filename += f"_{i+1}"

                    # 添加文件扩展名
                    if doc_type == 'word':
                        filename += '.docx'
                    elif doc_type == 'pdf':
                        filename += '.pdf'
                    elif doc_type == 'txt':
                        filename += '.txt'

                    success = self.download_file_with_aria2(url, meeting_dir, filename)
                    download_tasks.append(("文档", filename, success))
        
        # 显示下载结果
        print(f"\n📊 会议 {title} 下载结果:")
        success_count = 0
        for task_type, filename, success in download_tasks:
            status = "✅" if success else "❌"
            print(f"  {status} {task_type}: {filename}")
            if success:
                success_count += 1
        
        print(f"📈 成功下载: {success_count}/{len(download_tasks)} 个文件")
        print(f"📁 保存位置: {meeting_dir}")


def start_aria2_if_needed():
    """
    检查并启动aria2（如果需要）
    """
    try:
        # 检查aria2是否已经运行
        aria2_test = Aria2Downloader()
        if aria2_test.check_aria2_status():
            return True
        
        print("🔄 尝试启动aria2...")
        
        # 尝试启动aria2
        aria2_cmd = [
            "aria2c",
            "--enable-rpc",
            "--rpc-listen-all=false",
            "--rpc-listen-port=6800",
            "--max-connection-per-server=16",
            "--split=16",
            "--min-split-size=1M",
            "--max-concurrent-downloads=5",
            "--continue=true",
            "--daemon=true"
        ]
        
        subprocess.run(aria2_cmd, check=True, capture_output=True)
        
        # 等待aria2启动
        time.sleep(2)
        
        if aria2_test.check_aria2_status():
            print("✅ aria2启动成功")
            return True
        else:
            print("❌ aria2启动失败")
            return False
            
    except FileNotFoundError:
        print("❌ 未找到aria2程序，请确保aria2已安装并在PATH中")
        return False
    except Exception as e:
        print(f"❌ 启动aria2失败: {str(e)}")
        return False
