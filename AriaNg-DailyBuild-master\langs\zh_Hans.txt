[global]
AriaNg Version=AriaNg 版本
Operation Result=操作结果
Operation Succeeded=操作成功
is connected=已连接
Error=错误
OK=确定
Confirm=确认
Cancel=取消
Close=关闭
True=是
False=否
DEBUG=调试 (Debug)
INFO=普通 (Info)
WARN=警告 (Warn)
ERROR=错误 (Error)
Connecting=连接中
Connected=已连接
Disconnected=未连接
Reconnecting=重连中
Waiting to reconnect=等待重连
Global=全局
New=新建
Start=开始任务
Pause=暂停任务
Retry=重试
Retry Selected Tasks=重试选中的任务
Delete=删除任务
Select All=全部选中
Select None=全部不选
Select Invert=反向选择
Select All Failed Tasks=全选失败的任务
Select All Completed Tasks=全选已完成的任务
Select All Tasks=全部选中任务
Display Order=显示顺序
Copy Download Url=复制下载链接
Copy Magnet Link=复制磁力链接
Help=帮助
Search=搜索
Default=默认
Expand=展开
Collapse=折叠
Expand All=全部展开
Collapse All=全部折叠
Open=打开
Save=保存
Import=导入
Remove Task=删除任务
Remove Selected Task=删除选中的任务
Clear Stopped Tasks=清空已结束任务
Click to view task detail=点击查看任务详情
By File Name=按文件名
By File Size=按文件大小
By Progress=按进度
By Selected Status=按选中状态
By Remaining=按剩余时间
By Download Speed=按下载速度
By Upload Speed=按上传速度
By Peer Address=按节点地址
By Client Name=按客户端名称
Filters=过滤器
Download=下载
Upload=上传
Downloading=正在下载
Pending Verification=等待验证
Verifying=正在验证
Seeding=正在做种
Waiting=正在等待
Paused=已暂停
Completed=已完成
Error Occurred=发生错误
Removed=已删除
Finished / Stopped=已完成 / 已停止
Uncompleted=未完成
Click to pin=点击固定
Settings=系统设置
AriaNg Settings=AriaNg 设置
Aria2 Settings=Aria2 设置
Basic Settings=基本设置
HTTP/FTP/SFTP Settings=HTTP/FTP/SFTP 设置
HTTP Settings=HTTP 设置
FTP/SFTP Settings=FTP/SFTP 设置
BitTorrent Settings=BitTorrent 设置
Metalink Settings=Metalink 设置
RPC Settings=RPC 设置
Advanced Settings=高级设置
AriaNg Debug Console=AriaNg 调试控制台
Aria2 Status=Aria2 状态
File Name=文件名
File Size=大小
Progress=进度
Share Ratio=分享率
Remaining=剩余时间
Download Speed=下载速度
Upload Speed=上传速度
Links=链接
Torrent File=种子文件
Metalink File=Metalink 文件
File Name:=文件名:
Options=选项
Overview=总览
Pieces=区块信息
Files=文件列表
Peers=连接状态
Task Name=任务名称
Task Size=任务大小
Task Status=任务状态
Error Description=错误描述
Health Percentage=健康度
Info Hash=特征值
Seeders=种子数
Connections=连接数
Seed Creation Time=种子创建时间
Download Url=下载地址
Download Dir=下载路径
BT Tracker Servers=BT 服务器
Copy=复制
(Choose Files)=(选择文件)
Videos=视频
Audios=音频
Pictures=图片
Documents=文档
Applications=应用程序
Archives=存档文件
Other=其他
Custom=自定义
Custom Choose File=自定义选择文件
Address=地址
Client=客户端
Status=状态
Speed=速度
(local)=(本机)
No Data=无数据
No connected peers=没有连接到其他节点
Failed to change some tasks state.=修改一些任务状态时失败.
Confirm Retry=确认重试
Are you sure you want to retry the selected task? AriaNg will create same task after clicking OK.=您是否要重试选中的任务? 点击 "确定" 后, AriaNg 将会创建相同的任务.
Failed to retry this task.=该任务重试失败.
{successCount} tasks have been retried and {failedCount} tasks are failed.={{successCount}} 个任务重试成功以及 {{failedCount}} 个任务失败.
Confirm Remove=确认删除
Are you sure you want to remove the selected task?=您是否要删除选中的任务?
Failed to remove some task(s).=删除一些任务时失败.
Confirm Clear=确认清除
Are you sure you want to clear stopped tasks?=您是否要清除已结束的任务?
Download Links:=下载链接:
Download Now=立即下载
Download Later=手动下载
Open Torrent File=打开种子文件
Open Metalink File=打开 Metalink 文件
Support multiple URLs, one URL per line.=支持多个 URL 地址, 每个地址占一行.
Your browser does not support loading file!=您的浏览器不支持加载文件!
The selected file type is invalid!=选择的文件类型无效!
Failed to load file!=加载文件失败!
Download Completed=下载完成
BT Download Completed=BT 下载完成
Download Error=下载出错
AriaNg Url=AriaNg 地址
Command API Url=命令行 API 地址
Export Command API=导出命令行 API
Export=导出
Copied=已复制
Pause After Task Created=任务创建后暂停
Language=语言
Theme=主题
Light=浅色
Dark=深色
Follow system settings=跟随系统设置
Debug Mode=调试模式
Page Title=页面标题
Preview=预览
Tips: You can use the "noprefix" tag to ignore the prefix, "nosuffix" tag to ignore the suffix, and "scale\=n" tag to set the decimal precision.=小提示: 您可以使用 "noprefix" 标签忽略前缀, "nosuffix" 标签忽略后缀, 以及 "scale\=n" 标签设置小数的精度.
Example: ${downspeed:noprefix:nosuffix:scale\=1}=示例: ${downspeed:noprefix:nosuffix:scale\=1}
Updating Page Title Interval=页面标题更新间隔
Enable Browser Notification=启用浏览器通知
Browser Notification Sound=浏览器通知声音
Browser Notification Frequency=浏览器通知频次
Unlimited=无限制
High (Up to 10 Notifications / 1 Minute)=高 (最多 10 条通知 / 每分钟)
Middle (Up to 1 Notification / 1 Minute)=中 (最多 1 条通知 / 每分钟)
Low (Up to 1 Notification / 5 Minutes)=低 (最多 1 条通知 / 每5分钟)
WebSocket Auto Reconnect Interval=WebSocket 自动重连时间
Aria2 RPC Alias=Aria2 RPC 别名
Aria2 RPC Address=Aria2 RPC 地址
Aria2 RPC Protocol=Aria2 RPC 协议
Aria2 RPC Http Request Method=Aria2 RPC Http 请求方法
POST method only supports aria2 v1.15.2 and above.=POST 方法仅支持 aria2 v1.15.2 及以上.
Aria2 RPC Request Headers=Aria2 RPC 请求头
Support multiple request headers, one header per line, each line containing "header name: header value".=支持多个请求头, 每个请求头占一行, 每行包含 "请求头名: 请求头值".
Aria2 RPC Secret Token=Aria2 RPC 密钥
Activate=激活
Reset Settings=重置设置
Confirm Reset=确认重置
Are you sure you want to reset all settings?=您是否要重置所有设置?
Clear Settings History=清除设置历史
Are you sure you want to clear all settings history?=您是否要清除所有设置的历史记录?
Delete RPC Setting=删除 RPC 设置
Add New RPC Setting=添加新 RPC 设置
Are you sure you want to remove rpc setting "{rpcName}"?=您是否要删除 RPC 设置 "{{rpcName}}"?
Updating Global Stat Interval=全局状态更新间隔
Updating Task Information Interval=任务信息更新间隔
Keyboard Shortcuts=键盘快捷键
Supported Keyboard Shortcuts=支持的键盘快捷键
Set Focus On Search Box=将焦点放在搜索框上
Swipe Gesture=滑动手势
Change Tasks Order by Drag-and-drop=拖拽任务排序
Action After Creating New Tasks=创建新任务后执行操作
Navigate to Task List Page=转到任务列表页面
Navigate to Task Detail Page=转到任务详情页面
Action After Retrying Task=重试任务后执行操作
Navigate to Downloading Tasks Page=转到正在下载列表页面
Stay on Current Page=留在当前页面
Remove Old Tasks After Retrying=重试任务后删除原任务
Confirm Task Removal=任务删除前确认
Include Prefix When Copying From Task Details=任务详情页复制时包括前缀
Show Pieces Info In Task Detail Page=任务详情页显示区块信息
Pieces Amount is Less than or Equal to {value}=区块数量小于等于 {{value}}
RPC List Display Order=RPC 列表显示顺序
Each Task List Page Uses Independent Display Order=各任务列表页面使用独立显示顺序
Recently Used=最近使用
RPC Alias=RPC 别名
Import / Export AriaNg Settings=导入 / 导出 AriaNg 设置
Import Settings=导入设置
Export Settings=导出设置
AriaNg settings data=AriaNg 设置数据
Confirm Import=确认导入
Are you sure you want to import all settings?=您是否要导入所有设置?
Invalid settings data format!=无效的设置数据格式!
Data has been copied to clipboard.=数据已经复制到剪贴板中.
Supported Placeholder=支持的占位符
AriaNg Title=AriaNg 标题
Current RPC Alias=当前 RPC 别名
Downloading Count=正在下载数量
Waiting Count=正在等待数量
Stopped Count=已停止数量
You have disabled notification in your browser. You should change your browser's settings before you enable this function.=您已经在浏览器中禁用通知功能. 如需使用此功能, 请修改您浏览器的设置.
Language resource has been updated, please reload the page for the changes to take effect.=语言资源已经更新, 请重新加载页面使其生效.
Configuration has been modified, please reload the page for the changes to take effect.=配置已经修改, 请重新加载页面使其生效.
Reload AriaNg=重新加载 AriaNg
Show Secret=显示密钥
Hide Secret=隐藏密钥
Aria2 Version=Aria2 版本
Enabled Features=已启用的功能
Operations=操作
Reconnect=重新连接
Save Session=保存会话
Shutdown Aria2=关闭 Aria2
Confirm Shutdown=确认关闭
Are you sure you want to shutdown aria2?=您是否要关闭 aria2?
Session has been saved successfully.=会话已经成功保存.
Aria2 has been shutdown successfully.=Aria2 已经成功关闭.
Toggle Navigation=切换导航
Shortcut=快捷方式
Global Rate Limit=全局速度限制
Loading=正在加载...
More Than One Day=超过1天
Unknown=未知
Bytes=字节
Hours=小时
Minutes=分
Seconds=秒
Milliseconds=毫秒
Http=Http
Http (Disabled)=Http (已禁用)
Https=Https
WebSocket=WebSocket
WebSocket (Disabled)=WebSocket (已禁用)
WebSocket (Security)=WebSocket (安全)
Http and WebSocket would be disabled when accessing AriaNg via Https.=使用 Https 访问 AriaNg 时，Http 和 WebSocket 将被禁用.
POST=POST
GET=GET
Enabled=启用
Disabled=禁用
Always=始终
Never=从不
BitTorrent=BitTorrent
Changes to the settings take effect after refreshing page.=设置将在页面刷新后生效.
Logging Time=记录时间
Log Level=日志级别
Auto Refresh=自动刷新
Refresh Now=立即刷新
Clear Logs=清空日志
Are you sure you want to clear debug logs?=您是否要清除调试日志?
Show Detail=显示详情
Log Detail=日志详情
Aria2 RPC Debug=Aria2 RPC 调试
Aria2 RPC Request Method=Aria2 RPC 请求方法
Aria2 RPC Request Parameters=Aria2 RPC 请求参数
Aria2 RPC Response=Aria2 RPC 响应
Execute=执行
RPC method is illegal!=RPC方法错误!
AriaNg does not support this RPC method!=AriaNg 不支持该RPC方法!
RPC request parameters are invalid!=RPC 请求参数无效!
Type is illegal!=类型错误!
Parameter is invalid!=请求参数无效!
Option value cannot be empty!=参数内容不能为空!
Input number is invalid!=输入的数字无效!
Input number is below min value!=输入的数字小于最小值 {{value}} !
Input number is above max value!=输入的数字大于最大值 {{value}} !
Input value is invalid!=输入的内容无效!
Protocol is invalid!=协议无效!
RPC host cannot be empty!=RPC 主机不能为空!
RPC secret is not base64 encoded!=RPC 密钥不是 Base64 编码后的字符串!
URL is not base64 encoded!=指定 URL 不是 Base64 编码后的字符串!
Tap to configure and get started with AriaNg.=您还没有进行过设置, 点击这里进行设置.
Cannot initialize WebSocket!=无法初始化 WebSocket!
Cannot connect to aria2!=无法连接到 aria2!
Access Denied!=拒绝访问!
You cannot use AriaNg because this browser does not meet the minimum requirements for data storage.=您无法使用 AriaNg, 因为这个浏览器不满足数据存储的最低要求.

[error]
unknown=未知错误.
operation.timeout=操作超时.
resource.notfound=无法找到指定资源.
resource.notfound.max-file-not-found=无法找到指定资源. 参见 --max-file-not-found option 参数.
download.aborted.lowest-speed-limit=由于下载速度过慢, 下载已经终止. 参见 --lowest-speed-limit option 参数.
network.problem=网络问题.
resume.notsupported=服务器不支持断点续传.
space.notenough=可用磁盘空间不足.
piece.length.different=分片大小与 .aria2 控制文件中的不同. 参见 --allow-piece-length-change 参数.
download.sametime=aria2 已经下载了另一个相同文件.
download.torrent.sametime=aria2 已经下载了另一个相同哈希的种子文件.
file.exists=文件已经存在. 参见 --allow-overwrite 参数.
file.rename.failed=文件重命名失败. 参见 --auto-file-renaming 参数.
file.open.failed=文件打开失败.
file.create.failed=文件创建或删除已有文件失败.
io.error=文件系统出错.
directory.create.failed=无法创建指定目录.
name.resolution.failed=域名解析失败.
metalink.file.parse.failed=解析 Metalink 文件失败.
ftp.command.failed=FTP 命令执行失败.
http.response.header.bad=HTTP 返回头无效或无法识别.
redirects.toomany=指定地址重定向过多.
http.authorization.failed=HTTP 认证失败.
bencoded.file.parse.failed=解析种子文件失败.
torrent.file.corrupted=指定 ".torrent" 种子文件已经损坏或缺少 aria2 需要的信息.
magnet.uri.bad=指定磁链地址无效.
option.bad=设置错误.
server.overload=远程服务器繁忙, 无法处理当前请求.
rpc.request.parse.failed=处理 RPC 请求失败.
checksum.failed=文件校验失败.

[languages]
Czech=捷克语
German=德语
English=英语
Spanish=西班牙语
French=法语
Italian=意大利语
Polish=波兰语
Russian=俄语
Simplified Chinese=简体中文
Traditional Chinese=繁体中文

[format]
longdate=YYYY年MM月DD日 HH:mm:ss
time.millisecond={{value}} 毫秒
time.milliseconds={{value}} 毫秒
time.second={{value}} 秒
time.seconds={{value}} 秒
time.minute={{value}} 分钟
time.minutes={{value}} 分钟
time.hour={{value}} 小时
time.hours={{value}} 小时
requires.aria2-version=需要 aria2 v{{version}} 或更高版本
task.new.download-links=下载链接 ({{count}} 个链接):
task.pieceinfo=已完成: {{completed}}, 共计: {{total}} 块
task.error-occurred=发生错误 ({{errorcode}})
task.verifying-percent=正在验证 ({{verifiedPercent}}%)
settings.file-count=({{count}} 个文件)
settings.total-count=(共计: {{count}}个)
debug.latest-logs=最近 {{count}} 条日志

[rpc.error]
unauthorized=认证失败!

[option]
true=是
false=否
default=默认
none=无
hide=隐藏
full=完整
http=Http
https=Https
ftp=Ftp
mem=仅内存
get=GET
tunnel=TUNNEL
plain=明文
arc4=ARC4
binary=二进制
ascii=ASCII
debug=调试 (Debug)
info=普通 (Info)
notice=一般 (Notice)
warn=警告 (Warn)
error=错误 (Error)
adaptive=自适应
epoll=epoll
falloc=falloc
feedback=反馈
geom=几何
inorder=顺序
kqueue=kqueue
poll=poll
port=port
prealloc=prealloc
random=随机
select=select
trunc=trunc
SSLv3=SSLv3
TLSv1=TLSv1
TLSv1.1=TLSv1.1
TLSv1.2=TLSv1.2

[options]
dir.name=下载路径
dir.description=
log.name=日志文件
log.description=日志文件的路径. 如果设置为 "-", 日志则写入到 stdout. 如果设置为空字符串(""), 日志将不会记录到磁盘上.
max-concurrent-downloads.name=最大同时下载数
max-concurrent-downloads.description=
check-integrity.name=检查完整性
check-integrity.description=通过对文件的每个分块或整个文件进行哈希验证来检查文件的完整性. 此选项仅对BT、Metalink及设置了 --checksum 选项的 HTTP(S)/FTP 链接生效.
continue.name=断点续传
continue.description=继续下载部分完成的文件. 启用此选项可以继续下载从浏览器或其他程序按顺序下载的文件. 此选项目前只支持 HTTP(S)/FTP 下载的文件.
all-proxy.name=代理服务器
all-proxy.description=设置所有协议的代理服务器地址. 您还可以针对特定的协议覆盖此选项, 即使用 --http-proxy, --https-proxy 和 --ftp-proxy 选项. 此设置将会影响所有下载. 代理服务器地址的格式为 [http://][USER:PASSWORD@]HOST[:PORT].
all-proxy-user.name=代理服务器用户名
all-proxy-user.description=
all-proxy-passwd.name=代理服务器密码
all-proxy-passwd.description=
checksum.name=校验和
checksum.description=设置校验和. 选项值格式为 TYPE=DIGEST. TYPE 为哈希类型. 支持的哈希类型列在 aria2c -v 的 Hash Algorithms 中. DIGEST 是十六进制摘要. 例如, 设置 sha-1 摘要如同这样: sha-1=0192ba11326fe2298c8cb4de616f4d4140213838 此选项仅对 HTTP(S)/FTP 下载生效.
connect-timeout.name=连接超时时间
connect-timeout.description=设置建立 HTTP/FTP/代理服务器 连接的超时时间(秒). 当连接建立后, 此选项不再生效, 请使用 --timeout 选项.
dry-run.name=模拟运行
dry-run.description=如果设置为"是", aria2 将仅检查远程文件是否存在而不会下载文件内容. 此选项仅对 HTTP/FTP 下载生效. 如果设置为 true, BT 下载将会直接取消.
lowest-speed-limit.name=最小速度限制
lowest-speed-limit.description=当下载速度低于此选项设置的值(B/s) 时将会关闭连接. 0 表示不设置最小速度限制. 您可以增加数值的单位 K 或 M (1K = 1024, 1M = 1024K). 此选项不会影响 BT 下载.
max-connection-per-server.name=单服务器最大连接数
max-connection-per-server.description=
max-file-not-found.name=文件未找到重试次数
max-file-not-found.description=如果 aria2 从远程 HTTP/FTP 服务器收到 "文件未找到" 的状态超过此选项设置的次数后下载将会失败. 设置为 0 将会禁用此选项. 此选项仅影响 HTTP/FTP 服务器. 重试时同时会记录重试次数, 所以也需要设置 --max-tries 这个选项.
max-tries.name=最大尝试次数
max-tries.description=设置最大尝试次数. 0 表示不限制.
min-split-size.name=最小文件分片大小
min-split-size.description=aria2 不会分割小于 2*SIZE 字节的文件. 例如, 文件大小为 20MB, 如果 SIZE 为 10M, aria2 会把文件分成 2 段 [0-10MB) 和 [10MB-20MB), 并且使用 2 个源进行下载 (如果 --split >= 2). 如果 SIZE 为 15M, 由于 2*15M > 20MB, 因此 aria2 不会分割文件并使用 1 个源进行下载. 您可以增加数值的单位 K 或 M (1K = 1024, 1M = 1024K). 可以设置的值为: 1M-1024M.
netrc-path.name=.netrc 文件路径
netrc-path.description=
no-netrc.name=禁用 netrc
no-netrc.description=
no-proxy.name=不使用代理服务器列表
no-proxy.description=设置不使用代理服务器的主机名, 域名, 包含或不包含子网掩码的网络地址, 多个使用逗号分隔.
out.name=文件名
out.description=下载文件的文件名. 其总是相对于 --dir 选项中设置的路径. 当使用 --force-sequential 参数时此选项无效.
proxy-method.name=代理服务器请求方法
proxy-method.description=设置用来请求代理服务器的方法. 方法可设置为 GET 或 TUNNEL. HTTPS 下载将忽略此选项并总是使用 TUNNEL.
remote-time.name=获取服务器文件时间
remote-time.description=从 HTTP/FTP 服务获取远程文件的时间戳, 如果可用将设置到本地文件
reuse-uri.name=URI 复用
reuse-uri.description=当所有给定的 URI 地址都已使用, 继续使用已经使用过的 URI 地址.
retry-wait.name=重试等待时间
retry-wait.description=设置重试间隔时间(秒). 当此选项的值大于 0 时, aria2 在 HTTP 服务器返回 503 响应时将会重试.
server-stat-of.name=服务器状态保存文件
server-stat-of.description=指定用来保存服务器状态的文件名. 您可以使用 --server-stat-if 参数读取保存的数据.
server-stat-timeout.name=服务器状态超时
server-stat-timeout.description=指定服务器状态的过期时间 (单位为秒).
split.name=单任务连接数
split.description=下载时使用 N 个连接. 如果提供超过 N 个 URI 地址, 则使用前 N 个地址, 剩余的地址将作为备用. 如果提供的 URI 地址不足 N 个, 这些地址多次使用以保证同时建立 N 个连接. 同一服务器的连接数会被 --max-connection-per-server 选项限制.
stream-piece-selector.name=分片选择算法
stream-piece-selector.description=指定 HTTP/FTP 下载使用的分片选择算法. 分片表示的是并行下载时固定长度的分隔段. 如果设置为"默认", aria2 将会按减少建立连接数选择分片. 由于建立连接操作的成本较高, 因此这是合理的默认行为. 如果设置为"顺序", aria2 将选择索引最小的分片. 索引为 0 时表示为文件的第一个分片. 这将有助于视频的边下边播. --enable-http-pipelining 选项有助于减少重连接的开销. 请注意, aria2 依赖于 --min-split-size 选项, 所以有必要对 --min-split-size 选项设置一个合理的值. 如果设置为"随机", aria2 将随机选择一个分片. 就像"顺序"一样, 依赖于 --min-split-size 选项. 如果设置为"几何", aria2 会先选择索引最小的分片, 然后会为之前选择的分片保留指数增长的空间. 这将减少建立连接的次数, 同时文件开始部分将会先行下载. 这也有助于视频的边下边播.
timeout.name=超时时间
timeout.description=
uri-selector.name=URI 选择算法
uri-selector.description=指定 URI 选择的算法. 可选的值包括 "按顺序", "反馈" 和 "自适应". 如果设置为"按顺序", URI 将按列表中出现的顺序使用. 如果设置为"反馈", aria2 将根据之前的下载速度选择 URI 列表中下载速度最快的服务器. 同时也将有效跳过无效镜像. 之前统计的下载速度将作为服务器状态文件的一部分, 参见 --server-stat-of 和 --server-stat-if 选项. 如果设置为"自适应", 将从最好的镜像和保留的连接里选择一项. 补充说明, 其返回的镜像没有被测试过, 同时如果每个镜像都已经被测试过时, 返回的镜像还会被重新测试. 否则, 其将不会选择其他镜像. 例如"反馈", 其使用服务器状态文件.
check-certificate.name=检查证书
check-certificate.description=
http-accept-gzip.name=支持 GZip
http-accept-gzip.description=如果远程服务器的响应头中包含 Content-Encoding: gzip 或 Content-Encoding: deflate , 将发送包含 Accept: deflate, gzip 的请求头并解压缩响应.
http-auth-challenge.name=认证质询
http-auth-challenge.description=仅当服务器需要时才发送 HTTP 认证请求头. 如果设置为"否", 每次都会发送认证请求头. 例外: 如果用户名和密码包含在 URI 中, 将忽略此选项并且每次都会发送认证请求头.
http-no-cache.name=禁用缓存
http-no-cache.description=发送的请求头中将包含 Cache-Control: no-cache 和 Pragma: no-cache header 以避免内容被缓存. 如果设置为"否", 上述请求头将不会发送, 同时您也可以使用 --header 选项将 Cache-Control 请求头添加进去.
http-user.name=HTTP 默认用户名
http-user.description=
http-passwd.name=HTTP 默认密码
http-passwd.description=
http-proxy.name=HTTP 代理服务器
http-proxy.description=
http-proxy-user.name=HTTP 代理服务器用户名
http-proxy-user.description=
http-proxy-passwd.name=HTTP 代理服务器密码
http-proxy-passwd.description=
https-proxy.name=HTTPS 代理服务器
https-proxy.description=
https-proxy-user.name=HTTPS 代理服务器用户名
https-proxy-user.description=
https-proxy-passwd.name=HTTPS 代理服务器密码
https-proxy-passwd.description=
referer.name=请求来源
referer.description=设置 HTTP 请求来源 (Referer). 此选项将影响所有 HTTP/HTTPS 下载. 如果设置为 *, 请求来源将设置为下载链接. 此选项可以配合 --parameterized-uri 选项使用.
enable-http-keep-alive.name=启用持久连接
enable-http-keep-alive.description=启用 HTTP/1.1 持久连接.
enable-http-pipelining.name=启用 HTTP 管线化
enable-http-pipelining.description=启用 HTTP/1.1 管线化.
header.name=自定义请求头
header.description=增加 HTTP 请求头内容. 每行放置一项, 每项包含 "请求头名: 请求头值".
save-cookies.name=Cookies 保存路径
save-cookies.description=以 Mozilla/Firefox(1.x/2.x)/Netscape 格式将 Cookies 保存到文件中. 如果文件已经存在, 将被覆盖. 会话过期的 Cookies 也将会保存, 其过期时间将会设置为 0.
use-head.name=启用 HEAD 方法
use-head.description=第一次请求 HTTP 服务器时使用 HEAD 方法.
user-agent.name=自定义 User Agent
user-agent.description=
ftp-user.name=FTP 默认用户名
ftp-user.description=
ftp-passwd.name=FTP 默认密码
ftp-passwd.description=如果 URI 中包含用户名单不包含密码, aria2 首先会从 .netrc 文件中获取密码. 如果在 .netrc 文件中找到密码, 则使用该密码. 否则, 使用此选项设置的密码.
ftp-pasv.name=被动模式
ftp-pasv.description=在 FTP 中使用被动模式. 如果设置为"否", 则使用主动模式. 此选项不适用于 SFTP 传输.
ftp-proxy.name=FTP 代理服务器
ftp-proxy.description=
ftp-proxy-user.name=FTP 代理服务器用户名
ftp-proxy-user.description=
ftp-proxy-passwd.name=FTP 代理服务器密码
ftp-proxy-passwd.description=
ftp-type.name=传输类型
ftp-type.description=
ftp-reuse-connection.name=连接复用
ftp-reuse-connection.description=
ssh-host-key-md.name=SSH 公钥校验和
ssh-host-key-md.description=设置 SSH 主机公钥的校验和. 选项值格式为 TYPE=DIGEST. TYPE 为哈希类型. 支持的哈希类型为 sha-1 和 md5. DIGEST 是十六进制摘要. 例如: sha-1=b030503d4de4539dc7885e6f0f5e256704edf4c3. 此选项可以在使用 SFTP 时用来验证服务器的公钥. 如果此选项不设置, 即保留默认, 不会进行任何验证。
bt-detach-seed-only.name=分离仅做种任务
bt-detach-seed-only.description=统计当前活动下载任务(参见 -j 选项) 时排除仅做种的任务. 这意味着, 如果参数设置为 -j3, 此选项打开并且当前有 3 个正在活动的任务, 并且其中有 1 个进入做种模式, 那么其会从正在下载的数量中排除(即数量会变为 2), 在队列中等待的下一个任务将会开始执行. 但要知道, 在 RPC 方法中, 做种的任务仍然被认为是活动的下载任务.
bt-enable-hook-after-hash-check.name=启用哈希检查完成事件
bt-enable-hook-after-hash-check.description=允许 BT 下载哈希检查(参见 -V 选项) 完成后调用命令. 默认情况下, 当哈希检查成功后, 通过 --on-bt-download-complete 设置的命令将会被执行. 如果要禁用此行为, 请设置为"否".
bt-enable-lpd.name=启用本地节点发现 (LPD)
bt-enable-lpd.description=
bt-exclude-tracker.name=BT 排除服务器地址
bt-exclude-tracker.description=逗号分隔的 BT 排除服务器地址. 您可以使用 * 匹配所有地址, 因此将排除所有服务器地址. 当在 shell 命令行使用 * 时, 需要使用转义符或引号.
bt-external-ip.name=外部 IP 地址
bt-external-ip.description=指定用在 BitTorrent 下载和 DHT 中的外部 IP 地址. 它可能被发送到 BitTorrent 服务器. 对于 DHT, 此选项将会报告本地节点正在下载特定的种子. 这对于在私有网络中使用 DHT 非常关键. 虽然这个方法叫外部, 但其可以接受各种类型的 IP 地址.
bt-force-encryption.name=强制加密
bt-force-encryption.description=BT 消息中的内容需要使用 arc4 加密. 此选项是设置 --bt-require-crypto --bt-min-crypto-level=arc4 这两个选项的快捷方式. 此选项不会修改上述两个选项的内容. 如果设置为"是", 将拒绝以前的 BT 握手, 并仅使用模糊握手及加密消息.
bt-hash-check-seed.name=做种前检查文件哈希
bt-hash-check-seed.description=如果设置为"是", 当使用 --check-integrity 选项完成哈希检查及文件完成后才继续做种. 如果您希望仅当文件损坏或未完成时检查文件, 请设置为"否". 此选项仅对 BT 下载有效
bt-load-saved-metadata.name=加载已保存的元数据文件
bt-load-saved-metadata.description=当使用磁链下载时, 在从 DHT 获取种子元数据之前, 首先尝试加载使用 --bt-save-metadata 选项保存的文件. 如果文件加载成功, 则不会从 DHT 下载元数据.
bt-max-open-files.name=最多打开文件数
bt-max-open-files.description=设置 BT/Metalink 下载全局打开的最大文件数.
bt-max-peers.name=最大连接节点数
bt-max-peers.description=设置每个 BT 下载的最大连接节点数. 0 表示不限制.
bt-metadata-only.name=仅下载种子文件
bt-metadata-only.description=仅下载种子文件. 种子文件中描述的文件将不会下载. 此选项仅对磁链生效.
bt-min-crypto-level.name=最低加密级别
bt-min-crypto-level.description=设置加密方法的最小级别. 如果节点提供多种加密方法, aria2 将选择满足给定级别的最低级别.
bt-prioritize-piece.name=优先下载
bt-prioritize-piece.description=尝试先下载每个文件开头或结尾的分片. 此选项有助于预览文件. 参数可以包括两个关键词: head 和 tail. 如果包含两个关键词, 需要使用逗号分隔. 每个关键词可以包含一个参数, SIZE. 例如, 如果指定 head=SIZE, 每个文件的最前 SIZE 数据将会获得更高的优先级. tail=SIZE 表示每个文件的最后 SIZE 数据. SIZE 可以包含 K 或 M (1K = 1024, 1M = 1024K).
bt-remove-unselected-file.name=删除未选择的文件
bt-remove-unselected-file.description=当 BT 任务完成后删除未选择的文件. 要选择需要下载的文件, 请使用 --select-file 选项. 如果没有选择, 则所有文件都默认为需要下载. 此选项会从磁盘上直接删除文件, 请谨慎使用此选项.
bt-require-crypto.name=需要加密
bt-require-crypto.description=如果设置为"是", aria 将不会接受以前的 BitTorrent 握手协议(\19BitTorrent 协议)并建立连接. 因此 aria2 总是模糊握手.
bt-request-peer-speed-limit.name=期望下载速度
bt-request-peer-speed-limit.description=如果一个 BT 下载的整体下载速度低于此选项设置的值, aria2 会临时提高连接数以提高下载速度. 在某些情况下, 设置期望下载速度可以提高您的下载速度. 您可以增加数值的单位 K 或 M (1K = 1024, 1M = 1024K).
bt-save-metadata.name=保存种子文件
bt-save-metadata.description=保存种子文件为 ".torrent" 文件. 此选项仅对磁链生效. 文件名为十六进制编码后的哈希值及 ".torrent"后缀. 保存的目录与下载文件的目录相同. 如果相同的文件已存在, 种子文件将不会保存.
bt-seed-unverified.name=不检查已经下载的文件
bt-seed-unverified.description=不检查之前下载文件中每个分片的哈希值.
bt-stop-timeout.name=无速度时自动停止时间
bt-stop-timeout.description=当 BT 任务下载速度持续为 0, 达到此选项设置的时间后停止下载. 如果设置为 0, 此功能将禁用.
bt-tracker.name=BT 服务器地址
bt-tracker.description=逗号分隔的 BT 服务器地址. 这些地址不受 --bt-exclude-tracker 选项的影响, 因为这些地址在 --bt-exclude-tracker 选项排除掉其他地址之后才会添加.
bt-tracker-connect-timeout.name=BT 服务器连接超时时间
bt-tracker-connect-timeout.description=设置 BT 服务器的连接超时时间 (秒). 当连接建立后, 此选项不再生效, 请使用 --bt-tracker-timeout 选项.
bt-tracker-interval.name=BT 服务器连接间隔时间
bt-tracker-interval.description=设置请求 BT 服务器的间隔时间 (秒). 此选项将完全覆盖服务器返回的最小间隔时间和间隔时间, aria2 仅使用此选项的值.如果设置为 0, aria2 将根据服务器的响应情况和下载进程决定时间间隔.
bt-tracker-timeout.name=BT 服务器超时时间
bt-tracker-timeout.description=
dht-file-path.name=DHT (IPv4) 文件
dht-file-path.description=修改 IPv4 DHT 路由表文件路径.
dht-file-path6.name=DHT (IPv6) 文件
dht-file-path6.description=修改 IPv6 DHT 路由表文件路径.
dht-listen-port.name=DHT 监听端口
dht-listen-port.description=设置 DHT (IPv4, IPv6) 和 UDP 服务器使用的 UDP 端口. 多个端口可以使用逗号 "," 分隔, 例如: 6881,6885. 您还可以使用短横线 "-" 表示范围: 6881-6999, 或可以一起使用: 6881-6889, 6999.
dht-message-timeout.name=DHT 消息超时时间
dht-message-timeout.description=
enable-dht.name=启用 DHT (IPv4)
enable-dht.description=启用 IPv4 DHT 功能. 此选项同时会启用 UDP 服务器支持. 如果种子设置为私有, 即使此选项设置为"是", aria2 也不会启用 DHT.
enable-dht6.name=启用 DHT (IPv6)
enable-dht6.description=启用 IPv6 DHT 功能. 如果种子设置为私有, 即使此选项设置为"是", aria2 也不会启用 DHT. 使用 --dht-listen-port 选项设置监听的端口.
enable-peer-exchange.name=启用节点交换
enable-peer-exchange.description=启用节点交换扩展. 如果种子设置为私有, 即使此选项设置为"是", aria2 也不会启用此功能.
follow-torrent.name=下载种子中的文件
follow-torrent.description=如果设置为"是"或"仅内存", 当后缀为 .torrent 或内容类型为 application/x-bittorrent 的文件下载完成时, aria2 将按种子文件读取并下载该文件中提到的文件. 如果设置为"仅内存", 该种子文件将不会写入到磁盘中, 而仅会存储在内存中. 如果设置为"否", 则 .torrent 文件会下载到磁盘中, 但不会按种子文件读取并且其中的文件不会进行下载.
listen-port.name=监听端口
listen-port.description=设置 BT 下载的 TCP 端口. 多个端口可以使用逗号 "," 分隔, 例如: 6881,6885. 您还可以使用短横线 "-" 表示范围: 6881-6999, 或可以一起使用: 6881-6889, 6999.
max-overall-upload-limit.name=全局最大上传速度
max-overall-upload-limit.description=设置全局最大上传速度 (字节/秒). 0 表示不限制. 您可以增加数值的单位 K 或 M (1K = 1024, 1M = 1024K).
max-upload-limit.name=最大上传速度
max-upload-limit.description=设置每个任务的最大上传速度 (字节/秒). 0 表示不限制. 您可以增加数值的单位 K 或 M (1K = 1024, 1M = 1024K).
peer-id-prefix.name=节点 ID 前缀
peer-id-prefix.description=指定节点 ID 的前缀. BT 中节点 ID 长度为 20 字节. 如果超过 20 字节, 将仅使用前 20 字节. 如果少于 20 字节, 将在其后不足随机的数据保证为 20 字节.
peer-agent.name=Peer Agent
peer-agent.description=指定 BT 扩展握手期间用于节点客户端版本的字符串.
seed-ratio.name=最小分享率
seed-ratio.description=指定分享率. 当分享率达到此选项设置的值时会完成做种. 强烈建议您将此选项设置为大于等于 1.0. 如果您想不限制分享比率, 可以设置为 0.0. 如果同时设置了 --seed-time 选项, 当任意一个条件满足时将停止做种.
seed-time.name=最小做种时间
seed-time.description=以 (小数形式的) 分钟指定做种时间. 此选项设置为 0 时, 将在 BT 任务下载完成后不进行做种.
follow-metalink.name=下载 Metalink 中的文件
follow-metalink.description=如果设置为"是"或"仅内存", 当后缀为 .meta4 或 .metalink 或内容类型为 application/metalink4+xml 或 application/metalink+xml 的文件下载完成时, aria2 将按 Metalink 文件读取并下载该文件中提到的文件. 如果设置为"仅内存", 该 Metalink 文件将不会写入到磁盘中, 而仅会存储在内存中. 如果设置为"否", 则 .metalink 文件会下载到磁盘中, 但不会按 Metalink 文件读取并且其中的文件不会进行下载.
metalink-base-uri.name=基础 URI
metalink-base-uri.description=指定基础 URI 以便解析本地磁盘中存储的 Metalink 文件里 metalink:url 和 metalink:metaurl 中的相对 URI 地址. 如果 URI 表示的为目录, 最后需要以 / 结尾.
metalink-language.name=语言
metalink-language.description=
metalink-location.name=首选服务器位置
metalink-location.description=首选服务器所在的位置. 可以使用逗号分隔的列表, 例如: jp,us.
metalink-os.name=操作系统
metalink-os.description=下载文件的操作系统.
metalink-version.name=版本号
metalink-version.description=下载文件的版本号.
metalink-preferred-protocol.name=首选使用协议
metalink-preferred-protocol.description=指定首选使用的协议. 可以设置为 http, https, ftp 或"无". 设置为"无"时禁用此选项.
metalink-enable-unique-protocol.name=仅使用唯一协议
metalink-enable-unique-protocol.description=如果一个 Metalink 文件可用多种协议, 并且此选项设置为"是", aria2 将只会使用其中一种. 使用 --metalink-preferred-protocol 参数指定首选的协议.
enable-rpc.name=启用 JSON-RPC/XML-RPC 服务器
enable-rpc.description=
pause-metadata.name=种子文件下载完后暂停
pause-metadata.description=当种子文件下载完成后暂停后续的下载. 在 aria2 中有 3 种种子文件的下载类型: (1) 下载 .torrent 文件. (2) 通过磁链下载的种子文件. (3) 下载 Metalink 文件. 这些种子文件下载完后会根据文件内容继续进行下载. 此选项会暂停这些后续的下载. 此选项仅当 --enable-rpc 选项启用时生效.
rpc-allow-origin-all.name=接受所有远程请求
rpc-allow-origin-all.description=在 RPC 响应头增加 Access-Control-Allow-Origin 字段, 值为 * .
rpc-listen-all.name=在所有网卡上监听
rpc-listen-all.description=在所有网络适配器上监听 JSON-RPC/XML-RPC 的请求, 如果设置为"否", 仅监听本地网络的请求.
rpc-listen-port.name=监听端口
rpc-listen-port.description=
rpc-max-request-size.name=最大请求大小
rpc-max-request-size.description=设置 JSON-RPC/XML-RPC 最大的请求大小. 如果 aria2 检测到请求超过设定的字节数, 会直接取消连接.
rpc-save-upload-metadata.name=保存上传的种子文件
rpc-save-upload-metadata.description=在 dir 选项设置的目录中保存上传的种子文件或 Metalink 文件. 文件名包括 SHA-1 哈希后的元数据和扩展名两部分. 对于种子文件, 扩展名为 '.torrent'. 对于 Metalink 为 '.meta4'. 如果此选项设置为"否", 通过 aria2.addTorrent() 或 aria2.addMetalink() 方法添加的下载将无法通过 --save-session 选项保存.
rpc-secure.name=启用 SSL/TLS
rpc-secure.description=RPC 将通过 SSL/TLS 加密传输. RPC 客户端需要使用 https 协议连接服务器. 对于 WebSocket 客户端, 使用 wss 协议. 使用 --rpc-certificate 和 --rpc-private-key 选项设置服务器的证书和私钥.
allow-overwrite.name=允许覆盖
allow-overwrite.description=如果相应的控制文件不存在时从头重新下载文件. 参见 --auto-file-renaming 选项.
allow-piece-length-change.name=允许分片大小变化
allow-piece-length-change.description=如果设置为"否", 当分片长度与控制文件中的不同时, aria2 将会中止下载. 如果设置为"是", 您可以继续, 但部分下载进度将会丢失.
always-resume.name=始终断点续传
always-resume.description=始终断点续传. 如果设置为"是", aria2 始终尝试断点续传, 如果无法恢复, 则中止下载. 如果设置为"否", 对于不支持断点续传的 URI 或 aria2 遇到 N 个不支持断点续传的 URI (N 为 --max-resume-failure-tries 选项设置的值), aria2 会从头下载文件. 参见 --max-resume-failure-tries 参数.
async-dns.name=异步 DNS
async-dns.description=
auto-file-renaming.name=文件自动重命名
auto-file-renaming.description=重新命名已经存在的文件. 此选项仅对 HTTP(S)/FTP 下载有效. 新的文件名后会在文件名后、扩展名 (如果有) 前追加句点和数字(1..9999).
auto-save-interval.name=自动保存间隔
auto-save-interval.description=每隔设置的秒数自动保存控制文件(*.aria2). 如果设置为 0, 下载期间控制文件不会自动保存. 不论设置的值为多少, aria2 会在任务结束时保存控制文件. 可以设置的值为 0 到 600.
conditional-get.name=条件下载
conditional-get.description=仅当本地文件比远程文件旧时才进行下载. 此功能仅适用于 HTTP(S) 下载. 如果在 Metalink 中文件大小已经被指定则功能无法生效. 同时此功能还将忽略 Content-Disposition 响应头. 如果存在控制文件, 此选项将被忽略. 此功能通过 If-Modified-Since 请求头获取较新的文件. 当获取到本地文件的修改时间时, 此功能将使用用户提供的文件名 (参见 --out 选项), 如果没有指定 --out 选项则使用 URI 中的文件名. 为了覆盖已经存在的文件, 需要使用 --allow-overwrite 参数.
conf-path.name=配置文件路径
conf-path.description=
console-log-level.name=控制台日志级别
console-log-level.description=
content-disposition-default-utf8.name=使用 UTF-8 处理 Content-Disposition
content-disposition-default-utf8.description=处理 "Content-Disposition" 头中的字符串时使用 UTF-8 字符集来代替 ISO-8859-1, 例如, 文件名参数, 但不是扩展版本的文件名.
daemon.name=启用后台进程
daemon.description=
deferred-input.name=延迟加载
deferred-input.description=如果设置为"是", aria2 在启动时不会读取 --input-file 选项设置的文件中的所有 URI 地址, 而是会在之后需要时按需读取. 如果输入文件中包含大量要下载的 URI, 此选项可以减少内存的使用. 如果设置为"否", aria2 会在启动时读取所有的 URI. 当 -save-session 使用时将会禁用 --deferred-input 选项.
disable-ipv6.name=禁用 IPv6
disable-ipv6.description=
disk-cache.name=磁盘缓存
disk-cache.description=启用磁盘缓存. 如果设置为 0, 将禁用磁盘缓存. 此功能将下载的数据缓存在内存中, 最多占用此选项设置的字节数. 缓存存储由 aria2 实例创建并对所有下载共享. 由于数据以较大的单位写入并按文件的偏移重新排序, 所以磁盘缓存的一个优点是减少磁盘的 I/O. 如果调用哈希检查时并且数据缓存在内存中时, 将不需要从磁盘中读取. 大小可以包含 K 或 M (1K = 1024, 1M = 1024K).
download-result.name=下载结果
download-result.description=此选项将修改下载结果的格式. 如果设置为"默认", 将打印 GID, 状态, 平均下载速度和路径/URI. 如果涉及多个文件, 仅打印第一个请求文件的路径/URI, 其余的将被忽略. 如果设置为"完整", 将打印 GID, 状态, 平均下载速度, 下载进度和路径/URI. 其中, 下载进度和路径/URI 将会每个文件打印一行. 如果设置为"隐藏", 下载结果将会隐藏.
dscp.name=DSCP
dscp.description=为 QoS 设置 BT 上行 IP 包的 DSCP 值. 此参数仅设置 IP 包中 TOS 字段的 DSCP 位, 而不是整个字段. 如果您从 /usr/include/netinet/ip.h 得到的值, 需要除以 4 (否则值将不正确, 例如您的 CS1 类将会转为 CS4). 如果您从 RFC, 网络供应商的文档, 维基百科或其他来源采取常用的值, 可以直接使用.
rlimit-nofile.name=最多打开的文件描述符
rlimit-nofile.description=设置打开的文件描述符的软限制 (soft limit). 此选项仅当满足如下条件时开放: a. 系统支持它 (posix). b. 限制没有超过硬限制 (hard limit). c. 指定的限制比当前的软限制高. 这相当于设置 ulimit, 除了其不能降低限制. 此选项仅当系统支持 rlimit API 时有效.
enable-color.name=终端输出使用颜色
enable-color.description=
enable-mmap.name=启用 MMap
enable-mmap.description=内存中存放映射文件. 当文件空间没有预先分配至, 此选项无效. 参见 --file-allocation.
event-poll.name=事件轮询方法
event-poll.description=设置事件轮询的方法. 可选的值包括 epoll, kqueue, port, poll 和 select. 对于 epoll, kqueue, port 和 poll, 只有系统支持时才可用. 最新的 Linux 支持 epoll. 各种 *BSD 系统包括 Mac OS X 支持 kqueue. Open Solaris 支持 port. 默认值根据您使用的操作系统不同而不同.
file-allocation.name=文件分配方法
file-allocation.description=指定文件分配方法. "无" 不会预先分配文件空间. "prealloc"会在下载开始前预先分配空间. 这将会根据文件的大小需要一定的时间. 如果您使用的是较新的文件系统, 例如 ext4 (带扩展支持), btrfs, xfs 或 NTFS (仅 MinGW 构建), "falloc" 是最好的选择. 其几乎可以瞬间分配大(数 GiB)文件. 不要在旧的文件系统, 例如 ext3 和 FAT32 上使用 falloc, 因为与 prealloc 花费的时间相同, 并且其会阻塞 aria2 直到分配完成. 当您的系统不支持 posix_fallocate(3) 函数时, falloc 可能无法使用. "trunc" 使用 ftruncate(2) 系统调用或平台特定的实现将文件截取到特定的长度. 在多文件的 BitTorrent 下载中, 若某文件与其相邻的文件共享相同的分片时, 则相邻的文件也会被分配.
force-save.name=强制保存
force-save.description=即使任务完成或删除时使用 --save-session 选项时也保存该任务. 此选项在这种情况下还会保存控制文件. 此选项可以保存被认为已经完成但正在做种的 BT 任务.
save-not-found.name=保存未找到的文件
save-not-found.description=当使用 --save-session 选项时, 即使当任务中的文件不存在时也保存该下载任务. 此选项同时会将这种情况保存到控制文件中.
hash-check-only.name=仅哈希检查
hash-check-only.description=如果设置为"是", 哈希检查完使用 --check-integrity 选项, 根据是否下载完成决定是否终止下载.
human-readable.name=控制台可读输出
human-readable.description=在控制台输出可读格式的大小和速度 (例如, 1.2Ki, 3.4Mi).
keep-unfinished-download-result.name=保留未完成的任务
keep-unfinished-download-result.description=保留所有未完成的下载结果, 即使超过了 --max-download-result 选项设置的数量. 这将有助于在会话文件中保存所有的未完成的下载 (参考 --save-session 选项). 需要注意的是, 未完成任务的数量没有上限. 如果不希望这样, 请关闭此选项.
max-download-result.name=最多下载结果
max-download-result.description=设置内存中存储最多的下载结果数量. 下载结果包括已完成/错误/已删除的下载. 下载结果存储在一个先进先出的队列中, 因此其可以存储最多指定的下载结果的数量. 当队列已满且有新的下载结果创建时, 最老的下载结果将从队列的最前部移除, 新的将放在最后. 此选项设置较大的值后如果经过几千次的下载将导致较高的内存消耗. 设置为 0 表示不存储下载结果. 注意, 未完成的下载将始终保存在内存中, 不考虑该选项的设置. 参考 --keep-unfinished-download-result 选项.
max-mmap-limit.name=MMap 最大限制
max-mmap-limit.description=设置启用 MMap (参见 --enable-mmap 选项) 最大的文件大小. 文件大小由一个下载任务中所有文件大小的和决定. 例如, 如果一个下载包含 5 个文件, 那么文件大小就是这些文件的总大小. 如果文件大小超过此选项设置的大小时, MMap 将会禁用.
max-resume-failure-tries.name=最大断点续传尝试次数
max-resume-failure-tries.description=当 --always-resume 选项设置为"否"时, 如果 aria2 检测到有 N 个 URI 不支持断点续传时, 将从头开始下载文件. 如果 N 设置为 0, 当所有 URI 都不支持断点续传时才会从头下载文件. 参见 --always-resume 选项.
min-tls-version.name=最低 TLS 版本
min-tls-version.description=指定启用的最低 SSL/TLS 版本.
log-level.name=日志级别
log-level.description=
optimize-concurrent-downloads.name=优化并发下载
optimize-concurrent-downloads.description=根据可用带宽优化并发下载的数量. aria2 使用之前统计的下载速度通过规则 N = A + B Log10 (速度单位为 Mbps) 得到并发下载的数量. 其中系数 A 和 B 可以在参数中以冒号分隔自定义. 默认值 (A=5, B=25) 可以在 1Mbps 网络上使用通常 5 个并发下载, 在 100Mbps 网络上为 50 个. 并发下载的数量保持在 --max-concurrent-downloads 参数定义的最大之下.
piece-length.name=文件分片大小
piece-length.description=设置 HTTP/FTP 下载的分配大小. aria2 根据这个边界分割文件. 所有的分割都是这个长度的倍数. 此选项不适用于 BitTorrent 下载. 如果 Metalink 文件中包含分片哈希的结果此选项也不适用.
show-console-readout.name=显示控制台输出
show-console-readout.description=
summary-interval.name=下载摘要输出间隔
summary-interval.description=设置下载进度摘要的输出间隔(秒). 设置为 0 禁止输出.
max-overall-download-limit.name=全局最大下载速度
max-overall-download-limit.description=设置全局最大下载速度 (字节/秒). 0 表示不限制. 您可以增加数值的单位 K 或 M (1K = 1024, 1M = 1024K).
max-download-limit.name=最大下载速度
max-download-limit.description=设置每个任务的最大下载速度 (字节/秒). 0 表示不限制. 您可以增加数值的单位 K 或 M (1K = 1024, 1M = 1024K).
no-conf.name=禁用配置文件
no-conf.description=
no-file-allocation-limit.name=文件分配限制
no-file-allocation-limit.description=不对比此参数设置大小小的分配文件. 您可以增加数值的单位 K 或 M (1K = 1024, 1M = 1024K).
parameterized-uri.name=启用参数化 URI 支持
parameterized-uri.description=启用参数化 URI 支持. 您可以指定部分的集合: http://{sv1,sv2,sv3}/foo.iso. 同时您也可以使用步进计数器指定数字化的序列: http://host/image[000-100:2].img. 步进计数器可以省略. 如果所有 URI 地址不指向同样的文件, 例如上述第二个示例, 需要使用 -Z 选项.
quiet.name=禁用控制台输出
quiet.description=
realtime-chunk-checksum.name=实时数据块验证
realtime-chunk-checksum.description=如果提供了数据块的校验和, 将在下载过程中通过校验和验证数据块.
remove-control-file.name=删除控制文件
remove-control-file.description=在下载前删除控制文件. 使用 --allow-overwrite=true 选项时, 总是从头开始下载文件. 此选项将有助于使用不支持断点续传代理服务器的用户.
save-session.name=状态保存文件
save-session.description=当退出时保存错误及未完成的任务到指定的文件中. 您可以在重启 aria2 时使用 --input-file 选项重新加载. 如果您希望输出的内容使用 GZip 压缩, 您可以在文件名后增加 .gz 扩展名. 请注意, 通过 aria2.addTorrent() 和 aria2.addMetalink() RPC 方法添加的下载, 其元数据没有保存到文件的将不会保存. 通过 aria2.remove() 和 aria2.forceRemove() 删除的下载将不会保存.
save-session-interval.name=保存状态间隔
save-session-interval.description=每隔此选项设置的时间(秒)后会保存错误或未完成的任务到 --save-session 选项指定的文件中. 如果设置为 0, 仅当 aria2 退出时才会保存.
socket-recv-buffer-size.name=Socket 接收缓冲区大小
socket-recv-buffer-size.description=设置 Socket 接收缓冲区最大的字节数. 指定为 0 时将禁用此选项. 当使用 SO_RCVBUF 选项调用 setsockopt() 时此选项的值将设置到 Socket 的文件描述符中.
stop.name=自动关闭时间
stop.description=在此选项设置的时间(秒)后关闭应用. 如果设置为 0, 此功能将禁用.
truncate-console-readout.name=缩短控制台输出内容
truncate-console-readout.description=缩短控制台输出的内容在一行中.
