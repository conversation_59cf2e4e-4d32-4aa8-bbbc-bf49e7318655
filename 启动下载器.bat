@echo off
chcp 65001 >nul
title 腾讯会议下载器 - aria2加速版

echo.
echo ========================================
echo    腾讯会议下载器 - aria2加速版
echo ========================================
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python，请确保Python已安装并添加到PATH
    echo.
    echo 💡 请安装Python 3.7或更高版本
    echo    下载地址：https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

REM 检查配置文件
if not exist "config.json" (
    echo ⚠️  警告：未找到config.json配置文件
    echo.
    echo 💡 正在启动Cookie配置工具...
    echo.
    python update_cookie.py
    echo.
    if not exist "config.json" (
        echo ❌ 配置文件创建失败，程序退出
        pause
        exit /b 1
    )
)

REM 启动下载器
echo 🚀 启动aria2加速下载器...
echo.
python download_with_aria2.py

echo.
echo 程序已结束
pause
