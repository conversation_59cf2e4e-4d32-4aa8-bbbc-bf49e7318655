[global]
AriaNg Version=Verze AriaNg
Operation Result=Výsledek operace
Operation Succeeded=Operace byla úspěš<PERSON>
is connected=je připojeno
Error=Chyba
OK=OK
Confirm=Potvrdit
Cancel=Zrušit
Close=Zavřít
True=Pravda
False=Nepravda
DEBUG=Ladění
INFO=Informace
WARN=Upozornění
ERROR=Chyba
Connecting=Připojování
Connected=Připojeno
Disconnected=Odpojeno
Reconnecting=Opětovné připojování
Waiting to reconnect=Čekání na opětovné připojení
Global=Globální
New=Nový
Start=Spustit
Pause=Pozastavit
Retry=Zkusit znovu
Retry Selected Tasks=Znovu spustit vybrané úlohy
Delete=Smazat
Select All=Vybrat vše
Select None=Zrušit výběr
Select Invert=Invertovat výběr
Select All Failed Tasks=Vybrat všechny neúspěšné <PERSON>hy
Select All Completed Tasks=Vybrat všechny dokončené úlohy
Select All Tasks=Vybrat všechny úlohy
Display Order=Pořadí zobrazení
Copy Download Url=Kopírovat URL ke stažení
Copy Magnet Link=Kopírovat magnetický odkaz
Help=Nápověda
Search=Hledat
Default=Výchozí
Expand=Rozbalit
Collapse=Zabalit
Expand All=Rozbalit vše
Collapse All=Zabalit vše
Open=Otevřít
Save=Uložit
Import=Importovat
Remove Task=Odstranit úlohu
Remove Selected Task=Odstranit vybranou úlohu
Clear Stopped Tasks=Vymazat zastavené úlohy
Click to view task detail=Klikněte pro zobrazení detailu úlohy
By File Name=Podle názvu souboru
By File Size=Podle velikosti souboru
By Progress=Podle pokroku
By Selected Status=Podle vybraného stavu
By Remaining=Podle zbývajícího
By Download Speed=Podle rychlosti stahování
By Upload Speed=Podle rychlosti nahrávání
By Peer Address=Podle adresy peeru
By Client Name=Podle názvu klienta
Filters=Filtry
Download=Stáhnout
Upload=Nahrát
Downloading=Stahuje se
Pending Verification=Čeká na ověření
Verifying=Ověřování
Seeding=Sdílení
Waiting=Čekání
Paused=Pozastaveno
Completed=Dokončeno
Error Occurred=Došlo k chybě
Removed=Odstraněno
Finished / Stopped=Dokončeno / Zastaveno
Uncompleted=Nedokončeno
Click to pin=Klikněte pro připnutí
Settings=Nastavení
AriaNg Settings=Nastavení AriaNg
Aria2 Settings=Nastavení Aria2
Basic Settings=Základní nastavení
HTTP/FTP/SFTP Settings=Nastavení HTTP/FTP/SFTP
HTTP Settings=Nastavení HTTP
FTP/SFTP Settings=Nastavení FTP/SFTP
BitTorrent Settings=Nastavení BitTorrentu
Metalink Settings=Nastavení Metalinku
RPC Settings=Nastavení RPC
Advanced Settings=Pokročilé nastavení
AriaNg Debug Console=Ladicí konzole AriaNg
Aria2 Status=Stav Aria2
File Name=Název souboru
File Size=Velikost souboru
Progress=Pokrok
Share Ratio=Poměr sdílení
Remaining=Zbývající
Download Speed=Rychlost stahování
Upload Speed=Rychlost nahrávání
Links=Odkazy
Torrent File=Torrent soubor
Metalink File=Metalink soubor
File Name:=Název souboru:
Options=Možnosti
Overview=Přehled
Pieces=Části
Files=Soubory
Peers=Peery
Task Name=Název úlohy
Task Size=Velikost úlohy
Task Status=Stav úlohy
Error Description=Popis chyby
Health Percentage=Procento celistvosti
Info Hash=Hash informace
Seeders=Sdílející
Connections=Připojení
Seed Creation Time=Čas vytvoření sdílení
Download Url=Url stažení
Download Dir=Dir stažení
BT Tracker Servers=Servery BT trackeru
Copy=Kopírovat
(Choose Files)=(Vybrat soubory)
Videos=Videa
Audios=Audia
Pictures=Obrázky
Documents=Dokumenty
Applications=Aplikace
Archives=Archivy
Other=Jiné
Custom=Vlastní
Custom Choose File=Vlastní výběr souboru
Address=Adresa
Client=Klient
Status=Stav
Speed=Rychlost
(local)=(lokální)
No Data=Žádná data
No connected peers=Žádné připojené peery
Failed to change some tasks state.=Nepodařilo se změnit stav některých úloh.
Confirm Retry=Potvrdit opakování
Are you sure you want to retry the selected task? AriaNg will create same task after clicking OK.=Opravdu chcete opakovat vybranou úlohu? AriaNg vytvoří stejnou úlohu po kliknutí na OK.
Failed to retry this task.=Nepodařilo se opakovat tuto úlohu.
{successCount} tasks have been retried and {failedCount} tasks are failed.={{successCount}} úloh bylo opakováno a {{failedCount}} úloh selhalo.
Confirm Remove=Potvrdit odstranění
Are you sure you want to remove the selected task?=Opravdu chcete odstranit vybranou úlohu?
Failed to remove some task(s).=Nepodařilo se odstranit některé úlohy.
Confirm Clear=Potvrdit vymazání
Are you sure you want to clear stopped tasks?=Opravdu chcete vymazat zastavené úlohy?
Download Links:=Odkazy ke stažení:
Download Now=Stáhnout nyní
Download Later=Stáhnout později
Open Torrent File=Otevřít Torrent soubor
Open Metalink File=Otevřít Metalink soubor
Support multiple URLs, one URL per line.=Podporuje více URL, jednu URL na řádek.
Your browser does not support loading file!=Váš prohlížeč nepodporuje načítání souborů!
The selected file type is invalid!=Vybraný typ souboru je neplatný!
Failed to load file!=Nepodařilo se načíst soubor!
Download Completed=Stahování dokončeno
BT Download Completed=BT stahování dokončeno
Download Error=Chyba při stahování
AriaNg Url=AriaNg URL
Command API Url=URL API příkazu
Export Command API=Exportovat API příkaz
Export=Exportovat
Copied=Zkopírováno
Pause After Task Created=Pozastavit po vytvoření úlohy
Language=Jazyk
Theme=Motiv
Light=Světlý
Dark=Tmavý
Follow system settings=Řídit se nastavením systému
Debug Mode=Režim ladění
Page Title=Titul stránky
Preview=Náhled
Tips: You can use the "noprefix" tag to ignore the prefix, "nosuffix" tag to ignore the suffix, and "scale\=n" tag to set the decimal precision.=Tipy: Můžete použít tag "noprefix" pro ignorování předpony, "nosuffix" pro ignorování přípony a "scale\=n" pro nastavení desetinné přesnosti.
Example: ${downspeed:noprefix:nosuffix:scale\=1}=Příklad: ${downspeed:noprefix:nosuffix:scale\=1}
Updating Page Title Interval=Interval aktualizace titulku stránky
Enable Browser Notification=Povolit upozornění v prohlížeči
Browser Notification Sound=Zvuk upozornění prohlížeče
Browser Notification Frequency=Frekvence upozornění v prohlížeči
Unlimited=Neomezeno
High (Up to 10 Notifications / 1 Minute)=Vysoká (až 10 upozornění za 1 minutu)
Middle (Up to 1 Notification / 1 Minute)=Střední (až 1 upozornění za 1 minutu)
Low (Up to 1 Notification / 5 Minutes)=Nízká (až 1 upozornění za 5 minut)
WebSocket Auto Reconnect Interval=Interval automatického připojení WebSocketu
Aria2 RPC Alias=Alias Aria2 RPC
Aria2 RPC Address=Adresa Aria2 RPC
Aria2 RPC Protocol=Protokol Aria2 RPC
Aria2 RPC Http Request Method=Metoda HTTP požadavku Aria2 RPC
POST method only supports aria2 v1.15.2 and above.=Metoda POST podporuje pouze Aria2 v1.15.2 a novější.
Aria2 RPC Request Headers=Záhlaví požadavků Aria2 RPC
Support multiple request headers, one header per line, each line containing "header name: header value".=Podporuje více záhlaví požadavků, jedno záhlaví na řádku, každá řádka obsahuje "název záhlaví: hodnota záhlaví".
Aria2 RPC Secret Token=Tajný token Aria2 RPC
Activate=Aktivovat
Reset Settings=Obnovit nastavení
Confirm Reset=Potvrdit obnovení
Are you sure you want to reset all settings?=Opravdu chcete obnovit všechna nastavení?
Clear Settings History=Vymazat historii nastavení
Are you sure you want to clear all settings history?=Opravdu chcete vymazat celou historii nastavení?
Delete RPC Setting=Odstranit nastavení RPC
Add New RPC Setting=Přidat nové nastavení RPC
Are you sure you want to remove rpc setting "{rpcName}"?=Opravdu chcete odstranit nastavení RPC "{{rpcName}}"?
Updating Global Stat Interval=Interval aktualizace globální statistiky
Updating Task Information Interval=Interval aktualizace informací o úloze
Keyboard Shortcuts=Klávesové zkratky
Supported Keyboard Shortcuts=Podporované klávesové zkratky
Set Focus On Search Box=Umístit kurzor na vyhledávací pole
Swipe Gesture=Gesto přejetí
Change Tasks Order by Drag-and-drop=Změnit pořadí úloh pomocí přetahování
Action After Creating New Tasks=Akce po vytvoření nových úloh
Navigate to Task List Page=Přejít na stránku seznamu úloh
Navigate to Task Detail Page=Přejít na stránku detailu úlohy
Action After Retrying Task=Akce po opětovném spuštění úlohy
Navigate to Downloading Tasks Page=Přejít na stránku stahovaných úloh
Stay on Current Page=Zůstat na aktuální stránce
Remove Old Tasks After Retrying=Odstranit staré úlohy po opětovném spuštění
Confirm Task Removal=Potvrdit odstranění úlohy
Include Prefix When Copying From Task Details=Zahrnout předponu při kopírování z detailu úlohy
Show Pieces Info In Task Detail Page=Zobrazit informace o částech na stránce detailu úlohy
Pieces Amount is Less than or Equal to {value}=Počet částí je menší nebo roven hodnotě {{value}}
RPC List Display Order=Pořadí zobrazení seznamu RPC
Each Task List Page Uses Independent Display Order=Každá stránka seznamu úloh používá nezávislé pořadí zobrazení
Recently Used=Nedávno použité
RPC Alias=Alias RPC
Import / Export AriaNg Settings=Import / Export nastavení AriaNg
Import Settings=Importovat nastavení
Export Settings=Exportovat nastavení
AriaNg settings data=Data nastavení AriaNg
Confirm Import=Potvrdit import
Are you sure you want to import all settings?=Opravdu chcete importovat všechna nastavení?
Invalid settings data format!=Neplatný formát dat nastavení!
Data has been copied to clipboard.=Data byla zkopírována do schránky.
Supported Placeholder=Podporovaný zástupný symbol
AriaNg Title=Titul AriaNg
Current RPC Alias=Aktuální alias RPC
Downloading Count=Počet stahování
Waiting Count=Počet čekajících
Stopped Count=Počet zastavených
You have disabled notification in your browser. You should change your browser's settings before you enable this function.=Upozornění jsou v prohlížeči zakázána. Změňte nastavení prohlížeče, než tuto funkci povolíte.
Language resource has been updated, please reload the page for the changes to take effect.=Jazykový zdroj byl aktualizován, prosím, načtěte stránku znovu, aby se změny projevily.
Configuration has been modified, please reload the page for the changes to take effect.=Konfigurace byla upravena, prosím, načtěte stránku znovu, aby se změny projevily.
Reload AriaNg=Načíst znovu AriaNg
Show Secret=Zobrazit tajemství
Hide Secret=Skrýt tajemství
Aria2 Version=Verze Aria2
Enabled Features=Povolené funkce
Operations=Operace
Reconnect=Znovu připojit
Save Session=Uložit relaci
Shutdown Aria2=Vypnout Aria2
Confirm Shutdown=Potvrdit vypnutí
Are you sure you want to shutdown aria2?=Opravdu chcete vypnout Aria2?
Session has been saved successfully.=Relace byla úspěšně uložena.
Aria2 has been shutdown successfully.=Aria2 byla úspěšně vypnuta.
Toggle Navigation=Přepnout navigaci
Shortcut=Zkratka
Global Rate Limit=Globální omezení rychlosti
Loading=Načítání
More Than One Day=Více než 1 den
Unknown=Neznámé
Bytes=Bajty
Hours=Hodiny
Minutes=Minuty
Seconds=Sekundy
Milliseconds=Milisekundy
Http=Http
Http (Disabled)=Http (Zakázáno)
Https=Https
WebSocket=WebSocket
WebSocket (Disabled)=WebSocket (Zakázáno)
WebSocket (Security)=WebSocket (Zabezpečení)
Http and WebSocket would be disabled when accessing AriaNg via Https.=Http a WebSocket budou zakázány při přístupu k AriaNg přes Https.
POST=POST
GET=GET
Enabled=Povolené
Disabled=Zakázané
Always=Vždy
Never=Nikdy
BitTorrent=BitTorrent
Changes to the settings take effect after refreshing page.=Změny v nastavení se projeví po obnovení stránky.
Logging Time=Čas protokolování
Log Level=Úroveň protokolů
Auto Refresh=Automatické obnovení
Refresh Now=Obnovit nyní
Clear Logs=Vymazat protokoly
Are you sure you want to clear debug logs?=Opravdu chcete vymazat ladicí protokoly?
Show Detail=Zobrazit podrobnosti
Log Detail=Podrobnosti protokolu
Aria2 RPC Debug=Ladění Aria2 RPC
Aria2 RPC Request Method=Metoda požadavku Aria2 RPC
Aria2 RPC Request Parameters=Parametry požadavku Aria2 RPC
Aria2 RPC Response=Odpověď Aria2 RPC
Execute=Spustit
RPC method is illegal!=Metoda RPC je neplatná!
AriaNg does not support this RPC method!=AriaNg nepodporuje tuto metodu RPC!
RPC request parameters are invalid!=Parametry požadavku RPC jsou neplatné!
Type is illegal!=Typ je neplatný!
Parameter is invalid!=Parametr je neplatný!
Option value cannot be empty!=Hodnota volby nesmí být prázdná!
Input number is invalid!=Zadané číslo je neplatné!
Input number is below min value!=Zadané číslo je nižší než minimální hodnota {{value}}!
Input number is above max value!=Zadané číslo je vyšší než maximální hodnota {{value}}!
Input value is invalid!=Zadaná hodnota je neplatná!
Protocol is invalid!=Protokol je neplatný!
RPC host cannot be empty!=Hostitel RPC nesmí být prázdný!
RPC secret is not base64 encoded!=RPC tajemství není zakódováno v base64!
URL is not base64 encoded!=URL není zakódováno v base64!
Tap to configure and get started with AriaNg.=Klepněte pro konfiguraci a začněte používat AriaNg.
Cannot initialize WebSocket!=Nelze inicializovat WebSocket!
Cannot connect to aria2!=Nelze se připojit k aria2!
Access Denied!=Přístup byl odepřen!
You cannot use AriaNg because this browser does not meet the minimum requirements for data storage.=Nelze používat AriaNg, protože tento prohlížeč nesplňuje minimální požadavky pro ukládání dat.

[error]
unknown=Došlo k neznámé chybě.
operation.timeout=Časový limit operace vypršel.
resource.notfound=Zdroj nebyl nalezen.
resource.notfound.max-file-not-found=Zdroj nebyl nalezen. Viz volba --max-file-not-found.
download.aborted.lowest-speed-limit=Stahování bylo přerušeno, protože rychlost stahování byla příliš nízká. Viz volba --lowest-speed-limit.
network.problem=Došlo k problému se sítí.
resume.notsupported=Vzdálený server nepodporuje pokračování.
space.notenough=Na disku není dostatek volného místa.
piece.length.different=Délka částí se liší od délky uvedené v kontrolním souboru .aria2. Viz volba --allow-piece-length-change.
download.sametime=Aria2 v tu chvíli stahovala stejný soubor.
download.torrent.sametime=Aria2 v tu chvíli stahovala stejný torrent.
file.exists=Soubor již existuje. Viz volba --allow-overwrite.
file.rename.failed=Nepodařilo se přejmenovat soubor. Viz volba --auto-file-renaming.
file.open.failed=Nepodařilo se otevřít existující soubor.
file.create.failed=Nepodařilo se vytvořit nový soubor nebo zkrátit existující soubor.
io.error=Došlo k chybě souborového systému.
directory.create.failed=Nepodařilo se vytvořit adresář.
name.resolution.failed=Nepodařilo se přeložit název domény.
metalink.file.parse.failed=Nepodařilo se analyzovat dokument Metalink.
ftp.command.failed=Příkaz FTP selhal.
http.response.header.bad=Hlavička HTTP odpovědi byla neplatná nebo nerozpoznaná.
redirects.toomany=Došlo k příliš mnoha přesměrováním.
http.authorization.failed=HTTP autorizace selhala.
bencoded.file.parse.failed=Nepodařilo se analyzovat bencoded soubor (obvykle soubor ".torrent").
torrent.file.corrupted=Soubor ".torrent" byl poškozený nebo mu chyběly informace, které aria2 potřebovala.
magnet.uri.bad=Magnetický URI byl neplatný.
option.bad=Byla zadána špatná/nerozpoznaná volba nebo neočekávaný argument volby.
server.overload=Vzdálený server nedokázal zpracovat požadavek kvůli přetížení nebo údržbě.
rpc.request.parse.failed=Nepodařilo se analyzovat JSON-RPC požadavek.
checksum.failed=Validace kontrolního součtu selhala.

[languages]
Czech=Čeština
German=Němčina
English=Angličtina
Spanish=Španělština
French=Francouzština
Italian=Italština
Polish=Polština
Russian=Ruština
Simplified Chinese=Zjednodušená čínština
Traditional Chinese=Tradiční čínština

[format]
longdate=MM/DD/RRRR HH:mm:ss
time.millisecond={{value}} milisekunda
time.milliseconds={{value}} milisekund
time.second={{value}} sekunda
time.seconds={{value}} sekund
time.minute={{value}} minuta
time.minutes={{value}} minut
time.hour={{value}} hodina
time.hours={{value}} hodin
requires.aria2-version=Vyžaduje Aria2 v{{version}} nebo vyšší
task.new.download-links=Odkazy ke stažení ({{count}} odkazů):
task.pieceinfo=Dokončeno: {{completed}}, Celkem: {{total}}
task.error-occurred=Došlo k chybě ({{errorcode}})
task.verifying-percent=Ověřování ({{verifiedPercent}}%)
settings.file-count=({{count}} souborů)
settings.total-count=(Celkový počet: {{count}})
debug.latest-logs=Nejnovější {{count}} logu

[rpc.error]
unauthorized=Autorizace selhala!

[option]
true=Pravda
false=Nepravda
default=Výchozí
none=Žádné
hide=Skrýt
full=Plný
http=Http
https=Https
ftp=Ftp
mem=Pouze paměť
get=GET
tunnel=Tunel
plain=Prostý
arc4=ARC4
binary=Binární
ascii=ASCII
debug=Ladění
info=Informace
notice=Upozornění
warn=Varování
error=Chyba
adaptive=Adaptivní
epoll=Epoll
falloc=Falloc
feedback=Zpětná vazba
geom=Geometrie
inorder=V pořadí
kqueue=Kqueue
poll=Poll
port=Port
prealloc=Předalokace
random=Náhodný
select=Vybrat
trunc=Zkrátit
SSLv3=SSLv3
TLSv1=TLSv1
TLSv1.1=TLSv1.1
TLSv1.2=TLSv1.2

[options]
dir.name=Stahování cesta
dir.description=Udává ředitelství, do kterého budou stažené soubory uloženy.
log.name=Soubor logu
log.description=Název souboru logu. Pokud je zadáno "-", log se zapisuje na standardní výstup. Pokud je zadán prázdný řetězec (""), nebo pokud je tato volba vynechána, log se vůbec nezapisuje na disk.
max-concurrent-downloads.name=Maximální počet současných stahování
max-concurrent-downloads.description=Nastavuje maximální počet souborů, které bude aria2 stahovat najednou.
check-integrity.name=Kontrola integrity
check-integrity.description=Ověřuje integritu souboru validací hashů částí nebo celého souboru. Tato volba má účinek pouze u stahování BitTorrent, Metalink s kontrolními součty nebo u HTTP(S)/FTP stahování s volbou --checksum.
continue.name=Obnovit stahování
continue.description=Pokračuje ve stahování částečně staženého souboru. Použijte tuto volbu pro obnovení stahování, které bylo zahájeno webovým prohlížečem nebo jiným programem, který stahuje soubory sekvenčně od začátku. Tato volba je momentálně použitelná pouze pro HTTP(S)/FTP stahování.
all-proxy.name=Proxy server
all-proxy.description=Použít proxy server pro všechny protokoly. Také můžete tuto konfiguraci přepsat a specifikovat proxy server pro konkrétní protokol pomocí --http-proxy, --https-proxy a --ftp-proxy. Toto ovlivňuje všechna stahování. Formát PROXY je [http://][UŽIVATEL:HESLO@]HOST[:PORT].
all-proxy-user.name=Uživatelské jméno proxy
all-proxy-user.description=Určuje uživatelské jméno pro autentifikace při připojení ke všem proxy serverům.
all-proxy-passwd.name=Heslo proxy
all-proxy-passwd.description=Určuje heslo pro autentifikace při připojení ke všem proxy serverům.
checksum.name=Kontrolní součet
checksum.description=Nastavit kontrolní součet. Formát hodnoty volby je TYP=DIGEST. TYP je typ hash. Podporované typy hash jsou uvedeny v Hash Algorithms v aria2c -v. DIGEST je hexadecimální digest. Například nastavení sha-1 digestu vypadá takto: sha-1=0192ba11326fe2298c8cb4de616f4d4140213838. Tato volba platí pouze pro HTTP(S)/FTP stahování.
connect-timeout.name=Časový limit připojení
connect-timeout.description=Nastavte časový limit připojení v sekundách pro navázání spojení s HTTP/FTP/proxy serverem. Po navázání spojení tato volba přestane mít účinek a použije se volba --timeout.
dry-run.name=Zkušební start
dry-run.description=Pokud je zadáno "Pravda", aria2 pouze zkontroluje, zda je vzdálený soubor dostupný, a nestahuje žádná data. Tato volba má účinek pouze u HTTP/FTP stahování. Stahování BitTorrentu se zruší, pokud je zadáno "Pravda".
lowest-speed-limit.name=Nejnižší rychlost stahování
lowest-speed-limit.description=Ukončit spojení, pokud je rychlost stahování nižší nebo rovna této hodnotě (bajty za sekundu). 0 znamená, že aria2 nemá žádný limit na nejnižší rychlost. Můžete připojit K nebo M (1K = 1024, 1M = 1024K). Tato volba neovlivňuje stahování BitTorrentu.
max-connection-per-server.name=Maximální počet připojení na server
max-connection-per-server.description=Nastavuje maximální počet spojení, které může aria2 současně instalovat s jedním serverem pro stažení jednoho souboru. To pomáhá optimalizovat rychlost stahování tím, že zabraňuje přílišnému zatížení serveru.
max-file-not-found.name=Maximální počet pokusů o nalezení souboru
max-file-not-found.description=Pokud aria2 obdrží stav "soubor nenalezen" od vzdálených HTTP/FTP serverů NUM krát bez získání jediného bajtu, vynutí selhání stahování. Zadejte 0 pro deaktivaci této volby. Tato volba je účinná pouze při použití HTTP/FTP serverů. Počet pokusů se počítá do --max-tries, takže by měla být také nastavena.
max-tries.name=Maximální počet pokusů
max-tries.description=Nastavit počet pokusů. 0 znamená neomezený počet.
min-split-size.name=Minimální velikost části
min-split-size.description=aria2 nerozdělí méně než 2*SIZE bajtů. Například, pokud stahujete 20MiB soubor a SIZE je 10M, aria2 může rozdělit soubor na 2 rozsahy [0-10MiB) a [10MiB-20MiB) a stáhnout je pomocí 2 zdrojů (pokud --split >= 2, samozřejmě). Pokud je SIZE 15M, protože 2*15M > 20MiB, aria2 nerozdělí soubor a stáhne ho pomocí 1 zdroje. Můžete připojit K nebo M (1K = 1024, 1M = 1024K). Možné hodnoty: 1M-1024M.
netrc-path.name=Cesta k .netrc
netrc-path.description=Udává cestu k souboru .netrc, který bude použit pro autentifikaci při připojení k serveru.
no-netrc.name=Zakázat netrc
no-netrc.description=Vypíná použití souboru .netrc pro autentifikaci. Pokud je instalováno, aria2 nebude vyhledávat a používat tento soubor k autentifikaci.
no-proxy.name=Seznam bez proxy
no-proxy.description=Specifikujte seznam názvů hostitelů, domén a síťových adres oddělených čárkami s nebo bez masky podsítě, kde se proxy nemá používat.
out.name=Název souboru
out.description=Název staženého souboru. Vždy je relativní k adresáři uvedenému v možnosti --dir. Při použití možnosti --force-sequential je tato možnost ignorována.
proxy-method.name=Metoda proxy
proxy-method.description=Nastavte metodu použitou v proxy požadavku. Metoda je buď "GET" nebo "Tunel". Stahování přes HTTPS vždy používá "Tunel" bez ohledu na tuto možnost.
remote-time.name=Časová značka vzdáleného souboru
remote-time.description=Získá časovou značku vzdáleného souboru z HTTP/FTP serveru a pokud je dostupné, aplikuje ji na místní soubor.
reuse-uri.name=Znovu použít URI
reuse-uri.description=Znovu použít již použitá URI, pokud nezbývají žádná nepoužitá URI.
retry-wait.name=Čekání na opakování
retry-wait.description=Nastavte počet sekund čekání mezi opakováními. Pokud je SEC > 0, aria2 znovu zkouší stahování, když HTTP server vrátí odpověď 503.
server-stat-of.name=Výstup statistiky serveru
server-stat-of.description=Určete název souboru, do kterého se uloží profil výkonu serverů. Uložená data můžete načíst pomocí možnosti --server-stat-if.
server-stat-timeout.name=Timeout statistiky serveru
server-stat-timeout.description=Určuje timeout v sekundách pro zneplatnění profilu výkonu serverů od posledního kontaktu s nimi.
split.name=Počet rozdělení
split.description=Stáhněte soubor pomocí N připojení. Pokud je zadáno více než N URI, prvních N URI je použito a zbývající URI slouží jako záložní. Pokud je zadáno méně než N URI, tato URI se použijí vícekrát, aby bylo současně vytvořeno celkem N připojení. Počet připojení ke stejnému hostiteli je omezen možností --max-connection-per-server.
stream-piece-selector.name=Algoritmus výběru částí
stream-piece-selector.description=Určete algoritmus výběru částí použitý při stahování HTTP/FTP. Část znamená segment s pevnou délkou, který se stahuje paralelně při segmentovaném stahování. Pokud je zadán výchozí algoritmus, aria2 vybírá části tak, aby snížila počet vytváření připojení. To je rozumné výchozí chování, protože vytváření připojení je nákladná operace. Pokud je zadán "V pořadí", aria2 vybírá část s minimálním indexem. Index=0 znamená začátek souboru. To může být užitečné pro sledování filmu při stahování. Možnost --enable-http-pipelining může být užitečná pro snížení režijních nákladů na opakované připojení. Pamatujte, že aria2 respektuje možnost --min-split-size, takže bude nutné nastavit rozumnou hodnotu pro možnost --min-split-size. Pokud je zadán "Náhodný", aria2 vybírá části náhodně. Stejně jako u "V pořadí" se respektuje možnost --min-split-size. Pokud je zadán geom, na začátku aria2 vybírá část s minimálním indexem jako u "V pořadí", ale exponenciálně zvyšuje vzdálenost od dříve vybrané části. To sníží počet vytváření připojení a zároveň stáhne začátek souboru jako první. To bude užitečné pro sledování filmu při stahování.
timeout.name=Timeout
timeout.description=Určuje timeout pro všechny webové transakce. Pokud se operace během uvedené doby nedokončí, bude přerušena. Hodnota je uvedena ve vteřinách.
uri-selector.name=Algoritmus výběru URI
uri-selector.description=Určete algoritmus výběru URI. Možné hodnoty jsou "V pořadí", "Zpětná vazba" a "Adaptivní". Pokud je zadán "V pořadí", URI se zkouší v pořadí, ve kterém se objevily v seznamu URI. Pokud je zadán "Zpětná vazba", aria2 používá rychlost stahování pozorovanou v předchozích stahováních a vybírá nejrychlejší server v seznamu URI. To také efektivně přeskočí nefunkční zrcadla. Pozorovaná rychlost stahování je součástí výkonového profilu serverů uvedeného v --server-stat-of a --server-stat-if. Pokud je zadán "Adaptivní", vybere jedno z nejlepších zrcadel pro první a rezervované připojení. Pro doplňková připojení vrací zrcadla, která ještě nebyla testována, a pokud byla všechna již testována, vrací zrcadla, která je třeba znovu otestovat. Jinak již nevybírá žádná další zrcadla. Stejně jako "Zpětná vazba" používá výkonový profil serverů.
check-certificate.name=Kontrola certifikátu
check-certificate.description=Definuje, zda bude aria2 ověřovat SSL certifikáty při spojení s HTTPS servery. Pokud je nastaveno "Pravda", bude aria2 ověřovat certifikáty, pokud "Nepravda" - bude je ignorovat.
http-accept-gzip.name=Akceptovat GZip
http-accept-gzip.description=Odesílá hlavičku požadavku Accept: deflate, gzip a dekomprimuje odpověď, pokud vzdálený server odpoví s Content-Encoding: gzip nebo Content-Encoding: deflate.
http-auth-challenge.name=Autentizační výzva
http-auth-challenge.description=Odesílá hlavičku HTTP autorizace pouze tehdy, pokud je požadována serverem. Pokud je nastaveno false, hlavička autorizace je vždy odesílána na server. Výjimkou je, pokud je uživatelské jméno a heslo vloženo do URI, hlavička autorizace je vždy odeslána na server bez ohledu na tuto možnost.
http-no-cache.name=Bez cache
http-no-cache.description=Odesílá hlavičky Cache-Control: no-cache a Pragma: no-cache, aby se předešlo uloženému obsahu. Pokud je zadáno false, tyto hlavičky nejsou odesílány a můžete přidat hlavičku Cache-Control s libovolnou direktivou pomocí možnosti --header.
http-user.name=Výchozí uživatelské jméno HTTP
http-user.description=Určuje uživatelské jméno pro autentifikace při připojení k HTTP serveru.
http-passwd.name=Výchozí heslo HTTP
http-passwd.description=Určuje heslo pro autentifikace při připojení k HTTP serveru.
http-proxy.name=HTTP proxy server
http-proxy.description=Instaluje proxy server pro HTTP připojení. Zadejte adresu proxy serveru, přes kterou budou HTTP dotazy procházet.
http-proxy-user.name=Uživatelské jméno pro HTTP proxy
http-proxy-user.description=Určuje uživatelské jméno pro autentifikace při připojení k HTTP proxy serveru.
http-proxy-passwd.name=Heslo pro HTTP proxy
http-proxy-passwd.description=Určuje heslo pro autentifikace při připojení k HTTP proxy serveru.
https-proxy.name=HTTPS proxy server
https-proxy.description=Určuje proxy server pro HTTPS připojení. Zadejte adresu proxy serveru, přes kterou budou HTTPS dotazy procházet.
https-proxy-user.name=Uživatelské jméno pro HTTPS proxy
https-proxy-user.description=Určuje uživatelské jméno pro autentifikace při připojení k HTTPS proxy serveru.
https-proxy-passwd.name=Heslo pro HTTPS proxy
https-proxy-passwd.description=Určuje heslo pro autentifikace při připojení k HTTPS proxy serveru.
referer.name=Odkazující stránka
referer.description=Nastavte HTTP odkazující stránku (Referer). Toto ovlivňuje všechna HTTP/HTTPS stahování. Pokud je zadána *, adresa URI stahování se také používá jako odkazující stránka. To může být užitečné při použití společně s možností --parameterized-uri.
enable-http-keep-alive.name=Povolit přetrvávající připojení
enable-http-keep-alive.description=Povolit přetrvávající připojení HTTP/1.1.
enable-http-pipelining.name=Povolit HTTP pipelining
enable-http-pipelining.description=Povolit HTTP/1.1 pipelining.
header.name=Vlastní záhlaví
header.description=Připojit záhlaví k HTTP požadavkovému záhlaví. Uveďte jednu položku na řádek, každá položka obsahuje "název záhlaví: hodnota záhlaví".
save-cookies.name=Cesta k souboru Cookies
save-cookies.description=Uložit cookies do SOUBORU ve formátu Mozilla/Firefox(1.x/2.x)/Netscape. Pokud SOUBOR již existuje, bude přepsán. Session cookies jsou také uloženy a jejich hodnoty vypršení platnosti jsou považovány za 0.
use-head.name=Použít metodu HEAD
use-head.description=Použít metodu HEAD pro první požadavek na HTTP server.
user-agent.name=Uživatelský agent
user-agent.description=Určuje řetězec uživatelského agentu (User-Agent), který bude aria2 používat při komunikaci s webovými servery.
ftp-user.name=Výchozí uživatelské jméno FTP
ftp-user.description=Nastavuje uživatelské jméno, které bude standardně používáno pro ověřování při připojování k serverům FTP.
ftp-passwd.name=Výchozí heslo FTP
ftp-passwd.description=Pokud je uživatelské jméno vloženo, ale heslo v URI chybí, aria2 se pokusí získat heslo z .netrc. Pokud je heslo nalezeno v .netrc, použije jej jako heslo. Pokud ne, použije heslo zadané v této možnosti.
ftp-pasv.name=Pasivní režim
ftp-pasv.description=Použít pasivní režim v FTP. Pokud je zadáno false, bude použit aktivní režim. Tato možnost je ignorována pro přenos SFTP.
ftp-proxy.name=FTP Proxy Server
ftp-proxy.description=Nastaví proxy server pro připojení FTP. Zadejte adresu proxy serveru, přes který budou procházet požadavky FTP.
ftp-proxy-user.name=Uživatelské jméno pro FTP proxy
ftp-proxy-user.description=Nastaví uživatelské jméno, které se použije pro ověření při připojování k serveru proxy FTP.
ftp-proxy-passwd.name=Heslo pro FTP proxy
ftp-proxy-passwd.description=Nastaví heslo pro ověření při připojování k serveru proxy FTP.
ftp-type.name=Typ přenosu
ftp-type.description=Nastavuje typ přenosu pro připojení FTP. Možné hodnoty: passive nebo active.
ftp-reuse-connection.name=Znovu použít připojení
ftp-reuse-connection.description=Umožňuje opětovné použití jediného FTP připojení pro více stahování nebo odesílání, čímž se zvyšuje výkon.
ssh-host-key-md.name=Kontrolní součet veřejného SSH klíče
ssh-host-key-md.description=Nastavte kontrolní součet veřejného SSH klíče. Formát hodnoty možnosti je TYPE=DIGEST. TYPE je typ hash. Podporované typy hash jsou sha-1 nebo md5. DIGEST je hexadecimální digest. Například: sha-1=b030503d4de4539dc7885e6f0f5e256704edf4c3. Tato možnost může být použita k ověření veřejného klíče serveru při použití SFTP. Pokud tato možnost není nastavena (což je výchozí), ověřování neprobíhá.
bt-detach-seed-only.name=Vyloučit pouze seedy
bt-detach-seed-only.description=Vyloučit pouze seedy při počítání současných aktivních stahování (viz možnost -j). To znamená, že pokud je zadáno -j3, tato možnost je zapnuta a 3 stahování jsou aktivní a jedno z nich přejde do režimu seeding, pak je vyňato z počtu aktivních stahování (tím se počet stane 2) a další stahování čekající ve frontě se spustí. Upozorňujeme však, že seeding položka je stále považována za aktivní stahování v metodě RPC.
bt-enable-hook-after-hash-check.name=Povolit hook po kontrolě hash
bt-enable-hook-after-hash-check.description=Povolit spuštění příkazu hook po kontrole hash (viz možnost -V) v BitTorrent stahování. Ve výchozím nastavení, když kontrola hash uspěje, se spustí příkaz zadaný pomocí --on-bt-download-complete. Chcete-li tuto akci zakázat, zadejte false do této možnosti.
bt-enable-lpd.name=Povolit lokální vyhledávání peerů (LPD)
bt-enable-lpd.description=Povolit lokální vyhledávání peerů. Pokud je v torrentu nastaven příznak private, aria2 tuto funkci pro toto stahování nepoužívá, i když je zadáno "Pravda".
bt-exclude-tracker.name=BitTorrent vyloučení trackerů
bt-exclude-tracker.description=Čárkou oddělený seznam URI trackerů BitTorrentu, které mají být odstraněny. Můžete použít speciální hodnotu *, která odpovídá všem URI a tím odstraní všechny URI pro oznamování. Při zadávání * v příkazovém řádku shellu nezapomeňte jej escapovat nebo uzavřít do uvozovek.
bt-external-ip.name=Externí IP
bt-external-ip.description=Zadejte externí IP adresu, která se má použít při stahování pomocí BitTorrentu a DHT. Může být odeslána trackeru BitTorrentu. U DHT by měla být tato možnost nastavena pro oznámení, že lokální uzel stahuje konkrétní torrent. To je důležité při použití DHT v privátní síti. Ačkoliv je tato funkce označena jako externí, může přijímat jakýkoliv typ IP adresy.
bt-force-encryption.name=Vynutit šifrování
bt-force-encryption.description=Vyžaduje šifrování datového obsahu zprávy BitTorrent pomocí arc4. Toto je zkratka pro --bt-require-crypto --bt-min-crypto-level=arc4. Tato možnost nemění hodnotu těchto možností. Pokud je zadáno "Pravda", zamítne starší handshake BitTorrent a použije pouze handshake s obfuskováním a vždy šifruje datový obsah zprávy.
bt-hash-check-seed.name=Kontrola hash před seedováním
bt-hash-check-seed.description=Pokud je zadáno "Pravda", po kontrole hash pomocí --check-integrity možnosti a dokončení souboru pokračuje seedování souboru. Pokud chcete zkontrolovat soubor a stáhnout jej pouze v případě, že je poškozený nebo neúplný, nastavte tuto možnost na false. Tato možnost má vliv pouze na stahování BitTorrent.
bt-load-saved-metadata.name=Načíst uložený soubor metadat
bt-load-saved-metadata.description=Před získáním torrentových metadat z DHT při stahování pomocí magnetického odkazu se nejdříve pokusí přečíst soubor uložený pomocí možnosti --bt-save-metadata. Pokud je to úspěšné, přeskočí se stahování metadat z DHT.
bt-max-open-files.name=Maximální počet otevřených souborů
bt-max-open-files.description=Nastavte maximální počet souborů, které lze otevřít při stahování více souborů pomocí BitTorrent/Metalink globálně.
bt-max-peers.name=Maximální počet peerů
bt-max-peers.description=Nastavte maximální počet peerů na torrent. 0 znamená neomezeně.
bt-metadata-only.name=Stáhnout pouze metadata
bt-metadata-only.description=Stáhněte pouze metadata. Soubory popsané v metadatech nebudou staženy. Tato možnost má vliv pouze při použití BitTorrent Magnet URI.
bt-min-crypto-level.name=Minimální úroveň šifrování
bt-min-crypto-level.description=Nastavte minimální úroveň metody šifrování. Pokud peer poskytuje několik metod šifrování, aria2 vybere nejnižší, která splňuje danou úroveň.
bt-prioritize-piece.name=Upřednostnit část
bt-prioritize-piece.description=Pokuste se nejprve stáhnout první a poslední části každého souboru. To je užitečné pro náhledy souborů. Argument může obsahovat 2 klíčová slova: head a tail. Pro zahrnutí obou klíčových slov musí být oddělena čárkou. Tato klíčová slova mohou mít jeden parametr, SIZE. Například pokud je specifikováno head=SIZE, části v rozsahu prvních SIZE bajtů každého souboru mají vyšší prioritu. tail=SIZE znamená rozsah posledních SIZE bajtů každého souboru. SIZE může obsahovat K nebo M (1K = 1024, 1M = 1024K).
bt-remove-unselected-file.name=Odstranit nevybrané soubory
bt-remove-unselected-file.description=Odstraní nevybrané soubory po dokončení stahování v BitTorrent. Pro výběr souborů použijte možnost --select-file. Pokud není použita, všechny soubory se považují za vybrané. Používejte tuto možnost s opatrností, protože skutečně odstraní soubory z vašeho disku.
bt-require-crypto.name=Vyžadovat šifrování
bt-require-crypto.description=Pokud je zadáno "Pravda", aria2 neakceptuje ani neetabluje spojení se starším handshake BitTorrent (\19BitTorrent protocol). Aria2 tedy vždy používá handshake s obfuskováním.
bt-request-peer-speed-limit.name=Preferovaná rychlost stahování
bt-request-peer-speed-limit.description=Pokud je celková rychlost stahování u všech torrentů nižší než SPEED, aria2 dočasně zvýší počet peerů, aby dosáhla vyšší rychlosti stahování. Nastavení této možnosti na vaši preferovanou rychlost stahování může v některých případech zvýšit rychlost stahování. Můžete přidat K nebo M (1K = 1024, 1M = 1024K).
bt-save-metadata.name=Uložit metadata
bt-save-metadata.description=Uložit metadata jako ".torrent" soubor. Tato možnost má vliv pouze při použití BitTorrent Magnet URI. Název souboru je hexadecimálně kódovaný info hash s příponou ".torrent". Adresář, do kterého je uložen, je stejný jako adresář, kde je uložen stažený soubor. Pokud již stejný soubor existuje, metadata se neuloží.
bt-seed-unverified.name=Neověřovat stažené soubory
bt-seed-unverified.description=Seedujte dříve stažené soubory bez ověření hash částí.
bt-stop-timeout.name=Časový limit zastavení
bt-stop-timeout.description=Zastaví stahování BitTorrent, pokud rychlost stahování je 0 během po sobě jdoucích SEC sekund. Pokud je zadáno 0, tato funkce je deaktivována.
bt-tracker.name=BitTorrent trackery
bt-tracker.description=Čárkou oddělený seznam dodatečných URI trackerů BitTorrentu. Tyto URI nejsou ovlivněny možností --bt-exclude-tracker, protože jsou přidány po odstranění URI uvedených v možnosti --bt-exclude-tracker.
bt-tracker-connect-timeout.name=Časový limit připojení trackeru
bt-tracker-connect-timeout.description=Nastavte časový limit připojení k trackeru v sekundách. Po navázání připojení tato možnost již nemá vliv a místo ní se použije možnost --bt-tracker-timeout.
bt-tracker-interval.name=Interval připojení trackeru
bt-tracker-interval.description=Nastavte interval v sekundách mezi požadavky trackeru. Tento interval zcela přepisuje hodnotu intervalu a aria2 používá pouze tuto hodnotu, ignoruje minimální interval a hodnotu intervalu v odpovědi trackeru. Pokud je nastavena hodnota 0, aria2 určí interval na základě odpovědi trackeru a pokroku stahování.
bt-tracker-timeout.name=Časový limit trackeru
bt-tracker-timeout.description=Nastavuje časový limit pro interakci se sledovači BitTorrent. Určuje dobu čekání na odpověď od sledovače před opakovaným pokusem.
dht-file-path.name=Soubor DHT (IPv4)
dht-file-path.description=Změňte soubor směrovací tabulky DHT IPv4 na PATH.
dht-file-path6.name=Soubor DHT (IPv6)
dht-file-path6.description=Změňte soubor směrovací tabulky DHT IPv6 na PATH.
dht-listen-port.name=Port pro DHT
dht-listen-port.description=Nastavte UDP port pro naslouchání používaný DHT (IPv4, IPv6) a UDP trackerem. Více portů lze zadat pomocí "," například: 6881,6885. Můžete také použít "-" pro určení rozsahu: 6881-6999. "," a "-" lze kombinovat.
dht-message-timeout.name=Časový limit zprávy DHT
dht-message-timeout.description=Nastavuje časový limit pro zasílání zpráv v DHT (Distributed Hash Table). Určuje dobu čekání na odpověď od kolegů v síti DHT
enable-dht.name=Povolit DHT (IPv4)
enable-dht.description=Povolit funkčnost DHT IPv4. Tato možnost také povoluje podporu UDP trackeru. Pokud je v torrentu nastaven příznak privatní, aria2 tuto funkci nepoužije pro dané stahování, i když je zadáno "Pravda".
enable-dht6.name=Povolit DHT (IPv6)
enable-dht6.description=Povolit funkčnost DHT IPv6. Pokud je v torrentu nastaven příznak přivatní, aria2 tuto funkci nepoužije pro dané stahování, i když je zadáno "Pravda". Použijte možnost --dht-listen-port pro zadání čísla portu pro naslouchání.
enable-peer-exchange.name=Povolit výměnu peerů
enable-peer-exchange.description=Povolit rozšíření výměny peerů. Pokud je v torrentu nastaven příznak privatní, tato funkce je pro toto stahování deaktivována, i když je zadáno "Pravda".
follow-torrent.name=Sledovat torrent
follow-torrent.description=Pokud je zadáno "Pravda" nebo pouze paměť, při stažení souboru s příponou .torrent nebo s obsahem typu application/x-bittorrent aria2 jej analyzuje jako torrentový soubor a stáhne v něm zmíněné soubory. Pokud je zadáno pouze paměť, torrentový soubor není uložen na disk, ale zůstává pouze v paměti. Pokud je zadáno "Nepravda", .torrent soubor je stažen na disk, ale není analyzován jako torrent a jeho obsah není stažen.
listen-port.name=Port pro naslouchání
listen-port.description=Nastavte číslo TCP portu pro stahování BitTorrent. Více portů lze zadat pomocí "," například: 6881,6885. Můžete také použít "-" pro určení rozsahu: 6881-6999. "," a "-" lze kombinovat: 6881-6889,6999.
max-overall-upload-limit.name=Globální maximální limit nahrávání
max-overall-upload-limit.description=Nastavte maximální celkovou rychlost nahrávání v bajtech za sekundu. 0 znamená neomezeně. Můžete přidat K nebo M (1K = 1024, 1M = 1024K).
max-upload-limit.name=Maximální limit nahrávání
max-upload-limit.description=Nastavte maximální rychlost nahrávání pro každý torrent v bajtech za sekundu. 0 znamená neomezeně. Můžete přidat K nebo M (1K = 1024, 1M = 1024K).
peer-id-prefix.name=Předpona Peer ID
peer-id-prefix.description=Zadejte předponu ID peeru. Peer ID v BitTorrentu má délku 20 bajtů. Pokud je zadáno více než 20 bajtů, použije se pouze prvních 20. Pokud je zadáno méně než 20 bajtů, přidají se náhodná data, aby byla délka 20 bajtů.
peer-agent.name=Agent peeru
peer-agent.description=Zadejte řetězec použitý během rozšířeného handshaku BitTorrent pro verzi klienta peeru.
seed-ratio.name=Minimální podíl sdílení
seed-ratio.description=Nastavte podíl sdílení. Seedujte dokončené torrenty, dokud podíl sdílení nedosáhne RATIO. Důrazně doporučujeme zadat rovno nebo více než 1.0. Zadejte 0.0, pokud máte v úmyslu seedovat bez ohledu na podíl sdílení. Pokud je tato možnost zadána společně s možností --seed-time, seedování skončí, jakmile je splněna alespoň jedna z podmínek.
seed-time.name=Minimální čas seedování
seed-time.description=Specifikujte dobu sdílení v (desetinných) minutách. Zadání --seed-time=0 zakáže sdílení po dokončení stahování.
follow-metalink.name=Sledovat Metalink
follow-metalink.description=Pokud je nastaveno "Pravda" nebo pouze paměť, při stahování souboru s příponou .meta4 nebo .metalink nebo s typem obsahu application/metalink4+xml nebo application/metalink+xml, aria2 jej interpretuje jako soubor metalink a stáhne soubory v něm uvedené. Pokud je zadáno pouze paměť, soubor metalink se neukládá na disk, ale pouze do paměti. Pokud je zadáno "Nepravda", soubor .metalink se stáhne na disk, ale nebude interpretován jako metalink a jeho obsah se nestáhne.
metalink-base-uri.name=Základní URI
metalink-base-uri.description=Zadejte základní URI pro rozlišení relativního URI v elementech metalink:url a metalink:metaurl v souboru metalink uloženém na lokálním disku. Pokud URI ukazuje na adresář, musí končit znakem /.
metalink-language.name=Jazyk
metalink-language.description=Nastaví jazyk, který se má použít pro metadata Metalink. Jazyk je specifikován jako kód jazyka, například "en" pro angličtinu.
metalink-location.name=Preferované umístění serveru
metalink-location.description=Umístění preferovaného serveru. Je možné zadat seznam umístění oddělený čárkami, například jp,us.
metalink-os.name=Operační systém
metalink-os.description=Operační systém souboru ke stažení.
metalink-version.name=Verze
metalink-version.description=Verze souboru ke stažení.
metalink-preferred-protocol.name=Preferovaný protokol
metalink-preferred-protocol.description=Zadejte preferovaný protokol. Možné hodnoty jsou http, https, ftp a "Žadné". Zadejte "Žadné" pro deaktivaci této funkce.
metalink-enable-unique-protocol.name=Povolit unikátní protokol
metalink-enable-unique-protocol.description=Pokud je zadáno "Pravda" a v souboru metalink je pro zrcadlo dostupných několik protokolů, aria2 použije jeden z nich. Pomocí volby --metalink-preferred-protocol můžete specifikovat preferenci protokolu.
enable-rpc.name=Povolit JSON-RPC/XML-RPC server
enable-rpc.description=Umožňuje rozhraní vzdáleného volání procedur (RPC), které umožňuje ovládat aria2 externími aplikacemi.
pause-metadata.name=Pozastavit po stažení metadat
pause-metadata.description=Pozastavit stahování vytvořená na základě stažených metadat. V aria2 existují tři typy stahování metadat: (1) stahování souboru .torrent, (2) stahování metadat torrentu pomocí magnetického odkazu, (3) stahování souboru metalink. Tato stahování metadat vytvoří stahování pomocí svých metadat. Tato možnost pozastaví tato následná stahování. Tato možnost je účinná pouze tehdy, když je zadáno --enable-rpc=true.
rpc-allow-origin-all.name=Povolit všechny požadavky původu
rpc-allow-origin-all.description=Přidat pole hlavičky Access-Control-Allow-Origin s hodnotou * do odpovědi RPC.
rpc-listen-all.name=Naslouchat na všech síťových rozhraních
rpc-listen-all.description=Naslouchat příchozím požadavkům JSON-RPC/XML-RPC na všech síťových rozhraních. Pokud je zadáno "Nepravda", naslouchá pouze na lokálním loopback rozhraní.
rpc-listen-port.name=Port naslouchání
rpc-listen-port.description=Nastavuje port, na kterém bude aria2 naslouchat požadavkům RPC. Ve výchozím nastavení se používá port 6800.
rpc-max-request-size.name=Maximální velikost požadavku
rpc-max-request-size.description=Nastavte maximální velikost požadavku JSON-RPC/XML-RPC. Pokud aria2 zjistí, že požadavek přesahuje velikost SIZE bajtů, připojení přeruší.
rpc-save-upload-metadata.name=Uložit metadata nahrávání
rpc-save-upload-metadata.description=Uložte metadata nahraných torrentů nebo metalinků do adresáře určeného možností --dir. Název souboru se skládá z hexadecimálního řetězce SHA-1 hashe metadat a přípony. Pro torrent je přípona '.torrent'. Pro metalink je to '.meta4'. Pokud je této možnosti zadáno "Nepravda", stahování přidaná aria2.addTorrent() nebo aria2.addMetalink() nebudou uložena pomocí volby --save-session.
rpc-secure.name=Povolit SSL/TLS
rpc-secure.description=Přenos RPC bude šifrován pomocí SSL/TLS. Klienti RPC musí používat schéma https pro přístup k serveru. Pro klienta WebSocket použijte schéma wss. K určení certifikátu serveru a soukromého klíče použijte možnosti --rpc-certificate a --rpc-private-key.
allow-overwrite.name=Povolit přepsání
allow-overwrite.description=Restartujte stahování od začátku, pokud neexistuje odpovídající kontrolní soubor. Viz také možnost --auto-file-renaming.
allow-piece-length-change.name=Povolit změnu délky dílku
allow-piece-length-change.description=Pokud je zadáno "Nepravda", aria2 ukončí stahování, když se délka dílku liší od délky v kontrolním souboru. Pokud je zadáno "Pravda", můžete pokračovat, ale část pokroku stahování bude ztracena.
always-resume.name=Vždy obnovit stahování
always-resume.description=Vždy obnovit stahování. Pokud je zadáno "Pravda", aria2 vždy zkusí obnovit stahování a pokud obnovení není možné, stahování přeruší. Pokud je zadáno "Nepravda", pokud žádné z uvedených URI nepodporuje obnovení nebo aria2 narazí na N URI, které obnovení nepodporují (N je hodnota zadaná pomocí volby --max-resume-failure-tries), aria2 stáhne soubor od začátku. Viz volba --max-resume-failure-tries.
async-dns.name=Asynchronní DNS
async-dns.description=Umožňuje asynchronní překlad DNS, který může zlepšit výkon při překladu názvů hostitelů.
auto-file-renaming.name=Automatické přejmenování souboru
auto-file-renaming.description=Přejmenujte název souboru, pokud již existuje stejný soubor. Tato volba funguje pouze při stahování přes HTTP(S)/FTP. Nový název souboru bude obsahovat tečku a číslo (1..9999) připojené za názvem, ale před příponou souboru, pokud existuje.
auto-save-interval.name=Interval automatického ukládání
auto-save-interval.description=Uložte kontrolní soubor (*.aria2) každých SEC sekund. Pokud je zadáno 0, kontrolní soubor se během stahování neukládá. aria2 uloží kontrolní soubor, když se stahování zastaví, bez ohledu na hodnotu. Možné hodnoty jsou mezi 0 a 600.
conditional-get.name=Podmíněné stahování
conditional-get.description=Stahujte soubor pouze tehdy, když je místní soubor starší než vzdálený soubor. Tato funkce funguje pouze u stahování HTTP(S). Nefunguje, pokud je velikost souboru specifikována v Metalink. Ignoruje také hlavičku Content-Disposition. Pokud existuje kontrolní soubor, tato volba bude ignorována. Tato funkce používá hlavičku If-Modified-Since k podmíněnému stahování novějšího souboru. Při získávání času úpravy místního souboru se používá název souboru zadaný uživatelem (viz volba --out) nebo část názvu souboru v URI, pokud --out není specifikováno. Pro přepsání existujícího souboru je vyžadováno --allow-overwrite.
conf-path.name=Konfigurační soubor
conf-path.description=Určuje cestu ke konfiguračnímu souboru, který bude použit při spuštění aria2.
console-log-level.name=Úroveň konzolového logu
console-log-level.description=Nastavuje úroveň podrobností pro výstup protokolu konzoly. Dostupné úrovně: ladění, informace, upozornění, varování a chyba.
content-disposition-default-utf8.name=Použít UTF-8 pro zpracování Content-Disposition
content-disposition-default-utf8.description=Zpracovávejte řetězce uvedené v hlavičce Content-Disposition jako UTF-8 namísto ISO-8859-1, například parametr filename, ale ne jeho rozšířenou verzi.
daemon.name=Povolit Daemon
daemon.description=Spustí aria2 na pozadí jako Daemona, čímž uvolní terminál pro další úkoly.
deferred-input.name=Odložené načítání
deferred-input.description=Pokud je zadáno "Pravda", aria2 nečte všechny URI a možnosti ze souboru zadaného volbou --input-file při spuštění, ale čte je po jednom, když je to potřeba. To může snížit spotřebu paměti, pokud vstupní soubor obsahuje velké množství URI ke stažení. Pokud je zadáno "Nepravda", aria2 čte všechny URI a možnosti při spuštění. Možnost --deferred-input bude deaktivována, když je použita spolu s --save-session.
disable-ipv6.name=Deaktivovat IPv6
disable-ipv6.description=Zakáže použití IPv6 pro všechna síťová připojení.
disk-cache.name=Cache na disku
disk-cache.description=Povolit cache na disku. Pokud je velikost SIZE nastavena na 0, cache na disku je deaktivována. Tato funkce ukládá stažená data do paměti, která roste až na velikost SIZE bajtů. Úložný prostor cache je vytvořen pro instanci aria2 a je sdílen mezi všemi stahováními. Výhodou cache na disku je snížení I/O operací na disku, protože data jsou zapisována ve větších blocích a jsou přeorganizována podle offsetu souboru. Pokud je zapojeno kontrolování hashů a data jsou uložena v paměti, není nutné je číst z disku. SIZE může zahrnovat K nebo M (1K = 1024, 1M = 1024K).
download-result.name=Výsledek stahování
download-result.description=Tato volba mění formát výstupu výsledků stahování. Pokud je OPT nastaven na "Výchozí", zobrazí GID, stav, průměrnou rychlost stahování a cestu/URI. Pokud je zapojeno více souborů, je vytištěna cesta/URI prvního požadovaného souboru a ostatní jsou vynechány. Pokud je OPT nastaven na "Úplný", zobrazí GID, stav, průměrnou rychlost stahování, procento pokroku a cestu/URI. Procento pokroku a cesta/URI jsou zobrazeny pro každý požadovaný soubor v každém řádku. Pokud je OPT nastaven na "Skrýt", výsledky stahování jsou skryty.
dscp.name=DSCP
dscp.description=Nastavte hodnotu DSCP v odchozích IP paketech BitTorrent provozu pro QoS. Tento parametr nastaví pouze bity DSCP v poli TOS IP paketů, nikoliv celé pole. Pokud používáte hodnoty z /usr/include/netinet/ip.h, rozdělte je o 4 (jinak by hodnoty byly nesprávné, např. vaše třída CS1 by se změnila na CS4). Pokud používáte běžně používané hodnoty z RFC, dokumentace síťových poskytovatelů, Wikipedie nebo jiných zdrojů, používejte je tak, jak jsou.
rlimit-nofile.name=Měkký limit otevřených popisovačů souborů
rlimit-nofile.description=Nastavte měkký limit pro otevřené popisovače souborů. Tento limit bude mít efekt pouze pokud: a. Systém jej podporuje (posix). b. Limit nepřesahuje tvrdý limit. c. Zadaný limit je větší než aktuální měkký limit. Toto je ekvivalentní nastavení nofile pomocí ulimit, s tím rozdílem, že nikdy nezměníte limit na nižší hodnotu. Tato volba je dostupná pouze na systémech podporujících API rlimit.
enable-color.name=Povolit barvy v terminálu
enable-color.description=Povolí nebo zakáže použití barevného stylu ve výstupu konzoly.
enable-mmap.name=Povolit MMap
enable-mmap.description=Mapujte soubory do paměti. Tato volba nebude fungovat, pokud není prostor pro soubory předem alokován. Viz --file-allocation.
event-poll.name=Metoda volení událostí
event-poll.description=Specifikujte metodu pro volení událostí. Možné hodnoty jsou "Epoll", "Kqueue", "Port", "Poll" a "Vybrat". Pro každou z "Epoll", "Kqueue", "Port" a "Poll", je k dispozici, pokud to systém podporuje. "Epoll" je k dispozici na novějších verzích Linuxu. "Kqueue" je k dispozici na různých systémech *BSD včetně Mac OS X. "Port" je k dispozici na Open Solaris. Výchozí hodnota se může lišit podle použitého systému.
file-allocation.name=Metoda alokace souboru
file-allocation.description=Specifikujte metodu alokace souboru. "Žadné" nealokuje prostor souboru předem. "Předalokace" alokuje prostor souboru před začátkem stahování. To může nějaký čas trvat, v závislosti na velikosti souboru. Pokud používáte novější souborové systémy jako ext4 (s podporou extents), btrfs, xfs nebo NTFS (pouze MinGW verze), "Falloc" je nejlepší volba. Alokuje velké (několik GiB) soubory téměř okamžitě. Nepoužívejte "Falloc" na starších souborových systémech jako ext3 a FAT32, protože to trvá téměř stejně dlouho jako předalokace a zcela zablokuje aria2, dokud alokace neskončí. "Falloc" nemusí být k dispozici, pokud váš systém nemá funkci posix_fallocate(3). "Zkrátit" používá systémový hovor ftruncate(2) nebo platformě specifický ekvivalent pro zkrácení souboru na specifikovanou délku. U multi-souborových torrent stahování jsou alokovány i soubory sousedící vpřed k uvedeným souborům, pokud sdílejí stejný kus.
force-save.name=Vynutit uložení
force-save.description=Uloží stahování pomocí volby --save-session, i když je stahování dokončeno nebo odstraněno. Tato volba také uloží kontrolní soubor v těchto situacích. To může být užitečné pro uložení BitTorrent seeding, které je rozpoznáno jako dokončený stav.
save-not-found.name=Uložit soubor, který nebyl nalezen
save-not-found.description=Uloží stahování pomocí volby --save-session, i když soubor nebyl nalezen na serveru. Tato volba také uloží kontrolní soubor v těchto situacích.
hash-check-only.name=Kontrola hashů pouze
hash-check-only.description=Pokud je zadáno"Pravda", po kontrole hashů pomocí volby --check-integrity, přeruší stahování, bez ohledu na to, zda je stahování dokončeno.
human-readable.name=Konzolový výstup ve formátu čitelném pro člověka
human-readable.description=Tiskněte velikosti a rychlosti ve formátu čitelném pro člověka (např. 1.2Ki, 3.4Mi) v konzolovém výstupu.
keep-unfinished-download-result.name=Udržet nedokončené výsledky stahování
keep-unfinished-download-result.description=Udržujte nedokončené výsledky stahování, i když tím přesáhnete limit --max-download-result. To je užitečné, pokud musí být všechny nedokončené stahování uloženy v souboru relace (viz volba --save-session). Mějte na paměti, že neexistuje žádný horní limit pro počet nedokončených výsledků stahování, které je třeba uchovat. Pokud to není žádoucí, tuto volbu vypněte.
max-download-result.name=Maximální počet výsledků stahování
max-download-result.description=Určuje maximální počet výsledků stahování, které budou uchovávány v paměti. Výsledky stahování zahrnují dokončené/selhané/odstraněné stahování. Výsledky stahování jsou uchovávány v FIFO frontě a může obsahovat maximálně NUM výsledků stahování. Když je fronta plná a je vytvořen nový výsledek stahování, nejstarší výsledek je odstraněn z přední části fronty a nový je přidán na konec. Nastavení vyššího čísla v této volbě může vést k vyšší spotřebě paměti po tisících stahování. Specifikování hodnoty 0 znamená, že výsledky stahování nebudou uchovávány. Nezapomeňte, že neukončené stahování se uchovává v paměti bez ohledu na tuto volbu. Viz také volba --keep-unfinished-download-result.
max-mmap-limit.name=Maximální limit MMap
max-mmap-limit.description=Určuje maximální velikost souboru pro povolení Mmap (viz volba --enable-mmap). Velikost souboru je určena součtem všech souborů obsažených v jednom stahování. Například pokud stahování obsahuje 5 souborů, celková velikost souborů je součet těchto souborů. Pokud je velikost souboru přísně větší než velikost určená touto volbou, Mmap bude zakázáno.
max-resume-failure-tries.name=Maximální počet pokusů o obnovení po selhání
max-resume-failure-tries.description=Při použití s volbou --always-resume=false, aria2 stáhne soubor od začátku, když zjistí N URI, které nepodporují obnovení. Pokud je N rovno 0, aria2 stáhne soubor od začátku, když všechny zadané URI nepodporují obnovení. Viz volba --always-resume.
min-tls-version.name=Minimální verze TLS
min-tls-version.description=Určuje minimální verzi SSL/TLS pro povolení.
log-level.name=Úroveň protokolování
log-level.description=Určuje úroveň protokolování pro aplikaci.
optimize-concurrent-downloads.name=Optimalizovat souběžné stahování
optimize-concurrent-downloads.description=Optimalizuje počet souběžných stahování podle dostupné šířky pásma. aria2 používá rychlost stahování pozorovanou v předchozích stahováních k přizpůsobení počtu stahování spuštěných paralelně podle pravidla N = A + B Log10(rýchlost v Mbps). Koeficienty A a B lze přizpůsobit v argumentech volby s oddělením A a B dvojtečkou. Výchozí hodnoty (A=5, B=25) vedou k používání typických 5 paralelních stahování na 1Mbps síťích a více než 50 na 100Mbps sítích. Počet paralelních stahování zůstává omezen maximem definovaným parametrem --max-concurrent-downloads.
piece-length.name=Velikost bloku
piece-length.description=Určuje velikost bloku pro HTTP/FTP stahování. Toto je hranice, kdy aria2 rozdělí soubor. Všechna dělení probíhají na násobcích této velikosti. Tato volba bude ignorována při BitTorrent stahováních. Bude také ignorována, pokud Metalink soubor obsahuje hashe kousků.
show-console-readout.name=Zobrazit výstup v konzoli
show-console-readout.description=Určuje, zda se má výstup zobrazit v konzole.
summary-interval.name=Interval pro výstup souhrnu stahování
summary-interval.description=Určuje interval v sekundách pro zobrazení souhrnu pokroku stahování. Nastavení na 0 potlačí výstup.
max-overall-download-limit.name=Globální maximální limit stahování
max-overall-download-limit.description=Určuje maximální celkovou rychlost stahování v bajtech za sekundu. 0 znamená neomezeno. Můžete přidat K nebo M (1K = 1024, 1M = 1024K).
max-download-limit.name=Maximální limit stahování
max-download-limit.description=Určuje maximální rychlost stahování pro každý soubor v bajtech za sekundu. 0 znamená neomezeno. Můžete přidat K nebo M (1K = 1024, 1M = 1024K).
no-conf.name=Zakázat konfigurační soubor
no-conf.description=Zakáže načítání konfiguračního souboru.
no-file-allocation-limit.name=Bez limitu alokace souboru
no-file-allocation-limit.description=Bez alokace souboru pro soubory menší než URČITÁ velikost. Můžete přidat K nebo M (1K = 1024, 1M = 1024K).
parameterized-uri.name=Povolit parametrizované URI
parameterized-uri.description=Povolí podporu parametrizovaných URI. Můžete specifikovat sadu částí: http://{sv1,sv2,sv3}/foo.iso. Také můžete specifikovat číselné posloupnosti s krokovým čítačem: http://host/image[000-100:2].img. Krokový čítač může být vynechán. Pokud všechny URI neodkazují na stejný soubor, jako v druhém příkladu výše, je nutná volba -Z.
quiet.name=Zakázat výstup do konzole
quiet.description=Zakáže všechny výstupy do konzole.
realtime-chunk-checksum.name=Reálná kontrola součtů datového bloku
realtime-chunk-checksum.description=Validuje blok dat výpočtem kontrolního součtu během stahování souboru, pokud jsou poskytnuty kontrolní součty bloků.
remove-control-file.name=Odstranit kontrolní soubor
remove-control-file.description=Odstranit kontrolní soubor před stažením. Použití s volbou --allow-overwrite=true vždy začne stahování od začátku. Tato volba bude užitečná pro uživatele za proxy servery, které zakazují obnovení.
save-session.name=Soubor pro uložení sezení
save-session.description=Uloží chybné/nehotové stahování do SOUBORU při ukončení. Tento výstupní soubor můžete předat aria2c s volbou --input-file při restartu. Pokud chcete, aby byl soubor uložen v komprimovaném formátu, přidejte příponu .gz.
save-session-interval.name=Interval pro ukládání sezení
save-session-interval.description=Uloží chybné/nehotové stahování do souboru specifikovaného volbou --save-session každých SEC sekund. Pokud je zadáno 0, soubor se uloží pouze při ukončení aria2.
socket-recv-buffer-size.name=Velikost přijímacího bufferu socketu
socket-recv-buffer-size.description=Určuje maximální velikost přijímacího bufferu socketu v bajtech. Pokud zadáte 0, tato volba bude zakázána. Tato hodnota bude nastavena na socket souborového deskriptoru pomocí socketové volby SO_RCVBUF a funkce setsockopt().
stop.name=Automatické vypnutí
stop.description=Zastaví aplikaci po uplynutí SEC sekund. Pokud je zadáno 0, tato funkce je zakázána.
truncate-console-readout.name=Oříznout výstup v konzoli
truncate-console-readout.description=Ořízne výstup do konzole tak, aby se vešel na jeden řádek.
