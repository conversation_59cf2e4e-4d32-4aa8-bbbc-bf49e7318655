[global]
AriaNg Version=AriaNg Version
Operation Result=Ausführungsergebnis
Operation Succeeded=Ausführung erfolgreich
is connected=verbunden
Error=Fehler
OK=OK
Confirm=Bestätigen
Cancel=Abbrechen
Close=Schließen
True=Ja
False=Nein
DEBUG=Debug
INFO=Info
WARN=Warnung
ERROR=Fehler
Connecting=Verbinden
Connected=Verbunden
Disconnected=Getrennt
Reconnecting=Wiederverbinden
Waiting to reconnect=Warte auf erneute verbindung
Global=Global
New=Neu
Start=Starten
Pause=Pause
Retry=Nochmal
Retry Selected Tasks=Wiederholte ausgewählte Aufgaben
Delete=Löschen
Select All=Alle auswählen
Select None=Nichts auswählen
Select Invert=Auswahl umkehren
Select All Failed Tasks=Wähle alle fehlgeschlagenen Aufgaben
Select All Completed Tasks=Wähle alle erfolgreichen Aufgaben
Select All Tasks=Wähle alle Aufgaben
Display Order=Anzeigereihenfolge
Copy Download Url=Kopiere Download URL
Copy Magnet Link=Kopiere Magnet Link
Help=Hilfe
Search=Suche
Default=Standard
Expand=Erweitern
Collapse=Reduzieren
Expand All=Alle erweitern
Collapse All=Alle reduzieren
Open=Öffnen
Save=Speichern
Import=Importieren
Remove Task=Aufgabe entfernen
Remove Selected Task=Ausgewählte Aufgabe entfernen
Clear Stopped Tasks=Gestoppte Aufgaben löschen
Click to view task detail=Klicken, um Aufgabendetails anzuzeigen
By File Name=Nach Dateiname
By File Size=Nach Dateigröße
By Progress=Nach Fortschritt
By Selected Status=Nach ausgewähltem Status
By Remaining=Nach verbleibender Zeit
By Download Speed=Nach Download-Geschwindigkeit
By Upload Speed=Nach Upload-Geschwindigkeit
By Peer Address=Nach Peer-Adresse
By Client Name=Nach Client-Name
Filters=Filter
Download=Herunterladen
Upload=Hochladen
Downloading=Wird heruntergeladen
Pending Verification=Ausstehende Überprüfung
Verifying=Überprüfen
Seeding=Verteilen
Waiting=Warten
Paused=Pausiert
Completed=Abgeschlossen
Error Occurred=Fehler aufgetreten
Removed=Entfernt
Finished / Stopped=Beendet / Gestoppt
Uncompleted=Unvollständig
Click to pin=Klicken zum Anheften
Settings=Einstellungen
AriaNg Settings=AriaNg Einstellungen
Aria2 Settings=Aria2 Einstellungen
Basic Settings=Grundeinstellungen
HTTP/FTP/SFTP Settings=HTTP/FTP/SFTP Einstellungen
HTTP Settings=HTTP Einstellungen
FTP/SFTP Settings=FTP/SFTP Einstellungen
BitTorrent Settings=BitTorrent Einstellungen
Metalink Settings=Metalink Einstellungen
RPC Settings=RPC Einstellungen
Advanced Settings=Erweiterte Einstellungen
AriaNg Debug Console=AriaNg Debug-Konsole
Aria2 Status=Aria2 Status
File Name=Dateiname
File Size=Dateigröße
Progress=Fortschritt
Share Ratio=Teilverhältnis
Remaining=Verbleibend
Download Speed=Download-Geschwindigkeit
Upload Speed=Upload-Geschwindigkeit
Links=Links
Torrent File=Torrent-Datei
Metalink File=Metalink-Datei
File Name:=Dateiname:
Options=Optionen
Overview=Übersicht
Pieces=Teile
Files=Dateien
Peers=Peers
Task Name=Aufgabenname
Task Size=Aufgabengröße
Task Status=Aufgabenstatus
Error Description=Fehlerbeschreibung
Health Percentage=Gesundheitsprozentsatz
Info Hash=Info-Hash
Seeders=Seeder
Connections=Verbindungen
Seed Creation Time=Seed-Erstellungszeit
Download Url=Download-URL
Download Dir=Download-Verzeichnis
BT Tracker Servers=BT-Tracker-Server
Copy=Kopieren
(Choose Files)=(Dateien auswählen)
Videos=Videos
Audios=Audios
Pictures=Bilder
Documents=Dokumente
Applications=Anwendungen
Archives=Archive
Other=Andere
Custom=Benutzerdefiniert
Custom Choose File=Benutzerdefinierte Dateiauswahl
Address=Adresse
Client=Client
Status=Status
Speed=Geschwindigkeit
(local)=(lokal)
No Data=Keine Daten
No connected peers=Keine verbundenen Peers
Failed to change some tasks state.=Fehler beim Ändern des Status einiger Aufgaben.
Confirm Retry=Erneut versuchen bestätigen
Are you sure you want to retry the selected task? AriaNg will create same task after clicking OK.=Sind Sie sicher, dass Sie die ausgewählte Aufgabe erneut versuchen möchten? AriaNg erstellt nach Klick auf OK dieselbe Aufgabe.
Failed to retry this task.=Fehler beim erneuten Versuch dieser Aufgabe.
{successCount} tasks have been retried and {failedCount} tasks are failed.={{successCount}} Aufgaben wurden erneut versucht und {{failedCount}} Aufgaben sind fehlgeschlagen.
Confirm Remove=Entfernen bestätigen
Are you sure you want to remove the selected task?=Sind Sie sicher, dass Sie die ausgewählte Aufgabe entfernen möchten?
Failed to remove some task(s).=Fehler beim Entfernen einiger Aufgaben.
Confirm Clear=Löschen bestätigen
Are you sure you want to clear stopped tasks?=Sind Sie sicher, dass Sie gestoppte Aufgaben löschen möchten?
Download Links:=Download-Links:
Download Now=Jetzt herunterladen
Download Later=Später herunterladen
Open Torrent File=Torrent-Datei öffnen
Open Metalink File=Metalink-Datei öffnen
Support multiple URLs, one URL per line.=Mehrere URLs werden unterstützt; eine URL pro Zeile.
Your browser does not support loading file!=Ihr Browser unterstützt das Laden von Dateien nicht!
The selected file type is invalid!=Der ausgewählte Dateityp ist ungültig!
Failed to load file!=Datei konnte nicht geladen werden!
Download Completed=Download abgeschlossen
BT Download Completed=BT-Download abgeschlossen
Download Error=Download-Fehler
AriaNg Url=AriaNg-URL
Command API Url=Command-API-URL
Export Command API=Command-API exportieren
Export=Exportieren
Copied=Kopiert
Pause After Task Created=Nach Erstellung der Aufgabe pausieren
Language=Sprache
Theme=Design
Light=Hell
Dark=Dunkel
Follow system settings=Systemeinstellungen folgen
Debug Mode=Debug-Modus
Page Title=Seitentitel
Preview=Vorschau
Tips: You can use the "noprefix" tag to ignore the prefix, "nosuffix" tag to ignore the suffix, and "scale\=n" tag to set the decimal precision.=Tipp: Sie können das Tag "noprefix" verwenden, um das Präfix zu ignorieren, "nosuffix", um das Suffix zu ignorieren, und "scale\=n", um die Dezimalgenauigkeit festzulegen.
Example: ${downspeed:noprefix:nosuffix:scale\=1}=Beispiel: ${downspeed:noprefix:nosuffix:scale\=1}
Updating Page Title Interval=Intervall für Seitentitel-Aktualisierung
Enable Browser Notification=Browser-Benachrichtigung aktivieren
Browser Notification Sound=Benachrichtigungston
Browser Notification Frequency=Benachrichtigungshäufigkeit
Unlimited=Unbegrenzt
High (Up to 10 Notifications / 1 Minute)=Hoch (Bis zu 10 Benachrichtigungen / Minute)
Middle (Up to 1 Notification / 1 Minute)=Mittel (Bis zu 1 Benachrichtigung / Minute)
Low (Up to 1 Notification / 5 Minutes)=Niedrig (Bis zu 1 Benachrichtigung / 5 Minuten)
WebSocket Auto Reconnect Interval=WebSocket-Auto-Wiederverbindungsintervall
Aria2 RPC Alias=Aria2-RPC-Alias
Aria2 RPC Address=Aria2-RPC-Adresse
Aria2 RPC Protocol=Aria2-RPC-Protokoll
Aria2 RPC Http Request Method=Aria2-RPC-HTTP-Anfragemethode
POST method only supports aria2 v1.15.2 and above.=POST-Methode wird nur von aria2 v1.15.2 und höher unterstützt.
Aria2 RPC Request Headers=Aria2-RPC-Anfrage-Header
Support multiple request headers, one header per line, each line containing "header name: header value".=Mehrere Anfrage-Header werden unterstützt; ein Header pro Zeile, jede Zeile enthält "Headername: Headerwert".
Aria2 RPC Secret Token=Aria2-RPC-Geheimtoken
Activate=Aktivieren
Reset Settings=Einstellungen zurücksetzen
Confirm Reset=Zurücksetzen bestätigen
Are you sure you want to reset all settings?=Sind Sie sicher, dass Sie alle Einstellungen zurücksetzen möchten?
Clear Settings History=Einstellungshistorie löschen
Are you sure you want to clear all settings history?=Sind Sie sicher, dass Sie die gesamte Einstellungshistorie löschen möchten?
Delete RPC Setting=RPC-Einstellung löschen
Add New RPC Setting=Neue RPC-Einstellung hinzufügen
Are you sure you want to remove rpc setting "{rpcName}"?=Sind Sie sicher, dass Sie die RPC-Einstellung "{{rpcName}}" entfernen möchten?
Updating Global Stat Interval=Intervall für globale Statistik-Aktualisierung
Updating Task Information Interval=Intervall für Aufgabeninfo-Aktualisierung
Keyboard Shortcuts=Tastenkombinationen
Supported Keyboard Shortcuts=Unterstützte Tastenkombinationen
Set Focus On Search Box=Fokus auf Suchfeld setzen
Swipe Gesture=Wischgeste
Change Tasks Order by Drag-and-drop=Aufgabenreihenfolge per Drag-and-drop ändern
Action After Creating New Tasks=Aktion nach Erstellung neuer Aufgaben
Navigate to Task List Page=Zur Aufgabenlisten-Seite navigieren
Navigate to Task Detail Page=Zur Aufgabendetail-Seite navigieren
Action After Retrying Task=Aktion nach erneutem Versuch der Aufgabe
Navigate to Downloading Tasks Page=Zur Seite "Wird heruntergeladen" navigieren
Stay on Current Page=Auf aktueller Seite bleiben
Remove Old Tasks After Retrying=Alte Aufgaben nach erneutem Versuch entfernen
Confirm Task Removal=Entfernen der Aufgabe bestätigen
Include Prefix When Copying From Task Details=Präfix beim Kopieren aus Aufgabendetails einbeziehen
Show Pieces Info In Task Detail Page=Teile-Info auf Aufgabendetail-Seite anzeigen
Pieces Amount is Less than or Equal to {value}=Anzahl der Teile ist kleiner oder gleich {{value}}
RPC List Display Order=Anzeige-Reihenfolge der RPC-Liste
Each Task List Page Uses Independent Display Order=Jede Aufgabenlisten-Seite verwendet eigene Anzeige-Reihenfolge
Recently Used=Kürzlich verwendet
RPC Alias=RPC-Alias
Import / Export AriaNg Settings=AriaNg-Einstellungen importieren/exportieren
Import Settings=Einstellungen importieren
Export Settings=Einstellungen exportieren
AriaNg settings data=AriaNg-Einstellungsdaten
Confirm Import=Import bestätigen
Are you sure you want to import all settings?=Sind Sie sicher, dass Sie alle Einstellungen importieren möchten?
Invalid settings data format!=Ungültiges Einstellungsdatenformat!
Data has been copied to clipboard.=Daten wurden in die Zwischenablage kopiert.
Supported Placeholder=Unterstützte Platzhalter
AriaNg Title=AriaNg-Titel
Current RPC Alias=Aktueller RPC-Alias
Downloading Count=Anzahl der Downloads
Waiting Count=Anzahl der Wartenden
Stopped Count=Anzahl der Gestoppten
You have disabled notification in your browser. You should change your browser's settings before you enable this function.=Sie haben Benachrichtigungen in Ihrem Browser deaktiviert. Sie sollten die Einstellungen Ihres Browsers ändern, bevor Sie diese Funktion aktivieren.
Language resource has been updated, please reload the page for the changes to take effect.=Die Sprachressource wurde aktualisiert, bitte laden Sie die Seite neu, damit die Änderungen wirksam werden.
Configuration has been modified, please reload the page for the changes to take effect.=Die Konfiguration wurde geändert, bitte laden Sie die Seite neu, damit die Änderungen wirksam werden.
Reload AriaNg=AriaNg neu laden
Show Secret=Geheimnis anzeigen
Hide Secret=Geheimnis verbergen
Aria2 Version=Aria2-Version
Enabled Features=Aktivierte Funktionen
Operations=Operationen
Reconnect=Neu verbinden
Save Session=Sitzung speichern
Shutdown Aria2=Aria2 herunterfahren
Confirm Shutdown=Herunterfahren bestätigen
Are you sure you want to shutdown aria2?=Sind Sie sicher, dass Sie aria2 herunterfahren möchten?
Session has been saved successfully.=Sitzung wurde erfolgreich gespeichert.
Aria2 has been shutdown successfully.=Aria2 wurde erfolgreich heruntergefahren.
Toggle Navigation=Navigation umschalten
Shortcut=Kurzbefehl
Global Rate Limit=Globale Geschwindigkeitsbegrenzung
Loading=Laden...
More Than One Day=Mehr als 1 Tag
Unknown=Unbekannt
Bytes=Bytes
Hours=Stunden
Minutes=Minuten
Seconds=Sekunden
Milliseconds=Millisekunden
Http=Http
Http (Disabled)=Http (Deaktiviert)
Https=Https
WebSocket=WebSocket
WebSocket (Disabled)=WebSocket (Deaktiviert)
WebSocket (Security)=WebSocket (Sicherheit)
Http and WebSocket would be disabled when accessing AriaNg via Https.=Http und WebSocket werden deaktiviert, wenn AriaNg über Https aufgerufen wird.
POST=POST
GET=GET
Enabled=Aktiviert
Disabled=Deaktiviert
Always=Immer
Never=Nie
BitTorrent=BitTorrent
Changes to the settings take effect after refreshing page.=Änderungen an den Einstellungen werden nach dem Aktualisieren der Seite wirksam.
Logging Time=Protokollierungszeit
Log Level=Protokollierungsstufe
Auto Refresh=Automatisch aktualisieren
Refresh Now=Jetzt aktualisieren
Clear Logs=Protokolle löschen
Are you sure you want to clear debug logs?=Sind Sie sicher, dass Sie die Debug-Protokolle löschen möchten?
Show Detail=Details anzeigen
Log Detail=Protokolldetails
Aria2 RPC Debug=Aria2-RPC-Debug
Aria2 RPC Request Method=Aria2-RPC-Anfragemethode
Aria2 RPC Request Parameters=Aria2-RPC-Anfrageparameter
Aria2 RPC Response=Aria2-RPC-Antwort
Execute=Ausführen
RPC method is illegal!=RPC-Methode ist ungültig!
AriaNg does not support this RPC method!=AriaNg unterstützt diese RPC-Methode nicht!
RPC request parameters are invalid!=RPC-Anfrageparameter sind ungültig!
Type is illegal!=Typ ist ungültig!
Parameter is invalid!=Parameter ist ungültig!
Option value cannot be empty!=Optionswert darf nicht leer sein!
Input number is invalid!=Eingegebene Zahl ist ungültig!
Input number is below min value!=Eingegebene Zahl liegt unter dem Mindestwert {{value}}!
Input number is above max value!=Eingegebene Zahl liegt über dem Maximalwert {{value}}!
Input value is invalid!=Eingegebener Wert ist ungültig!
Protocol is invalid!=Protokoll ist ungültig!
RPC host cannot be empty!=RPC-Host darf nicht leer sein!
RPC secret is not base64 encoded!=RPC-Geheimnis ist nicht base64-codiert!
URL is not base64 encoded!=URL ist nicht base64-codiert!
Tap to configure and get started with AriaNg.=Tippen Sie, um AriaNg zu konfigurieren und zu starten.
Cannot initialize WebSocket!=WebSocket kann nicht initialisiert werden!
Cannot connect to aria2!=Verbindung zu aria2 kann nicht hergestellt werden!
Access Denied!=Zugriff verweigert!
You cannot use AriaNg because this browser does not meet the minimum requirements for data storage.=Sie können AriaNg nicht verwenden, da Ihr Browser die Mindestanforderungen für die Datenspeicherung nicht erfüllt.

[error]
unknown=Unbekannter Fehler ist aufgetreten.
operation.timeout=Vorgang hat zu lange gedauert.
resource.notfound=Ressource wurde nicht gefunden.
resource.notfound.max-file-not-found=Ressource wurde nicht gefunden. Siehe Option --max-file-not-found.
download.aborted.lowest-speed-limit=Download wurde abgebrochen, da die Download-Geschwindigkeit zu niedrig war. Siehe Option --lowest-speed-limit.
network.problem=Netzwerkproblem ist aufgetreten.
resume.notsupported=Der Remote-Server unterstützt keine Wiederaufnahme.
space.notenough=Nicht genügend Speicherplatz verfügbar.
piece.length.different=Stücklänge unterscheidet sich von der in der .aria2-Steuerdatei. Siehe Option --allow-piece-length-change.
download.sametime=aria2 hat die gleiche Datei bereits heruntergeladen.
download.torrent.sametime=aria2 hat die gleiche Datei bereits heruntergeladen.
file.exists=Datei existiert bereits. Siehe Option --allow-overwrite.
file.rename.failed=Datei konnte nicht umbenannt werden. Siehe Option --auto-file-renaming.
file.open.failed=Vorhandene Datei konnte nicht geöffnet werden.
file.create.failed=Neue Datei konnte nicht erstellt oder vorhandene Datei konnte nicht gekürzt werden.
io.error=Dateisystemfehler ist aufgetreten.
directory.create.failed=Verzeichnis konnte nicht erstellt werden.
name.resolution.failed=Domainname konnte nicht aufgelöst werden.
metalink.file.parse.failed=Metalink-Dokument konnte nicht verarbeitet werden.
ftp.command.failed=FTP-Befehl ist fehlgeschlagen.
http.response.header.bad=HTTP-Antwort-Header war fehlerhaft oder unerwartet.
redirects.toomany=Zu viele Weiterleitungen sind aufgetreten.
http.authorization.failed=HTTP-Authentifizierung ist fehlgeschlagen.
bencoded.file.parse.failed=Bencoded-Datei (meistens ".torrent"-Datei) konnte nicht verarbeitet werden.
torrent.file.corrupted=Die ".torrent"-Datei war beschädigt oder enthielt nicht die benötigten Informationen für aria2.
magnet.uri.bad=Magnet-URI war fehlerhaft.
option.bad=Ungültige/unerkannte Option oder unerwartetes Optionsargument wurde angegeben.
server.overload=Der Remote-Server konnte die Anfrage aufgrund einer vorübergehenden Überlastung oder Wartung nicht bearbeiten.
rpc.request.parse.failed=JSON-RPC-Anfrage konnte nicht verarbeitet werden.
checksum.failed=Prüfsummenvalidierung ist fehlgeschlagen.

[languages]
Czech=Tschechisch
German=Deutsch
English=Englisch
Spanish=Spanisch
French=Französisch
Italian=Italienisch
Polish=Polisch
Russian=Russisch
Simplified Chinese=Einfaches Chinesisch
Traditional Chinese=Traditionelles Chinesisch

[format]
longdate=MM/DD/YYYY HH:mm:ss
time.millisecond={{value}} Millisekunde
time.milliseconds={{value}} Millisekunden
time.second={{value}} Sekunde
time.seconds={{value}} Sekunden
time.minute={{value}} Minute
time.minutes={{value}} Minuten
time.hour={{value}} Stunde
time.hours={{value}} Stunden
requires.aria2-version=Benötigt aria2 v{{version}} oder höher
task.new.download-links=Download Links ({{count}} Links):
task.pieceinfo=Abgeschlossen: {{completed}}, Gesamt: {{total}}
task.error-occurred=Fehler aufgetreten ({{errorcode}})
task.verifying-percent=Verifizieren ({{verifiedPercent}}%)
settings.file-count=({{count}} Dateien)
settings.total-count=(Gesamtanzahl: {{count}})
debug.latest-logs=Letzten {{count}} Logs

[rpc.error]
unauthorized=Authorisierung fehlgeschlagen!

[option]
true=Wahr
false=Falsch
default=Standard
none=Keine
hide=Ausblenden
full=Vollständig
http=Http
https=Https
ftp=Ftp
mem=Nur Speicher
get=GET
tunnel=TUNNEL
plain=Klartext
arc4=ARC4
binary=Binär
ascii=ASCII
debug=Debug
info=Info
notice=Hinweis
warn=Warnung
error=Fehler
adaptive=Adaptiv
epoll=epoll
falloc=falloc
feedback=Rückmeldung
geom=Geometrisch
inorder=In Reihenfolge
kqueue=kqueue
poll=poll
port=port
prealloc=Vorabzuweisung
random=Zufällig
select=Auswählen
trunc=Abschneiden
SSLv3=SSLv3
TLSv1=TLSv1
TLSv1.1=TLSv1.1
TLSv1.2=TLSv1.2

[options]
dir.name=Download-Pfad
dir.description=Gibt das Verzeichnis an, in dem die heruntergeladenen Dateien gespeichert werden sollen.
log.name=Protokolldatei
log.description=Der Dateiname der Protokolldatei. Wenn - angegeben ist, wird das Protokoll auf stdout geschrieben. Wenn ein leerer String ("") angegeben ist oder diese Option weggelassen wird, wird kein Protokoll auf die Festplatte geschrieben.
max-concurrent-downloads.name=Maximale gleichzeitige Downloads
max-concurrent-downloads.description=Legt die maximale Anzahl von Dateien fest, die aria2 auf einmal herunterladen kann.
check-integrity.name=Integrität prüfen
check-integrity.description=Dateiintegrität durch Validierung von Stück-Hashes oder eines Hashs der gesamten Datei prüfen. Diese Option wirkt sich nur auf BitTorrent-, Metalink-Downloads mit Prüfsummen oder HTTP(S)/FTP-Downloads mit der Option --checksum aus.
continue.name=Download fortsetzen
continue.description=Fortsetzen einer teilweise heruntergeladenen Datei. Verwenden Sie diese Option, um einen Download fortzusetzen, der von einem Webbrowser oder einem anderen Programm gestartet wurde, das Dateien sequentiell vom Anfang herunterlädt. Derzeit ist diese Option nur für HTTP(S)/FTP-Downloads anwendbar.
all-proxy.name=Proxy-Server
all-proxy.description=Proxy-Server für alle Protokolle verwenden. Sie können diese Einstellung überschreiben und einen Proxy-Server für ein bestimmtes Protokoll mit --http-proxy, --https-proxy und --ftp-proxy angeben. Dies betrifft alle Downloads. Das Format von PROXY ist [http://][USER:PASSWORD@]HOST[:PORT].
all-proxy-user.name=Proxy-Benutzername
all-proxy-user.description=Gibt den Benutzernamen für die Authentifizierung bei der Verbindung mit allen Proxyservern an.
all-proxy-passwd.name=Proxy-Passwort
all-proxy-passwd.description=Gibt das Passwort für die Authentifizierung bei der Verbindung mit allen Proxyservern an.
checksum.name=Prüfsumme
checksum.description=Prüfsumme festlegen. Das Format des Optionswerts ist TYP=DIGEST. TYP ist der Hash-Typ. Die unterstützten Hash-Typen sind in Hash Algorithms in aria2c -v aufgeführt. DIGEST ist der hexadezimale Digest. Beispiel: sha-1=0192ba11326fe2298c8cb4de616f4d4140213838 Diese Option gilt nur für HTTP(S)/FTP-Downloads.
connect-timeout.name=Verbindungs-Timeout
connect-timeout.description=Verbindungs-Timeout in Sekunden zum Aufbau der Verbindung zu HTTP/FTP/Proxy-Server festlegen. Nach dem Verbindungsaufbau hat diese Option keine Wirkung mehr und stattdessen wird die Option --timeout verwendet.
dry-run.name=Testlauf
dry-run.description=Wenn true angegeben ist, prüft aria2 nur, ob die Remote-Datei verfügbar ist und lädt keine Daten herunter. Diese Option wirkt sich auf HTTP/FTP-Downloads aus. BitTorrent-Downloads werden abgebrochen, wenn true angegeben ist.
lowest-speed-limit.name=Minimale Geschwindigkeit
lowest-speed-limit.description=Verbindung schließen, wenn die Download-Geschwindigkeit kleiner oder gleich diesem Wert (Bytes pro Sekunde) ist. 0 bedeutet, dass aria2 keine minimale Geschwindigkeit hat. Sie können K oder M anhängen (1K = 1024, 1M = 1024K). Diese Option betrifft BitTorrent-Downloads nicht.
max-connection-per-server.name=Maximale Verbindungen pro Server
max-connection-per-server.description=Legt die maximale Anzahl der Verbindungen fest, die aria2 gleichzeitig mit einem Server aufbauen kann, um eine Datei herunterzuladen. Dies hilft, die Download-Geschwindigkeit zu optimieren, indem eine übermäßige Serverlast vermieden wird.
max-file-not-found.name=Maximale "Datei nicht gefunden"-Versuche
max-file-not-found.description=Wenn aria2 vom Remote-HTTP/FTP-Server NUM-mal den Status "Datei nicht gefunden" erhält, ohne ein einziges Byte zu bekommen, wird der Download erzwungen abgebrochen. 0 deaktiviert diese Option. Diese Option ist nur bei HTTP/FTP-Servern wirksam. Die Anzahl der Wiederholungsversuche wird auf --max-tries angerechnet, daher sollte diese ebenfalls konfiguriert werden.
max-tries.name=Maximale Versuche
max-tries.description=Anzahl der Versuche festlegen. 0 bedeutet unbegrenzt.
min-split-size.name=Minimale Split-Größe
min-split-size.description=aria2 teilt keinen Bereich kleiner als 2*GRÖSSE Byte. Beispiel: Beim Download einer 20MiB-Datei. Wenn GRÖSSE 10M ist, kann aria2 die Datei in 2 Bereiche [0-10MiB) und [10MiB-20MiB) aufteilen und mit 2 Quellen herunterladen (wenn --split >= 2). Wenn GRÖSSE 15M ist, da 2*15M > 20MiB, wird die Datei nicht geteilt und mit 1 Quelle heruntergeladen. Sie können K oder M anhängen (1K = 1024, 1M = 1024K). Mögliche Werte: 1M-1024M.
netrc-path.name=.netrc-Pfad
netrc-path.description=Gibt den Pfad zur .netrc-Datei an, die für die Authentifizierung bei der Verbindung mit dem Server verwendet werden soll.
no-netrc.name=.netrc deaktivieren
no-netrc.description=Deaktiviert die Verwendung der .netrc-Datei für die Authentifizierung. Wenn sie installiert ist, wird aria2 nicht nach dieser Datei suchen und sie nicht zur Authentifizierung verwenden.
no-proxy.name=Keine Proxy-Liste
no-proxy.description=Geben Sie eine durch Kommas getrennte Liste von Hostnamen, Domains und Netzwerkadressen mit oder ohne Subnetzmaske an, bei denen kein Proxy verwendet werden soll.
out.name=Dateiname
out.description=Der Dateiname der heruntergeladenen Datei. Er ist immer relativ zum Verzeichnis, das mit der Option --dir angegeben wurde. Wenn die Option --force-sequential verwendet wird, wird diese Option ignoriert.
proxy-method.name=Proxy-Methode
proxy-method.description=Methode für Proxy-Anfrage festlegen. METHODE ist entweder GET oder TUNNEL. HTTPS-Downloads verwenden immer TUNNEL, unabhängig von dieser Option.
remote-time.name=Remote-Datei-Zeitstempel
remote-time.description=Zeitstempel der Remote-Datei vom HTTP/FTP-Server abrufen und, falls verfügbar, auf die lokale Datei anwenden.
reuse-uri.name=URI wiederverwenden
reuse-uri.description=Bereits verwendete URIs wiederverwenden, wenn keine unbenutzten URIs mehr vorhanden sind.
retry-wait.name=Wartezeit bei Wiederholung
retry-wait.description=Sekunden festlegen, die zwischen Wiederholungen gewartet werden. Wenn SEC > 0, wiederholt aria2 Downloads, wenn der HTTP-Server eine 503-Antwort zurückgibt.
server-stat-of.name=Server-Statistik-Ausgabe
server-stat-of.description=Dateiname angeben, in den das Leistungsprofil der Server gespeichert wird. Sie können gespeicherte Daten mit der Option --server-stat-if laden.
server-stat-timeout.name=Server-Statistik-Timeout
server-stat-timeout.description=Timeout in Sekunden angeben, um das Leistungsprofil der Server seit dem letzten Kontakt zu ihnen ungültig zu machen.
split.name=Split-Anzahl
split.description=Datei mit N Verbindungen herunterladen. Wenn mehr als N URIs angegeben sind, werden die ersten N URIs verwendet und die übrigen als Backup. Wenn weniger als N URIs angegeben sind, werden diese URIs mehrmals verwendet, sodass insgesamt N Verbindungen gleichzeitig hergestellt werden. Die Anzahl der Verbindungen zum selben Host wird durch die Option --max-connection-per-server eingeschränkt.
stream-piece-selector.name=Stückauswahl-Algorithmus
stream-piece-selector.description=Stückauswahl-Algorithmus für HTTP/FTP-Download festlegen. Stück bedeutet ein Segment fester Länge, das im parallelen Download heruntergeladen wird. Bei default wählt aria2 Stücke so aus, dass die Anzahl der Verbindungsaufbauten reduziert wird. Dies ist das vernünftige Standardverhalten, da Verbindungsaufbau teuer ist. Bei inorder wählt aria2 das Stück mit dem kleinsten Index. Index=0 bedeutet Anfang der Datei. Dies ist nützlich, um Filme während des Downloads anzusehen. Die Option --enable-http-pipelining kann helfen, den Overhead durch erneute Verbindungen zu reduzieren. Beachten Sie, dass aria2 die Option --min-split-size beachtet, daher sollte ein sinnvoller Wert für --min-split-size angegeben werden. Bei random wählt aria2 Stücke zufällig aus. Wie bei inorder wird --min-split-size beachtet. Bei geom wählt aria2 zu Beginn Stücke mit dem kleinsten Index wie bei inorder, aber es hält exponentiell zunehmend Abstand zu zuvor gewählten Stücken. Dies reduziert die Anzahl der Verbindungsaufbauten und lädt gleichzeitig den Anfang der Datei zuerst herunter. Dies ist nützlich, um Filme während des Downloads anzusehen.
timeout.name=Timeout
timeout.description=Gibt eine Zeitüberschreitung für alle Web-Transaktionen an. Wenn der Vorgang nicht innerhalb der angegebenen Zeit abgeschlossen wird, wird er abgebrochen. Der Wert wird in Sekunden angegeben.
uri-selector.name=URI-Auswahl-Algorithmus
uri-selector.description=URI-Auswahl-Algorithmus festlegen. Mögliche Werte sind inorder, feedback und adaptive. Bei inorder wird die URI in der Reihenfolge der URI-Liste versucht. Bei feedback verwendet aria2 die Download-Geschwindigkeit aus vorherigen Downloads und wählt den schnellsten Server aus der URI-Liste. Dies überspringt auch effektiv tote Mirrors. Die beobachtete Download-Geschwindigkeit ist Teil des Leistungsprofils der Server, wie in --server-stat-of und --server-stat-if erwähnt. Bei adaptive werden die besten Mirrors für die ersten und reservierten Verbindungen ausgewählt. Für ergänzende Verbindungen werden Mirrors zurückgegeben, die noch nicht getestet wurden, und wenn alle getestet wurden, Mirrors, die erneut getestet werden müssen. Andernfalls werden keine weiteren Mirrors ausgewählt. Wie bei feedback wird ein Leistungsprofil der Server verwendet.
check-certificate.name=Zertifikat prüfen
check-certificate.description=Legt fest, ob aria2 SSL-Zertifikate bei der Verbindung zu HTTPS-Servern authentifiziert. Bei „True“ prüft aria2 die Zertifikate, bei „False“ ignoriert es sie.
http-accept-gzip.name=GZip akzeptieren
http-accept-gzip.description=Sendet Accept: deflate, gzip im Request-Header und entpackt die Antwort, wenn der Remote-Server mit Content-Encoding: gzip oder Content-Encoding: deflate antwortet.
http-auth-challenge.name=Auth-Herausforderung
http-auth-challenge.description=Sendet HTTP-Authentifizierungs-Header nur, wenn dieser vom Server angefordert wird. Wenn false gesetzt ist, wird der Authentifizierungs-Header immer an den Server gesendet. Ausnahme: Wenn Benutzername und Passwort in der URI eingebettet sind, wird der Authentifizierungs-Header immer gesendet, unabhängig von dieser Option.
http-no-cache.name=Kein Cache
http-no-cache.description=Sendet Cache-Control: no-cache und Pragma: no-cache, um zwischengespeicherte Inhalte zu vermeiden. Bei false werden diese Header nicht gesendet und Sie können Cache-Control-Header mit einer gewünschten Direktive über die Option --header hinzufügen.
http-user.name=HTTP-Standard-Benutzername
http-user.description=Gibt den Benutzernamen für die Authentifizierung bei der Verbindung mit dem HTTP-Server an.
http-passwd.name=HTTP-Standard-Passwort
http-passwd.description=Gibt ein Passwort für die Authentifizierung bei der Verbindung mit einem HTTP-Server an.
http-proxy.name=HTTP-Proxy-Server
http-proxy.description=Installiert einen Proxyserver für HTTP-Verbindungen. Geben Sie die Adresse des Proxy-Servers an, über den HTTP-Anfragen laufen sollen.
http-proxy-user.name=HTTP-Proxy-Benutzername
http-proxy-user.description=Gibt den Benutzernamen für die Authentifizierung bei der Verbindung mit dem HTTP-Proxyserver an.
http-proxy-passwd.name=HTTP-Proxy-Passwort
http-proxy-passwd.description=Gibt das Passwort für die Authentifizierung bei der Verbindung mit dem HTTP-Proxyserver an.
https-proxy.name=HTTPS-Proxy-Server
https-proxy.description=Gibt einen Proxyserver für HTTPS-Verbindungen an. Geben Sie die Adresse des Proxy-Servers an, über den HTTPS-Anfragen laufen sollen.
https-proxy-user.name=HTTPS-Proxy-Benutzername
https-proxy-user.description=Gibt den Benutzernamen für die Authentifizierung bei der Verbindung mit einem HTTPS-Proxyserver an.
https-proxy-passwd.name=HTTPS-Proxy-Passwort
https-proxy-passwd.description=Gibt das Passwort für die Authentifizierung bei der Verbindung mit einem HTTPS-Proxyserver an.
referer.name=Referer
referer.description=HTTP-Referer (Referrer) festlegen. Dies betrifft alle HTTP/HTTPS-Downloads. Wenn * angegeben ist, wird die Download-URI auch als Referer verwendet. Dies kann nützlich sein in Kombination mit der Option --parameterized-uri.
enable-http-keep-alive.name=Persistente Verbindung aktivieren
enable-http-keep-alive.description=HTTP/1.1 persistente Verbindung aktivieren.
enable-http-pipelining.name=HTTP-Pipelining aktivieren
enable-http-pipelining.description=HTTP/1.1-Pipelining aktivieren.
header.name=Benutzerdefinierter Header
header.description=HEADER zum HTTP-Request-Header hinzufügen. Ein Eintrag pro Zeile, jeder Eintrag enthält "Headername: Headervalue".
save-cookies.name=Cookies-Pfad
save-cookies.description=Cookies in Datei im Mozilla/Firefox(1.x/2.x)/Netscape-Format speichern. Wenn die Datei bereits existiert, wird sie überschrieben. Sitzungs-Cookies werden ebenfalls gespeichert und deren Ablaufwerte als 0 behandelt.
use-head.name=HEAD-Methode verwenden
use-head.description=HEAD-Methode für die erste Anfrage an den HTTP-Server verwenden.
user-agent.name=Benutzerdefinierter User-Agent
user-agent.description=Gibt den User-Agent String an, den aria2 bei der Kommunikation mit Webservern verwendet.
ftp-user.name=FTP-Standard-Benutzername
ftp-user.description=Legt den Benutzernamen fest, der standardmäßig für die Authentifizierung bei Verbindungen zu FTP-Servern verwendet wird.
ftp-passwd.name=FTP-Standard-Passwort
ftp-passwd.description=Wenn Benutzername eingebettet ist, aber Passwort in der URI fehlt, versucht aria2, das Passwort über .netrc zu ermitteln. Wenn Passwort in .netrc gefunden wird, wird es verwendet. Andernfalls wird das in dieser Option angegebene Passwort verwendet.
ftp-pasv.name=Passiver Modus
ftp-pasv.description=Passiven Modus bei FTP verwenden. Bei false wird der aktive Modus verwendet. Diese Option wird bei SFTP-Übertragungen ignoriert.
ftp-proxy.name=FTP-Proxy-Server
ftp-proxy.description=Richtet einen Proxyserver für FTP-Verbindungen ein. Geben Sie die Adresse des Proxy-Servers an, über den FTP-Anfragen laufen sollen.
ftp-proxy-user.name=FTP-Proxy-Benutzername
ftp-proxy-user.description=Legt den Benutzernamen fest, der für die Authentifizierung bei der Verbindung mit dem FTP-Proxy verwendet wird.
ftp-proxy-passwd.name=FTP-Proxy-Passwort
ftp-proxy-passwd.description=Legt das Passwort fest, das für die Authentifizierung bei der Verbindung mit dem FTP-Proxy verwendet wird.
ftp-type.name=Übertragungsart
ftp-type.description=Legt die Übertragungsart für die FTP-Verbindung fest. Mögliche Werte: passiv oder aktiv.
ftp-reuse-connection.name=Verbindung wiederverwenden
ftp-reuse-connection.description=Ermöglicht die Wiederverwendung einer einzigen FTP-Verbindung für mehrere Downloads oder Uploads und erhöht so die Leistung.
ssh-host-key-md.name=SSH-Public-Key-Prüfsumme
ssh-host-key-md.description=Prüfsumme für SSH-Host-Public-Key festlegen. Das Format des Optionswerts ist TYP=DIGEST. TYP ist der Hash-Typ. Unterstützte Hash-Typen sind sha-1 oder md5. DIGEST ist der hexadezimale Digest. Beispiel: sha-1=b030503d4de4539dc7885e6f0f5e256704edf4c3. Diese Option kann zur Validierung des Server-Public-Keys bei SFTP verwendet werden. Wenn diese Option nicht gesetzt ist (Standard), findet keine Validierung statt.
bt-detach-seed-only.name=Nur Seed trennen
bt-detach-seed-only.description=Seed-Only-Downloads beim Zählen der gleichzeitigen aktiven Downloads ausschließen (siehe -j Option). Das bedeutet, wenn -j3 angegeben ist und diese Option aktiviert ist und 3 Downloads aktiv sind und einer davon in den Seed-Modus wechselt, wird er aus der aktiven Download-Zählung ausgeschlossen (wird also zu 2), und der nächste Download in der Warteschlange wird gestartet. Beachten Sie, dass das seeding-Element weiterhin als aktiver Download in der RPC-Methode erkannt wird.
bt-enable-hook-after-hash-check.name=Hook nach Hash-Prüfung aktivieren
bt-enable-hook-after-hash-check.description=Hook-Befehl nach Hash-Prüfung (siehe -V Option) im BitTorrent-Download zulassen. Standardmäßig wird bei erfolgreicher Hash-Prüfung der mit --on-bt-download-complete angegebene Befehl ausgeführt. Um diese Aktion zu deaktivieren, geben Sie false an.
bt-enable-lpd.name=Lokale Peer-Erkennung (LPD) aktivieren
bt-enable-lpd.description=Lokale Peer-Erkennung aktivieren. Wenn ein privates Flag in einem Torrent gesetzt ist, verwendet aria2 diese Funktion für diesen Download nicht, auch wenn true angegeben ist.
bt-exclude-tracker.name=BitTorrent-Tracker ausschließen
bt-exclude-tracker.description=Kommagetrennte Liste von BitTorrent-Tracker-Announce-URIs, die entfernt werden sollen. Sie können den speziellen Wert * verwenden, der alle URIs trifft und somit alle Announce-URIs entfernt. Wenn Sie * in der Shell-Befehlszeile angeben, denken Sie daran, es zu escapen oder zu quoten.
bt-external-ip.name=Externe IP
bt-external-ip.description=Externe IP-Adresse für BitTorrent-Download und DHT angeben. Sie kann an den BitTorrent-Tracker gesendet werden. Für DHT sollte diese Option gesetzt werden, um anzugeben, dass der lokale Node einen bestimmten Torrent herunterlädt. Dies ist wichtig für die Verwendung von DHT in einem privaten Netzwerk. Obwohl diese Funktion "extern" heißt, kann sie jede Art von IP-Adressen akzeptieren.
bt-force-encryption.name=Verschlüsselung erzwingen
bt-force-encryption.description=Erfordert Verschlüsselung der BitTorrent-Nachrichten mit arc4. Dies ist eine Kurzform für --bt-require-crypto --bt-min-crypto-level=arc4. Diese Option ändert die Werte dieser Optionen nicht. Bei true wird das Legacy-BitTorrent-Handshake abgelehnt und nur das Obfuscation-Handshake verwendet und Nachrichten immer verschlüsselt.
bt-hash-check-seed.name=Hash-Prüfung vor Seeding
bt-hash-check-seed.description=Wenn true angegeben ist, wird nach der Hash-Prüfung mit --check-integrity und vollständiger Datei weiterhin gesät. Wenn Sie die Datei prüfen und nur herunterladen möchten, wenn sie beschädigt oder unvollständig ist, setzen Sie diese Option auf false. Diese Option wirkt sich nur auf BitTorrent-Downloads aus.
bt-load-saved-metadata.name=Gespeicherte Metadaten-Datei laden
bt-load-saved-metadata.description=Bevor Torrent-Metadaten von DHT beim Download mit Magnet-Link abgerufen werden, wird zuerst versucht, die von --bt-save-metadata gespeicherte Datei zu lesen. Bei Erfolg wird das Herunterladen von Metadaten aus DHT übersprungen.
bt-max-open-files.name=Maximale offene Dateien
bt-max-open-files.description=Maximale Anzahl von Dateien, die global bei BitTorrent-/Metalink-Downloads mit mehreren Dateien geöffnet werden dürfen.
bt-max-peers.name=Maximale Peers
bt-max-peers.description=Maximale Anzahl von Peers pro Torrent angeben. 0 bedeutet unbegrenzt.
bt-metadata-only.name=Nur Metadaten herunterladen
bt-metadata-only.description=Nur Metadaten herunterladen. Die in den Metadaten beschriebenen Dateien werden nicht heruntergeladen. Diese Option wirkt sich nur aus, wenn BitTorrent-Magnet-URI verwendet wird.
bt-min-crypto-level.name=Minimales Verschlüsselungslevel
bt-min-crypto-level.description=Minimales Level der Verschlüsselungsmethode festlegen. Wenn mehrere Verschlüsselungsmethoden von einem Peer angeboten werden, wählt aria2 die niedrigste, die das angegebene Level erfüllt.
bt-prioritize-piece.name=Stück priorisieren
bt-prioritize-piece.description=Versucht, die ersten und letzten Stücke jeder Datei zuerst herunterzuladen. Dies ist nützlich für die Vorschau von Dateien. Das Argument kann 2 Schlüsselwörter enthalten: head und tail. Um beide einzuschließen, müssen sie durch Komma getrennt werden. Diese Schlüsselwörter können einen Parameter SIZE enthalten. Beispiel: Bei head=SIZE erhalten Stücke im Bereich der ersten SIZE Bytes jeder Datei höhere Priorität. tail=SIZE bedeutet den Bereich der letzten SIZE Bytes jeder Datei. SIZE kann K oder M enthalten (1K = 1024, 1M = 1024K).
bt-remove-unselected-file.name=Nicht ausgewählte Datei entfernen
bt-remove-unselected-file.description=Entfernt nicht ausgewählte Dateien nach Abschluss des Downloads bei BitTorrent. Um Dateien auszuwählen, verwenden Sie die Option --select-file. Wenn diese nicht verwendet wird, gelten alle Dateien als ausgewählt. Bitte verwenden Sie diese Option mit Vorsicht, da sie tatsächlich Dateien von Ihrer Festplatte entfernt.
bt-require-crypto.name=Verschlüsselung erforderlich
bt-require-crypto.description=Wenn true angegeben ist, akzeptiert aria2 keine Verbindung mit Legacy-BitTorrent-Handshake (\19BitTorrent protocol). aria2 verwendet immer das Obfuscation-Handshake.
bt-request-peer-speed-limit.name=Bevorzugte Download-Geschwindigkeit
bt-request-peer-speed-limit.description=Wenn die gesamte Download-Geschwindigkeit jedes Torrents niedriger als SPEED ist, erhöht aria2 vorübergehend die Anzahl der Peers, um mehr Download-Geschwindigkeit zu erzielen. Die Konfiguration mit Ihrer bevorzugten Download-Geschwindigkeit kann in manchen Fällen die Download-Geschwindigkeit erhöhen. Sie können K oder M anhängen (1K = 1024, 1M = 1024K).
bt-save-metadata.name=Metadaten speichern
bt-save-metadata.description=Metadaten als ".torrent"-Datei speichern. Diese Option wirkt sich nur aus, wenn BitTorrent-Magnet-URI verwendet wird. Der Dateiname ist der hexadezimale Info-Hash mit der Endung ".torrent". Das Verzeichnis ist das gleiche wie das Download-Verzeichnis. Wenn die Datei bereits existiert, werden die Metadaten nicht gespeichert.
bt-seed-unverified.name=Heruntergeladene Dateien nicht prüfen
bt-seed-unverified.description=Bereits heruntergeladene Dateien ohne Prüfung der Stück-Hashes seeden.
bt-stop-timeout.name=Stopp-Timeout
bt-stop-timeout.description=BitTorrent-Download stoppen, wenn die Download-Geschwindigkeit in aufeinanderfolgenden SEC Sekunden 0 ist. Bei 0 ist diese Funktion deaktiviert.
bt-tracker.name=BitTorrent-Tracker
bt-tracker.description=Kommagetrennte Liste zusätzlicher BitTorrent-Tracker-Announce-URIs. Diese URIs werden nicht von der Option --bt-exclude-tracker beeinflusst, da sie nach dem Entfernen der URIs aus --bt-exclude-tracker hinzugefügt werden.
bt-tracker-connect-timeout.name=BitTorrent-Tracker-Verbindungs-Timeout
bt-tracker-connect-timeout.description=Verbindungs-Timeout in Sekunden zum Aufbau der Verbindung zum Tracker festlegen. Nach dem Verbindungsaufbau hat diese Option keine Wirkung mehr und stattdessen wird die Option --bt-tracker-timeout verwendet.
bt-tracker-interval.name=BitTorrent-Tracker-Intervall
bt-tracker-interval.description=Intervall in Sekunden zwischen Tracker-Anfragen festlegen. Dies überschreibt den Intervallwert vollständig und aria2 verwendet nur diesen Wert und ignoriert den Mindestintervall und den Intervallwert in der Tracker-Antwort. Bei 0 bestimmt aria2 das Intervall basierend auf der Tracker-Antwort und dem Download-Fortschritt.
bt-tracker-timeout.name=BitTorrent-Tracker-Timeout
bt-tracker-timeout.description=Legt die Zeitüberschreitung für die Interaktion mit BitTorrent-Trackern fest. Gibt die Zeitspanne an, die auf eine Antwort des Trackers gewartet wird, bevor ein neuer Versuch unternommen wird.
dht-file-path.name=DHT (IPv4)-Datei
dht-file-path.description=IPv4-DHT-Routing-Tabellendatei auf PFAD ändern.
dht-file-path6.name=DHT (IPv6)-Datei
dht-file-path6.description=IPv6-DHT-Routing-Tabellendatei auf PFAD ändern.
dht-listen-port.name=DHT-Listen-Port
dht-listen-port.description=UDP-Listen-Port für DHT (IPv4, IPv6) und UDP-Tracker festlegen. Mehrere Ports können mit "," angegeben werden, z.B.: 6881,6885. Sie können auch "-" für einen Bereich verwenden: 6881-6999. , und - können zusammen verwendet werden.
dht-message-timeout.name=DHT-Nachrichten-Timeout
dht-message-timeout.description=Legt den Timeout für das Senden von Distributed Hash Table (DHT)-Nachrichten fest. Legt die Zeit fest, die auf eine Antwort von Peers im DHT-Netzwerk gewartet wird
enable-dht.name=DHT (IPv4) aktivieren
enable-dht.description=IPv4-DHT-Funktionalität aktivieren. Aktiviert auch UDP-Tracker-Unterstützung. Wenn ein privates Flag in einem Torrent gesetzt ist, verwendet aria2 DHT für diesen Download nicht, auch wenn true angegeben ist.
enable-dht6.name=DHT (IPv6) aktivieren
enable-dht6.description=IPv6-DHT-Funktionalität aktivieren. Wenn ein privates Flag in einem Torrent gesetzt ist, verwendet aria2 DHT für diesen Download nicht, auch wenn true angegeben ist. Verwenden Sie die Option --dht-listen-port, um die Portnummer festzulegen.
enable-peer-exchange.name=Peer-Austausch aktivieren
enable-peer-exchange.description=Peer-Exchange-Erweiterung aktivieren. Wenn ein privates Flag in einem Torrent gesetzt ist, ist diese Funktion für diesen Download deaktiviert, auch wenn true angegeben ist.
follow-torrent.name=Torrent folgen
follow-torrent.description=Wenn true oder mem angegeben ist, wird beim Download einer Datei mit der Endung .torrent oder Content-Type application/x-bittorrent diese als Torrent-Datei geparst und die darin genannten Dateien heruntergeladen. Bei mem wird die Torrent-Datei nicht auf die Festplatte geschrieben, sondern nur im Speicher gehalten. Bei false wird die .torrent-Datei auf die Festplatte heruntergeladen, aber nicht als Torrent geparst und deren Inhalte nicht heruntergeladen.
listen-port.name=Listen-Port
listen-port.description=TCP-Portnummer für BitTorrent-Downloads festlegen. Mehrere Ports können mit "," angegeben werden, z.B.: 6881,6885. Sie können auch "-" für einen Bereich verwenden: 6881-6999. , und - können zusammen verwendet werden: 6881-6889,6999.
max-overall-upload-limit.name=Globale maximale Upload-Geschwindigkeit
max-overall-upload-limit.description=Maximale gesamte Upload-Geschwindigkeit in Bytes/Sekunde festlegen. 0 bedeutet unbegrenzt. Sie können K oder M anhängen (1K = 1024, 1M = 1024K).
max-upload-limit.name=Maximale Upload-Geschwindigkeit
max-upload-limit.description=Maximale Upload-Geschwindigkeit pro Torrent in Bytes/Sekunde festlegen. 0 bedeutet unbegrenzt. Sie können K oder M anhängen (1K = 1024, 1M = 1024K).
peer-id-prefix.name=Peer-ID-Präfix
peer-id-prefix.description=Präfix der Peer-ID angeben. Die Peer-ID bei BitTorrent ist 20 Byte lang. Wenn mehr als 20 Bytes angegeben sind, werden nur die ersten 20 verwendet. Bei weniger als 20 Bytes werden zufällige Bytes hinzugefügt, um die Länge auf 20 zu bringen.
peer-agent.name=Peer-Agent
peer-agent.description=Zeichenkette für die BitTorrent-Extended-Handshake-Version des Peer-Clients angeben.
seed-ratio.name=Minimale Share-Ratio
seed-ratio.description=Share-Ratio angeben. Fertige Torrents seeden, bis die Share-Ratio RATIO erreicht ist. Es wird empfohlen, hier mindestens 1.0 anzugeben. Geben Sie 0.0 an, wenn Sie unabhängig von der Share-Ratio seeden möchten. Wenn --seed-time zusammen mit dieser Option angegeben ist, endet das Seeding, wenn mindestens eine der Bedingungen erfüllt ist.
seed-time.name=Minimale Seed-Zeit
seed-time.description=Seed-Zeit in (Bruch-)Minuten angeben. Mit --seed-time=0 wird das Seeding nach Abschluss des Downloads deaktiviert.
follow-metalink.name=Metalink folgen
follow-metalink.description=Wenn true oder mem angegeben ist, wird beim Download einer Datei mit der Endung .meta4 oder .metalink oder Content-Type application/metalink4+xml oder application/metalink+xml diese als Metalink-Datei geparst und die darin genannten Dateien heruntergeladen. Bei mem wird die Metalink-Datei nicht auf die Festplatte geschrieben, sondern nur im Speicher gehalten. Bei false wird die .metalink-Datei auf die Festplatte heruntergeladen, aber nicht als Metalink-Datei geparst und deren Inhalte nicht heruntergeladen.
metalink-base-uri.name=Basis-URI
metalink-base-uri.description=Basis-URI angeben, um relative URIs in metalink:url und metalink:metaurl-Elementen in einer auf der lokalen Festplatte gespeicherten Metalink-Datei aufzulösen. Wenn die URI auf ein Verzeichnis zeigt, muss sie mit / enden.
metalink-language.name=Sprache
metalink-language.description=Legt die Sprache fest, die für Metalink-Metadaten verwendet werden soll. Die Sprache wird als Sprachcode angegeben, z. B. „en“ für Englisch.
metalink-location.name=Bevorzugter Serverstandort
metalink-location.description=Der Standort des bevorzugten Servers. Eine kommagetrennte Liste von Standorten ist zulässig, z.B. jp,us.
metalink-os.name=Betriebssystem
metalink-os.description=Das Betriebssystem der herunterzuladenden Datei.
metalink-version.name=Version
metalink-version.description=Die Version der herunterzuladenden Datei.
metalink-preferred-protocol.name=Bevorzugtes Protokoll
metalink-preferred-protocol.description=Bevorzugtes Protokoll angeben. Mögliche Werte sind http, https, ftp und none. Mit none wird diese Funktion deaktiviert.
metalink-enable-unique-protocol.name=Einzigartiges Protokoll aktivieren
metalink-enable-unique-protocol.description=Wenn true angegeben ist und mehrere Protokolle für einen Mirror in einer Metalink-Datei verfügbar sind, verwendet aria2 eines davon. Verwenden Sie die Option --metalink-preferred-protocol, um die Präferenz des Protokolls festzulegen.
enable-rpc.name=JSON-RPC/XML-RPC-Server aktivieren
enable-rpc.description=Es bietet eine Remote-Procedure-Call (RPC)-Schnittstelle, die die Steuerung von aria2 durch externe Anwendungen ermöglicht.
pause-metadata.name=Nach Metadaten-Download pausieren
pause-metadata.description=Downloads pausieren, die durch Metadaten-Download erstellt wurden. Es gibt 3 Arten von Metadaten-Downloads in aria2: (1) Download einer .torrent-Datei. (2) Download von Torrent-Metadaten mit Magnet-Link. (3) Download einer Metalink-Datei. Diese Metadaten-Downloads erzeugen Downloads mit ihren Metadaten. Diese Option pausiert diese nachfolgenden Downloads. Diese Option ist nur wirksam, wenn --enable-rpc=true angegeben ist.
rpc-allow-origin-all.name=Alle Origin-Anfragen erlauben
rpc-allow-origin-all.description=Fügt dem RPC-Response das Header-Feld Access-Control-Allow-Origin mit Wert * hinzu.
rpc-listen-all.name=Auf allen Netzwerkschnittstellen lauschen
rpc-listen-all.description=Eingehende JSON-RPC/XML-RPC-Anfragen auf allen Netzwerkschnittstellen lauschen. Bei false wird nur auf der lokalen Loopback-Schnittstelle gelauscht.
rpc-listen-port.name=Listen-Port
rpc-listen-port.description=Legt den Port fest, an dem aria2 auf RPC-Anfragen wartet. Standardmäßig wird der Port 6800 verwendet.
rpc-max-request-size.name=Maximale Anfragegröße
rpc-max-request-size.description=Maximale Größe der JSON-RPC/XML-RPC-Anfrage festlegen. Wenn aria2 erkennt, dass die Anfrage größer als SIZE Bytes ist, wird die Verbindung getrennt.
rpc-save-upload-metadata.name=Upload-Metadaten speichern
rpc-save-upload-metadata.description=Die hochgeladenen Torrent- oder Metalink-Metadaten im Verzeichnis speichern, das mit der Option --dir angegeben wurde. Der Dateiname besteht aus dem SHA-1-Hash-String der Metadaten plus Erweiterung. Für Torrent ist die Erweiterung '.torrent'. Für Metalink ist sie '.meta4'. Bei false werden die Downloads, die durch aria2.addTorrent() oder aria2.addMetalink() hinzugefügt wurden, nicht durch die Option --save-session gespeichert.
rpc-secure.name=SSL/TLS aktivieren
rpc-secure.description=RPC-Transport wird durch SSL/TLS verschlüsselt. Die RPC-Clients müssen das https-Schema verwenden, um auf den Server zuzugreifen. Für WebSocket-Clients verwenden Sie das wss-Schema. Verwenden Sie die Optionen --rpc-certificate und --rpc-private-key, um das Serverzertifikat und den privaten Schlüssel anzugeben.
allow-overwrite.name=Überschreiben erlauben
allow-overwrite.description=Download von Grund auf neu starten, wenn die zugehörige Steuerdatei nicht existiert. Siehe auch Option --auto-file-renaming.
allow-piece-length-change.name=Stücklängenänderung erlauben
allow-piece-length-change.description=Bei false bricht aria2 den Download ab, wenn die Stücklänge von der in der Steuerdatei abweicht. Bei true können Sie fortfahren, aber ein Teil des Download-Fortschritts geht verloren.
always-resume.name=Download immer fortsetzen
always-resume.description=Download immer fortsetzen. Bei true versucht aria2 immer, den Download fortzusetzen, und bricht ab, wenn dies nicht möglich ist. Bei false, wenn alle angegebenen URIs Resume nicht unterstützen oder aria2 N URIs findet, die Resume nicht unterstützen (N ist der Wert von --max-resume-failure-tries), lädt aria2 die Datei von Grund auf neu. Siehe Option --max-resume-failure-tries.
async-dns.name=Asynchrones DNS
async-dns.description=Aktiviert die asynchrone DNS-Auflösung, die die Leistung der Hostnamenauflösung verbessern kann.
auto-file-renaming.name=Automatische Dateiumbenennung
auto-file-renaming.description=Dateiname umbenennen, wenn die gleiche Datei bereits existiert. Diese Option funktioniert nur bei HTTP(S)/FTP-Downloads. Der neue Dateiname erhält einen Punkt und eine Zahl (1..9999) nach dem Namen, aber vor der Dateierweiterung, falls vorhanden.
auto-save-interval.name=Automatisches Speicherintervall
auto-save-interval.description=Steuerdatei (*.aria2) alle SEC Sekunden speichern. Bei 0 wird während des Downloads keine Steuerdatei gespeichert. aria2 speichert eine Steuerdatei beim Stoppen unabhängig vom Wert. Mögliche Werte: 0 bis 600.
conditional-get.name=Bedingter Download
conditional-get.description=Datei nur herunterladen, wenn die lokale Datei älter als die Remote-Datei ist. Diese Funktion funktioniert nur bei HTTP(S)-Downloads. Sie funktioniert nicht, wenn die Dateigröße in Metalink angegeben ist. Sie ignoriert auch den Content-Disposition-Header. Wenn eine Steuerdatei existiert, wird diese Option ignoriert. Diese Funktion verwendet den If-Modified-Since-Header, um nur neuere Dateien bedingt zu erhalten. Beim Abrufen des Änderungsdatums der lokalen Datei wird der vom Benutzer angegebene Dateiname (siehe Option --out) oder der Dateiname in der URI verwendet, wenn --out nicht angegeben ist. Zum Überschreiben der bestehenden Datei ist --allow-overwrite erforderlich.
conf-path.name=Konfigurationsdatei
conf-path.description=Gibt den Pfad zur Konfigurationsdatei an, die beim Start von aria2 verwendet werden soll.
console-log-level.name=Konsolen-Protokollierungsstufe
console-log-level.description=Legt den Detaillierungsgrad für die Konsolenprotokollausgabe fest. Verfügbare Stufen: debug, information, alert, warning und error.
content-disposition-default-utf8.name=UTF-8 für Content-Disposition verwenden
content-disposition-default-utf8.description=Quoted String im Content-Disposition-Header als UTF-8 statt ISO-8859-1 behandeln, z.B. den filename-Parameter, aber nicht die erweiterte Version filename.
daemon.name=Daemon aktivieren
daemon.description=Lässt aria2 im Hintergrund als Daemon laufen und gibt das Terminal für andere Aufgaben frei.
deferred-input.name=Verzögertes Laden
deferred-input.description=Bei true liest aria2 nicht alle URIs und Optionen aus der Datei, die mit --input-file angegeben wurde, beim Start, sondern liest sie einzeln, wenn sie später benötigt werden. Dies kann den Speicherverbrauch reduzieren, wenn die Eingabedatei viele URIs enthält. Bei false liest aria2 alle URIs und Optionen beim Start. Die Option --deferred-input wird deaktiviert, wenn --save-session zusammen verwendet wird.
disable-ipv6.name=IPv6 deaktivieren
disable-ipv6.description=Deaktiviert IPv6 für alle Netzwerkverbindungen.
disk-cache.name=Festplatten-Cache
disk-cache.description=Festplatten-Cache aktivieren. Bei SIZE=0 ist der Festplatten-Cache deaktiviert. Diese Funktion speichert die heruntergeladenen Daten im Speicher, der maximal SIZE Bytes groß wird. Der Cache-Speicher wird für die aria2-Instanz erstellt und von allen Downloads gemeinsam genutzt. Vorteil: Weniger Festplatten-I/O, da die Daten in größeren Einheiten geschrieben und nach Offset sortiert werden. Wenn eine Hash-Prüfung erforderlich ist und die Daten im Speicher sind, müssen sie nicht von der Festplatte gelesen werden. SIZE kann K oder M enthalten (1K = 1024, 1M = 1024K).
download-result.name=Download-Ergebnis
download-result.description=Diese Option ändert die Formatierung der Download-Ergebnisse. Bei OPT=default werden GID, Status, durchschnittliche Download-Geschwindigkeit und Pfad/URI ausgegeben. Bei mehreren Dateien wird Pfad/URI der ersten angeforderten Datei ausgegeben, die übrigen werden weggelassen. Bei OPT=full werden GID, Status, durchschnittliche Download-Geschwindigkeit, Fortschrittsprozentsatz und Pfad/URI ausgegeben. Fortschrittsprozentsatz und Pfad/URI werden für jede angeforderte Datei in jeder Zeile ausgegeben. Bei OPT=hide werden Download-Ergebnisse ausgeblendet.
dscp.name=DSCP
dscp.description=DSCP-Wert in ausgehenden IP-Paketen des BitTorrent-Verkehrs für QoS festlegen. Dieser Parameter setzt nur die DSCP-Bits im TOS-Feld der IP-Pakete, nicht das gesamte Feld. Wenn Sie Werte aus /usr/include/netinet/ip.h nehmen, teilen Sie sie durch 4 (sonst wären die Werte falsch, z.B. würde Ihre CS1-Klasse zu CS4 werden). Wenn Sie gebräuchliche Werte aus RFC, Netzwerkhersteller-Dokumentation, Wikipedia oder einer anderen Quelle nehmen, verwenden Sie sie wie sie sind.
rlimit-nofile.name=Soft-Limit für offene Dateideskriptoren
rlimit-nofile.description=Soft-Limit für offene Dateideskriptoren festlegen. Diese Option wirkt nur, wenn: a. Das System es unterstützt (posix). b. Das Limit das Hard-Limit nicht überschreitet. c. Das angegebene Limit größer als das aktuelle Soft-Limit ist. Dies entspricht dem Setzen von nofile über ulimit, außer dass das Limit nie verringert wird. Diese Option ist nur auf Systemen mit rlimit-API verfügbar.
enable-color.name=Farben im Terminal aktivieren
enable-color.description=Aktiviert oder deaktiviert die Verwendung eines Farbstils in der Konsolenausgabe.
enable-mmap.name=MMap aktivieren
enable-mmap.description=Dateien in den Speicher abbilden. Diese Option funktioniert möglicherweise nicht, wenn der Dateispeicher nicht vorab zugewiesen ist. Siehe --file-allocation.
event-poll.name=Ereignis-Polling-Methode
event-poll.description=Methode für das Polling von Ereignissen festlegen. Mögliche Werte sind epoll, kqueue, port, poll und select. Für epoll, kqueue, port und poll gilt: verfügbar, wenn das System sie unterstützt. epoll ist auf aktuellen Linux-Systemen verfügbar. kqueue auf verschiedenen *BSD-Systemen einschließlich Mac OS X. port auf Open Solaris. Der Standardwert kann je nach verwendetem System variieren.
file-allocation.name=Dateizuweisungsmethode
file-allocation.description=Dateizuweisungsmethode festlegen. none weist keinen Speicherplatz vorab zu. prealloc weist Speicherplatz vor dem Download zu. Dies kann je nach Dateigröße einige Zeit dauern. Bei neueren Dateisystemen wie ext4 (mit Extents-Unterstützung), btrfs, xfs oder NTFS (nur MinGW-Build) ist falloc die beste Wahl. Es weist große (mehrere GiB) Dateien fast sofort zu. Verwenden Sie falloc nicht mit alten Dateisystemen wie ext3 und FAT32, da es fast genauso lange dauert wie prealloc und aria2 während der Zuweisung komplett blockiert. falloc ist möglicherweise nicht verfügbar, wenn Ihr System keine posix_fallocate(3)-Funktion hat. trunc verwendet den Systemaufruf ftruncate(2) oder plattformspezifisches Pendant, um eine Datei auf eine bestimmte Länge zu kürzen. Bei Multi-Datei-Torrent-Downloads werden die angrenzenden Dateien ebenfalls zugewiesen, wenn sie sich ein Stück teilen.
force-save.name=Speichern erzwingen
force-save.description=Download mit der Option --save-session speichern, auch wenn der Download abgeschlossen oder entfernt wurde. Diese Option speichert auch die Steuerdatei in diesen Situationen. Dies kann nützlich sein, um BitTorrent-Seeding zu speichern, das als abgeschlossen erkannt wird.
save-not-found.name=Nicht gefundene Datei speichern
save-not-found.description=Download mit der Option --save-session speichern, auch wenn die Datei auf dem Server nicht gefunden wurde. Diese Option speichert auch die Steuerdatei in diesen Situationen.
hash-check-only.name=Nur Hash-Prüfung
hash-check-only.description=Bei true wird nach der Hash-Prüfung mit --check-integrity der Download abgebrochen, unabhängig davon, ob der Download abgeschlossen ist oder nicht.
human-readable.name=Konsolenausgabe menschenlesbar
human-readable.description=Größen und Geschwindigkeit in menschenlesbarem Format (z.B. 1,2Ki, 3,4Mi) in der Konsolenausgabe ausgeben.
keep-unfinished-download-result.name=Unvollständige Download-Ergebnisse behalten
keep-unfinished-download-result.description=Unvollständige Download-Ergebnisse behalten, auch wenn dadurch --max-download-result überschritten wird. Dies ist nützlich, wenn alle unvollständigen Downloads in der Sitzungsdatei gespeichert werden müssen (siehe Option --save-session). Beachten Sie, dass es keine Obergrenze für die Anzahl der zu behaltenden unvollständigen Download-Ergebnisse gibt. Wenn das unerwünscht ist, deaktivieren Sie diese Option.
max-download-result.name=Maximale Download-Ergebnisse
max-download-result.description=Maximale Anzahl von Download-Ergebnissen, die im Speicher gehalten werden. Die Download-Ergebnisse sind abgeschlossene/fehlerhafte/entfernte Downloads. Die Ergebnisse werden in einer FIFO-Warteschlange gespeichert und es können maximal NUM Ergebnisse gespeichert werden. Wenn die Warteschlange voll ist und ein neues Ergebnis erstellt wird, wird das älteste Ergebnis entfernt und das neue hinten angehängt. Ein hoher Wert kann zu hohem Speicherverbrauch nach Tausenden von Downloads führen. Bei 0 wird kein Download-Ergebnis gespeichert. Unvollständige Downloads werden unabhängig vom Wert dieser Option im Speicher gehalten. Siehe Option --keep-unfinished-download-result.
max-mmap-limit.name=Maximale MMap-Grenze
max-mmap-limit.description=Maximale Dateigröße für die Aktivierung von mmap (siehe Option --enable-mmap) festlegen. Die Dateigröße ergibt sich aus der Summe aller Dateien eines Downloads. Beispiel: Ein Download enthält 5 Dateien, dann ist die Dateigröße die Gesamtsumme dieser Dateien. Ist die Dateigröße strikt größer als der angegebene Wert, wird mmap deaktiviert.
max-resume-failure-tries.name=Maximale Resume-Fehlversuche
max-resume-failure-tries.description=Bei Verwendung mit --always-resume=false lädt aria2 die Datei von Grund auf neu, wenn N URIs gefunden werden, die Resume nicht unterstützen. Bei N=0 wird die Datei von Grund auf neu geladen, wenn alle angegebenen URIs Resume nicht unterstützen. Siehe Option --always-resume.
min-tls-version.name=Minimale TLS-Version
min-tls-version.description=Minimale SSL/TLS-Version zum Aktivieren angeben.
log-level.name=Protokollierungsstufe
log-level.description=Gibt die Protokollierungsstufe für die Anwendung an.
optimize-concurrent-downloads.name=Gleichzeitige Downloads optimieren
optimize-concurrent-downloads.description=Optimiert die Anzahl der gleichzeitigen Downloads entsprechend der verfügbaren Bandbreite. aria2 verwendet die Download-Geschwindigkeit aus vorherigen Downloads, um die Anzahl der parallel gestarteten Downloads nach der Regel N = A + B Log10(Geschwindigkeit in Mbps) anzupassen. Die Koeffizienten A und B können im Optionsargument mit Doppelpunkt getrennt angepasst werden. Die Standardwerte (A=5, B=25) führen zu typischerweise 5 parallelen Downloads bei 1Mbps-Netzwerken und über 50 bei 100Mbps-Netzwerken. Die Anzahl der parallelen Downloads bleibt unter dem Maximum, das durch die Option --max-concurrent-downloads definiert ist.
piece-length.name=Stücklänge
piece-length.description=Stücklänge für HTTP/FTP-Downloads festlegen. Dies ist die Grenze, an der aria2 eine Datei teilt. Alle Splits erfolgen bei Vielfachen dieser Länge. Diese Option wird bei BitTorrent-Downloads ignoriert. Sie wird auch ignoriert, wenn die Metalink-Datei Stück-Hashes enthält.
show-console-readout.name=Konsolenausgabe anzeigen
show-console-readout.description=Gibt an, ob die Ausgabe in der Konsole angezeigt werden soll.
summary-interval.name=Intervall für Download-Zusammenfassung
summary-interval.description=Intervall in Sekunden für die Ausgabe der Download-Fortschrittszusammenfassung festlegen. Bei 0 wird die Ausgabe unterdrückt.
max-overall-download-limit.name=Globale maximale Download-Geschwindigkeit
max-overall-download-limit.description=Maximale gesamte Download-Geschwindigkeit in Bytes/Sekunde festlegen. 0 bedeutet unbegrenzt. Sie können K oder M anhängen (1K = 1024, 1M = 1024K).
max-download-limit.name=Maximale Download-Geschwindigkeit
max-download-limit.description=Maximale Download-Geschwindigkeit pro Download in Bytes/Sekunde festlegen. 0 bedeutet unbegrenzt. Sie können K oder M anhängen (1K = 1024, 1M = 1024K).
no-conf.name=Konfigurationsdatei deaktivieren
no-conf.description=Deaktiviert das Laden der Konfigurationsdatei.
no-file-allocation-limit.name=Keine Dateizuweisungsgrenze
no-file-allocation-limit.description=Für Dateien, deren Größe kleiner als SIZE ist, wird keine Dateizuweisung vorgenommen. Sie können K oder M anhängen (1K = 1024, 1M = 1024K).
parameterized-uri.name=Parametrisierte URI aktivieren
parameterized-uri.description=Parametrisierte URI-Unterstützung aktivieren. Sie können Teile angeben: http://{sv1,sv2,sv3}/foo.iso. Sie können auch numerische Sequenzen mit Schrittzähler angeben: http://host/image[000-100:2].img. Ein Schrittzähler kann weggelassen werden. Wenn alle URIs nicht auf die gleiche Datei zeigen, wie im zweiten Beispiel oben, ist die Option -Z erforderlich.
quiet.name=Konsolenausgabe deaktivieren
quiet.description=Deaktiviert alle Ausgaben auf der Konsole.
realtime-chunk-checksum.name=Echtzeit-Datenchunk-Prüfung
realtime-chunk-checksum.description=Chunk von Daten durch Berechnung der Prüfsumme während des Downloads validieren, wenn Chunk-Prüfsummen bereitgestellt werden.
remove-control-file.name=Steuerdatei entfernen
remove-control-file.description=Steuerdatei vor dem Download entfernen. In Kombination mit --allow-overwrite=true startet der Download immer von Grund auf neu. Dies ist nützlich für Nutzer hinter Proxy-Servern, die Resume deaktivieren.
save-session.name=Sitzungsdatei speichern
save-session.description=Fehlerhafte/unvollständige Downloads beim Beenden in Datei speichern. Sie können diese Ausgabedatei mit der Option --input-file an aria2c beim Neustart übergeben. Wenn Sie die Ausgabe gzippen möchten, hängen Sie eine .gz-Endung an den Dateinamen an. Beachten Sie, dass Downloads, die durch die RPC-Methoden aria2.addTorrent() und aria2.addMetalink() hinzugefügt wurden und deren Metadaten nicht als Datei gespeichert werden konnten, nicht gespeichert werden. Downloads, die mit aria2.remove() und aria2.forceRemove() entfernt wurden, werden nicht gespeichert.
save-session-interval.name=Sitzungsintervall speichern
save-session-interval.description=Fehlerhafte/unvollständige Downloads alle SEC Sekunden in die mit --save-session angegebene Datei speichern. Bei 0 wird die Datei nur beim Beenden von aria2 gespeichert.
socket-recv-buffer-size.name=Socket-Empfangspuffergröße
socket-recv-buffer-size.description=Maximale Socket-Empfangspuffergröße in Bytes festlegen. Bei 0 wird diese Option deaktiviert. Dieser Wert wird mit setsockopt() als SO_RCVBUF auf den Socket-Dateideskriptor gesetzt.
stop.name=Automatische Abschaltzeit
stop.description=Anwendung stoppen, nachdem SEC Sekunden vergangen sind. Bei 0 ist diese Funktion deaktiviert.
truncate-console-readout.name=Konsolenausgabe kürzen
truncate-console-readout.description=Konsolenausgabe kürzen, damit sie in eine Zeile passt.
