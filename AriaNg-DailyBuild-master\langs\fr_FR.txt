[global]
AriaNg Version=Version de AriaNg
Operation Result=Résultat de l'opération
Operation Succeeded=Opération réussie
is connected=Connecté
Error=Erreur
OK=OK
Confirm=Confirmer
Cancel=Annuler
Close=Fermer
True=Vrai
False=Faux
DEBUG=DÉBOGUER
INFO=INFORMATIONS
WARN=AVERTISSEMENT
ERROR=ERREUR
Connecting=Connexion en cours
Connected=Connecté
Disconnected=Déconnecté
Reconnecting=Reconnexion en cours
Waiting to reconnect=En attente de reconnexion
Global=Général
New=Nouveau
Start=Démarrer
Pause=Pause
Retry=Réessayer
Retry Selected Tasks=Relancer les tâches sélectionnées
Delete=Supprimer
Select All=Tout sélectionner
Select None=Tout désléctionner
Select Invert=Inverser la sélection
Select All Failed Tasks=Sélectionner toutes les tâches échouées
Select All Completed Tasks=Sélectionner toutes les tâches réuissies
Select All Tasks=Sélectionner toutes les tâches
Display Order=Ordre d'affichage
Copy Download Url=Copier L'URL de téléchargement
Copy Magnet Link=Copier le lien du magnet
Help=Aide
Search=Rechercher
Default=Défaut
Expand=Développer
Collapse=Réduire
Expand All=Tout développer
Collapse All=Tout réduire
Open=Ouvrir
Save=Sauvegarder
Import=Importer
Remove Task=Supprimer la tâche
Remove Selected Task=Supprimer les tâche sélectionnées
Clear Stopped Tasks=Supprimer les tâches stoppées
Click to view task detail=Cliquer pour voir le détail de la tâche
By File Name=Par nom de fichier
By File Size=Par taille de fichier
By Progress=Par progression
By Selected Status=Par statut sélectionné
By Remaining=Par temps restant
By Download Speed=Par vitesse de téléchargement
By Upload Speed=Par vitesse de téléversement
By Peer Address=Par adresse de peer
By Client Name=Par nom de client
Filters=Filtres
Download=Téléchargements
Upload=Téléversement
Downloading=Téléchargements en cours
Pending Verification=Vérification en attente
Verifying=Vérification en cours
Seeding=Seed en cours
Waiting=En attente
Paused=En pause
Completed=Terminé
Error Occurred=Erreur rencontrée
Removed=Supprimé
Finished / Stopped=Terminés / Stoppés
Uncompleted=Incomplet
Click to pin=Cliquer pour épingler
Settings=Paramètres
AriaNg Settings=Paramètres AriaNg
Aria2 Settings=Paramètres Aria2
Basic Settings=Paramètres basiques
HTTP/FTP/SFTP Settings=Paramètres HTTP/FTP/SFTP
HTTP Settings=Paramètres HTTP
FTP/SFTP Settings=Paramètres FTP/SFTP
BitTorrent Settings=Paramètres BitTorrent
Metalink Settings=Paramètres Metalink
RPC Settings=Paramètres RPC
Advanced Settings=Paramètres avancés
AriaNg Debug Console=Console de débogage AriaNg
Aria2 Status=Statut Aria2
File Name=Nom de fichier
File Size=Taille de fichier
Progress=Progression
Share Ratio=Ratio de partage
Remaining=Temps restant
Download Speed=Vitesse de téléchargement
Upload Speed=Vitesse de téléversement
Links=Liens
Torrent File=Fichier torrent
Metalink File=Fichier Metalink
File Name:=Nom de fichier:
Options=Options
Overview=Aperçu
Pieces=Pièces
Files=Fichiers
Peers=Peers
Task Name=Nom de la tâche
Task Size=Taille de la tâche
Task Status=Statut de la tâche
Error Description=Erreur de description
Health Percentage=Pourcentage de santé
Info Hash=Informations hash
Seeders=Seeders
Connections=Connexions
Seed Creation Time=Temps depuis création du seed
Download Url=URL de téléchargement
Download Dir=Destination du téléchargement
BT Tracker Servers=Serveur de suivi BT
Copy=Copier
(Choose Files)=(Choisir fichiers)
Videos=Vidéos
Audios=Audios
Pictures=Images
Documents=Documents
Applications=Applications
Archives=Archives
Other=Autres
Custom=Personnalisé
Custom Choose File=Choix de fichier personnalisé
Address=Adresse
Client=Client
Status=Statut
Speed=Vitesse
(local)=(Local)
No Data=Aucune donnée
No connected peers=Aucun peer connecté
Failed to change some tasks state.=Echec lors du changement d'état des tâches.
Confirm Retry=Confirmer nouvel essai
Are you sure you want to retry the selected task? AriaNg will create same task after clicking OK.=Ëtes-vous sûr de vouloir la tâche sélectionnée? AriaNg va créer la même tâche après confirmation.
Failed to retry this task.=Échec de la nouvelle tentative pour cette tâche.
{successCount} tasks have been retried and {failedCount} tasks are failed.={{successCount}} tâches ont été relancées et {{failedCount}} tâches ont échouées.
Confirm Remove=Confirmer suppression
Are you sure you want to remove the selected task?=Ëtes-vous sûr de vouloir supprimer la tâche sélectionnée?
Failed to remove some task(s).=Echec lors de la suppression de certaines tâches.
Confirm Clear=Confirmer nettoyage
Are you sure you want to clear stopped tasks?=Ëtes-vous sûr de vouloir effacer les tâches stoppées?
Download Links:=Liens de téléchargement
Download Now=Télécharger maintenant
Download Later=Télécharger plus tard
Open Torrent File=Ouvrir fichier torrent
Open Metalink File=Ouvrir fichier Metalink
Support multiple URLs, one URL per line.=Supporte plusieurs URLs, une URL par ligne.
Your browser does not support loading file!=Votre navigateur ne supporte pas le chargement de fichier!
The selected file type is invalid!=Le type de fichier sélectionné est invalide!
Failed to load file!=Echec lors du chargement du fichier!
Download Completed=Téléchargement terminé
BT Download Completed=Téléchargement BT terminé
Download Error=Erreur de téléchargement
AriaNg Url=URL de AriaNg
Command API Url=URL de la commande API
Export Command API=Exporter la commande API
Export=Exporter
Copied=Copié
Pause After Task Created=Pause après création de la tâche
Language=Langage
Theme=Thème
Light=Clair
Dark=Sombre
Follow system settings=Similaire aux réglages système
Debug Mode=Mode de débogage
Page Title=Titre de la page
Preview=Prévisualisation
Tips: You can use the "noprefix" tag to ignore the prefix, "nosuffix" tag to ignore the suffix, and "scale\=n" tag to set the decimal precision.=Astuce : Vous pouvez utiliser le tag "noprefix" pour ignorer le préfixe, le tag "nosuffix" pour ignorer le suffixe, et le tag "scale\n" pour régler à la décimale près.
Example: ${downspeed:noprefix:nosuffix:scale\=1}=Exemple: ${downspeed:noprefix:nosuffix:scale\=1}
Updating Page Title Interval=Actualisation de l'intervalle du titre de la page
Enable Browser Notification=Activer les notification dans le navigateur
Browser Notification Sound=Son de notification du navigateur
Browser Notification Frequency=Fréquence de notification du navigateur
Unlimited=Illimité
High (Up to 10 Notifications / 1 Minute)=Haut (Jusqu'à 10 notifications / 1 minute)
Middle (Up to 1 Notification / 1 Minute)=Moyen (Jusqu'a 1 notification / 1 minute)
Low (Up to 1 Notification / 5 Minutes)= Petit (Jusqu'à 1 notification / 5 minutes)
WebSocket Auto Reconnect Interval=Intervalle de reconnexion automatique au WebSocket
Aria2 RPC Alias=Alias Aria2 RPC
Aria2 RPC Address=Adresse Aria2 RPC
Aria2 RPC Protocol=Protocole Aria2 RPC
Aria2 RPC Http Request Method=Méthode de requête Http Aria2 RPC
POST method only supports aria2 v1.15.2 and above.=La méthode POST supporte seulement Aria2 v1.15.2 et supérieur.
Aria2 RPC Request Headers=En-têtes requête RPC Aria2
Support multiple request headers, one header per line, each line containing "header name: header value".=Supporte plusieurs en-têtes de requête, une en-tête par ligne, chaque ligne doit contenir "header name: header value".
Aria2 RPC Secret Token=Jeton secret RPC Aria2
Activate=Activer
Reset Settings=Réinitialiser paramètres
Confirm Reset=Confirmer la réinitialisation
Are you sure you want to reset all settings?=Êtes-vous sûr de vouloir réinitilaiser tous les paramètres?
Clear Settings History=Supprimer l'historique des réglages
Are you sure you want to clear all settings history?=Êtes-vous sûr de vouloir supprimer tout l'historique des réglages?
Delete RPC Setting=Supprimer le paramétrage RPC
Add New RPC Setting=Ajouter un nouveau paramétrage RPC
Are you sure you want to remove rpc setting "{rpcName}"?=Êtes-vous sûr de vouloir supprimer le paramétrage RPC "{{rpcName}}"?
Updating Global Stat Interval=Actualisation de l'intervalle des statistiques globales
Updating Task Information Interval=Actualisation de l'intervalle de la tâche d'information
Keyboard Shortcuts=Raccourcis clavier
Supported Keyboard Shortcuts=Raccourcis clavier supportés
Set Focus On Search Box=Mettre focus sur la la barre de recherche
Swipe Gesture=Geste de balayage
Change Tasks Order by Drag-and-drop=Changer l'ordre des tâches par glisser-déposer
Action After Creating New Tasks=Action après la création de nouvelles tâches
Navigate to Task List Page=Naviguer à la page liste des tâches
Navigate to Task Detail Page=Naviguer à la page de détails de la tâche
Action After Retrying Task=Action après relance d'une tâche
Navigate to Downloading Tasks Page=Naviguer à la page tâches de téléchargement
Stay on Current Page=Rester sur la page actuelle
Remove Old Tasks After Retrying=Supprimer les anciennes tâches après nouvel essai
Confirm Task Removal= Confirmer la suppresion de la tâche
Include Prefix When Copying From Task Details=Inclure préfixe lorsque les détails d'une tâche sont copiés
Show Pieces Info In Task Detail Page=Montrer les infos des pièces dans la page de détails de la tâche
Pieces Amount is Less than or Equal to {value}= Le nombre de pièces est inférieur à ou égal à {{value}}
RPC List Display Order=Ordre d'affichage de la liste RPC
Each Task List Page Uses Independent Display Order=Chaque page de liste de tâches utilisent un ordre d'affichage indépendant
Recently Used=Utilisé récemment
RPC Alias=Alias RPC
Import / Export AriaNg Settings=Importer / Exporter paramètres AriaNg
Import Settings=Importer les paramètres
Export Settings=Exporter les paramètres
AriaNg settings data=Paramètres données AriaNg
Confirm Import=Confirmer l'importation
Are you sure you want to import all settings?=Êtes-vous sûr de vouloir importer tous les paramètres?
Invalid settings data format!=Mauvais paramètrage format de données!
Data has been copied to clipboard.=Les données ont été copiées dans le presse-papiers.
Supported Placeholder=Balise supportée
AriaNg Title=Titre AriaNg
Current RPC Alias=Alias RPC actuel
Downloading Count=Nombre de téléchargements
Waiting Count=Nombre en attente
Stopped Count=Nombre stoppé
You have disabled notification in your browser. You should change your browser's settings before you enable this function.=Vous avez désactivé les notifications dans votre navigateur. Vous devriez modifier les paramètresde votre navigateur avant d'activer cette fonction.
Language resource has been updated, please reload the page for the changes to take effect.=La langue de la ressource à changée, recharger la page pour que les changements prennent effet s'il vous plaît.
Configuration has been modified, please reload the page for the changes to take effect.=La configuration été modifiée, recharger la page pour que les changements prennent effet s'il vous plaît.
Reload AriaNg=Recharger AriaNg
Show Secret=Montrer secret
Hide Secret=Cacher secret
Aria2 Version=Version de Aria2
Enabled Features=Fonctionnalités activées
Operations=Opérations
Reconnect=Reconnexion
Save Session=Sauvegarder session
Shutdown Aria2=Arrêter Aria2
Confirm Shutdown=Confirmer l'arrêt
Are you sure you want to shutdown aria2?=Êtes-vous sûr de vouloir arrêter Aria2?
Session has been saved successfully.=La session a été sauvegardée avec succès.
Aria2 has been shutdown successfully.=Aria2 a été arrêté avec succès.
Toggle Navigation=Activer navigation
Shortcut=Raccourci
Global Rate Limit=Limite globale de taux
Loading=Chargement en cours
More Than One Day=Plus d'une journée
Unknown=Inconnu
Bytes=Bytes
Hours=Heures
Minutes=Minutes
Seconds=Secondes
Milliseconds=Millisecondes
Http=Http
Http (Disabled)=Http (Désactivé)
Https=Https
WebSocket=WebSocket
WebSocket (Disabled)=WebSocket (Désactivé)
WebSocket (Security)=WebSocket (Sécurité)
Http and WebSocket would be disabled when accessing AriaNg via Https.=Http et WebSocket devraient êtres désactivés pour un accès à AriaNg en Https.
POST=POST
GET=GET
Enabled=Activé
Disabled=Désactivé
Always=Toujours
Never=Jamais
BitTorrent=BitTorrent
Changes to the settings take effect after refreshing page.=Les changements des paramètres prendront effet après le rafraîchissement de la page.
Logging Time=Temps de connexion
Log Level=Niveau de log
Auto Refresh=Rafraîchissement automatique
Refresh Now=Rafraîchir maintenant
Clear Logs=Effacer les logs
Are you sure you want to clear debug logs?=Êtes-vous sûr de vouloir effacer les logs?
Show Detail=Montrer le détail
Log Detail=Détail de log
Aria2 RPC Debug=Débogage RPC Aria2
Aria2 RPC Request Method=Méthode de requête RPC Aria2
Aria2 RPC Request Parameters=Paramètres de requête RPC Aria2
Aria2 RPC Response=Réponse RPC Aria2
Execute=Éxecuter
RPC method is illegal!=La méthode RPC est illégale!
AriaNg does not support this RPC method!=AriaNg ne supporte pas cette méthode RPC!
RPC request parameters are invalid!=Paramètres de requête RPC invalides!
Type is illegal!=Le type est illégal!
Parameter is invalid!=Le paramètre est invalide!
Option value cannot be empty!=La valeur option ne peut pas être vide!
Input number is invalid!=La saisie du nombre est invalide!
Input number is below min value!=La saisie du nombre est en dessous de la valeur minimale {{value}}!
Input number is above max value!=La saisie du nombre est au dessus la valeur maximale {{value}}!
Input value is invalid!=La saisie de la valeur est invalide!
Protocol is invalid!=Protocole invalide!
RPC host cannot be empty!=L'Hôte RPC ne peut pas être vide!
RPC secret is not base64 encoded!=Le secret RPC n'est pas encodé en base64!
URL is not base64 encoded!=L'URL n'est pas encodée en base64!
Tap to configure and get started with AriaNg.=Cliquer pour configurer et démarrer avec AriaNg.
Cannot initialize WebSocket!=Initialisation du WebSocket impossible!
Cannot connect to aria2!=Connexion à Aria2 impossible!
Access Denied!=Accès refusé!
You cannot use AriaNg because this browser does not meet the minimum requirements for data storage.=Vous ne pouvez pas utiliser AriaNg car votre navigateur ne respecte pas les prérequis minimum pour du stockage de donnée.

[error]
unknown=Erreur inconnue.
operation.timeout=Cette opération a mis trop de temps à répondre.
resource.notfound=La ressource spécifiée n'a pas été trouvée.
resource.notfound.max-file-not-found=La ressource n'a pas été trouvée. Allez voir l'option --max-file-not-found.
download.aborted.lowest-speed-limit=Le téléchargementa été annulé car la vitesse de téléchargement était trop lente. Allez voir l'option --lowest-speed-limit.
network.problem=Erreur réseau.
resume.notsupported=Le serveur distant ne supporte pas la reprise.
space.notenough=Espace de stockage insuffisant.
piece.length.different=La longueur de la pièce est différente de celle dans le fichier de contrôle .aria2. Allez voir l'option --allow-piece-length-change.
download.sametime=Aria2 télécharge déjà ce fichier en ce moment.
download.torrent.sametime=Aria2 télécharge déjà ce fichier en ce moment.
file.exists=Le fichier existe déjà. Allez voir l'option --allow-overwrite.
file.rename.failed=Erreur dans le rennomage du fichier. Allez voir l'option --auto-file-renaming.
file.open.failed=Echec durant l'ouverture du fichier existant.
file.create.failed=Echec durant la création du fichier ou du tronquage d'un fichier existant.
io.error=Erreur de fichier système.
directory.create.failed=Impossible de créer la destination.
name.resolution.failed=Echec durant la résolution du nom.
metalink.file.parse.failed=Erreur durant l'analyse du fichier Metalink.
ftp.command.failed=Echec durant l'exécution de la commande FTP.
http.response.header.bad=La réponse de l'en-tête HTTP a été mauvaise ou inattendue.
redirects.toomany=Trop de redirections rencontrées.
http.authorization.failed=Autorisations HTTP incorrectes.
bencoded.file.parse.failed=Erreur durant l'analyse du fichier bencoded (un fichier ".torrent" la plupart du temps).
torrent.file.corrupted=Le fichier torrent est corrompu ou il manque des informations dont Aria2 a besoin.
magnet.uri.bad=L'URI du magnet est invalide.
option.bad=Une mauvaise option a été donnée ou un argument d'option inattendu a été donné.
server.overload=Le serveur distant a été dans l'incapacité d'effectuer la requête à cause d'une maintenance ou une surcharge temporaire.
rpc.request.parse.failed=Echec dans l'analyse de la requête JSON-RPC.
checksum.failed=La vérification du checksum a échoué.

[languages]
Czech=Tchèque
German=Allemand
English=Anglais
Spanish=Espagnol
French=Français
Italian=Italien
Polish=Polonais
Russian=Russe
Simplified Chinese=Chinois Simplifié
Traditional Chinese=Chinois Traditionnel

[format]
longdate=DD/MM/YYYY HH:mm:ss
time.millisecond={{value}} milliseconde
time.milliseconds={{value}} millisecondes
time.second={{value}} seconde
time.seconds={{value}} secondes
time.minute={{value}} minute
time.minutes={{value}} minutes
time.hour={{value}} heure
time.hours={{value}} heures
requires.aria2-version=Nécessite Aria2 v{{version}} ou supérieure
task.new.download-links=Liens de téléchargement ({{count}} liens):
task.pieceinfo=Complété: {{completed}}, Total: {{total}}
task.error-occurred=Erreur rencontrée ({{errorcode}})
task.verifying-percent=Vérification ({{verifiedPercent}}%)
settings.file-count=({{count}} fichiers)
settings.total-count=(Nombre total: {{count}})
debug.latest-logs=Derniers {{count}} logs

[rpc.error]
unauthorized=Autorisation incorrecte!

[option]
true=Vrai
false=Faux
default=Défaut
none=Aucun
hide=Masquer
full=Complet
http=Http
https=Https
ftp=Ftp
mem=Mémoire seulement
get=GET
tunnel=TUNNEL
plain=Texte brut
arc4=ARC4
binary=Binaire
ascii=ASCII
debug=Débogage
info=Information
notice=Annonce
warn=Avertissement
error=Erreur
adaptive=Adaptatif
epoll=epoll
falloc=falloc
feedback=Feedback
geom=geom
inorder=Dans l'ordre
kqueue=kqueue
poll=poll
port=port
prealloc=Pré-allouer
random=Aléatoire
select=Sélectionner
trunc=trunc
SSLv3=SSLv3
TLSv1=TLSv1
TLSv1.1=TLSv1.1
TLSv1.2=TLSv1.2

[options]
dir.name=Répertoire de téléchargement
dir.description=
log.name=Nom du fichier de log
log.description=Le nom du fichier de log. Si - est spécifié, le fichier sera écrit dans le stdout. Si il est vide (""), ou si cette option est oubliée, aucun fichier de log ne sera écrit sur le disque..
max-concurrent-downloads.name=Nombre maximum de téléchargements simultanés
max-concurrent-downloads.description=Définit le nombre maximum de téléchargements simultanés.
check-integrity.name=Vérifier l'intégrité
check-integrity.description=Vérifier l'intégrité d'un fichier en validant le hash par pièce ou avec le fichier entier. Cette option n'a d'effet que sur BitTorrent, les liens Metalink se téléchargent avec les checksums ou les liens HTTP(S)/FTP se téléchargent --checksum option.
continue.name=Reprendre le téléchargement
continue.description=Poursuivre le téléchargement d'un fichier partiellement téléchargé. Utilisez cette option pour reprendre un téléchargement commencé par un navigateur web ou un autre programme qui télécharge les fichiers séquentiellement depuis le début. Actuellement, cette option ne s'applique qu'aux téléchargements HTTP(S)/FTP.
all-proxy.name=Serveur proxy
all-proxy.description=Utiliser un serveur proxy pour tous les protocoles. Vous pouvez également remplacer ce paramètre et spécifier un serveur proxy pour un protocole particulier en utilisant --http-proxy, --https-proxy et --ftp-proxy. Le format de PROXY est [http://][UTILISATEUR:MOT DE PASSE@]HÔTE[:PORT].
all-proxy-user.name=Nom d'utilisateur du serveur proxy
all-proxy-user.description=
all-proxy-passwd.name=Mot de passe du serveur proxy
all-proxy-passwd.description=
checksum.name=Checksum
checksum.description= Définir le checksum. Le format de la valeur de l'option est TYPE=DIGEST. TYPE est le type de hachage. Le type de hachage supporté est listé dans Algorithmes de hachage dans aria2c -v. DIGEST est un condensé hexadécimal. Par exemple, l'option sha-1 digest ressemble à ceci : sha-1=0192ba11326fe2298c8cb4de616f4d4140213838 Cette option ne s'applique qu'aux téléchargements HTTP(S)/FTP.
connect-timeout.name=Temps de connexion
connect-timeout.description=Définit le délai de connexion en secondes pour établir la connexion au serveur HTTP/FTP/proxy. Une fois la connexion établie, cette option n'a plus d'effet et l'option --timeout est utilisée à la place.
dry-run.name=Test à blanc
dry-run.description=Si vrai est donné, aria2 vérifie simplement si le fichier distant est disponible et ne télécharge pas les données. Cette option a un effet sur les téléchargements HTTP/FTP. Les téléchargements BitTorrent sont annulés si vrai est spécifié.
lowest-speed-limit.name=Limite de vitesse la plus basse
lowest-speed-limit.description=Couper la connexion si la vitesse de téléchargement est inférieure ou égale à cette valeur (octets par seconde). 0 signifie qu'aria2 n'a pas de limite de vitesse minimale. Vous pouvez ajouter K ou M (1K = 1024, 1M = 1024K). Cette option n'affecte pas les téléchargements BitTorrent.
max-connection-per-server.name=Nombre maximum de connexions par serveur.
max-connection-per-server.description=
max-file-not-found.name=Nombre maximum d'essais de fichier non trouvé
max-file-not-found.description=Si aria2 reçoit NUM fois le statut « fichier non trouvé » des serveurs HTTP/FTP distants sans obtenir un seul octet, il faut alors forcer l'échec du téléchargement. Spécifiez 0 pour désactiver cette option. Cette option n'est efficace que lors de l'utilisation de serveurs HTTP/FTP. Le nombre de tentatives est pris en compte dans l'option --max-tries, et doit donc être configuré également.
max-tries.name=Nombre maximum de tentatives.
max-tries.description=Définir le nombre de tentatives maximum. 0 signifie illimité.
min-split-size.name=Taille minimale du fractionnement.
min-split-size.description=Aria2 ne fractionne pas les fichiers d'une taille inférieure à 2*POIDS octets. Par exemple, considérons le téléchargement d'un fichier de 20MiB. Si POIDS est 10M, aria2 peut diviser le fichier en 2 plages [0-10MiB) et [10MiB-20MiB) et le télécharger en utilisant 2 sources (si --split >= 2, bien sûr). Si POIDS est 15M, puisque 2*15M > 20MiB, aria2 ne divise pas le fichier et le télécharge en utilisant 1 source. Vous pouvez ajouter K ou M (1K = 1024, 1M = 1024K). Valeurs possibles : 1M-1024M.
netrc-path.name=Chemin d'accès au .netrc
netrc-path.description=
no-netrc.name=Désactiver netrc
no-netrc.description=
no-proxy.name=Liste des serveurs pour lesquels ne pas utiliser de proxy.
no-proxy.description=Spécifiez une liste de noms d'hôtes, de domaines et d'adresses réseau séparés par des virgules, avec ou sans masque de sous-réseau, pour lesquels aucun proxy ne doit être utilisé.
out.name=Nom de fichier
out.description=Le nom du fichier téléchargé. Il est toujours relatif au répertoire donné dans l'option --dir. Lorsque l'option --force-sequential est utilisée, cette option est ignorée.
proxy-method.name=Méthode de requête du serveur proxy
proxy-method.description=Définit la méthode à utiliser dans la requête proxy. METHOD ou GET ou TUNNEL. Les téléchargements HTTPS utilisent toujours TUNNEL, quelle que soit cette option.
remote-time.name=Horodatage du fichier distant
remote-time.description=Récupérer l'horodatage du fichier distant à partir du serveur HTTP/FTP distant et, s'il est disponible, l'appliquer au fichier local.
reuse-uri.name=Réutiliser L'URI
reuse-uri.description=Réutiliser les URI déjà utilisés s'il ne reste plus d'URI inutilisés.
retry-wait.name=Temps d'attente pour une nouvelle tentative.
retry-wait.description=Définit le nombre de secondes à attendre entre les tentatives. Lorsque SEC > 0, aria2 réessaie les téléchargements lorsque le serveur HTTP renvoie une réponse 503.
server-stat-of.name=Sortie des statistiques du serveur.
server-stat-of.description=Spécifie le nom du fichier dans lequel le profil de performance des serveurs est sauvegardé. Vous pouvez charger les données enregistrées à l'aide de l'option --server-stat-if.
server-stat-timeout.name=Délai d'attente pour le statut du serveur
server-stat-timeout.description=Spécifie le délai en secondes pour invalider le profil de performance des serveurs depuis le dernier contact avec eux.
split.name=Connexion par téléchargement
split.description=Télécharger un fichier en utilisant N connexions. Si plus de N URIs sont donnés, les N premiers URIs sont utilisés et les URIs restants sont utilisés pour la sauvegarde. Si moins de N URIs sont donnés, ces URIs sont utilisés plus d'une fois de façon à ce que N connexions au total soient effectuées simultanément. Le nombre de connexions au même hôte est limité par l'option --max-connexion-par-serveur.
stream-piece-selector.name=Algorithme de sélection des pièces.
stream-piece-selector.description=Spécifie l'algorithme de sélection des morceaux utilisé dans le téléchargement HTTP/FTP. Un morceau est un segment de longueur fixe qui est téléchargé en parallèle dans le cadre d'un téléchargement segmenté. Si la valeur par défaut est donnée, aria2 sélectionne le morceau de manière à réduire le nombre de connexions à établir. Ce comportement par défaut est raisonnable car l'établissement d'une connexion est une opération coûteuse. Si dans l'ordre est donné, aria2 sélectionne le morceau qui a l'index minimum. Index=0 signifie le premier du fichier. Ceci est utile pour visionner un film pendant son téléchargement. L'option --enable-http-pipelining peut être utile pour réduire les frais de reconnexion. Veuillez noter qu'aria2 honore l'option --min-split-size, il sera donc nécessaire de spécifier une valeur raisonnable à l'option --min-split-size. Si random est donné, aria2 sélectionne les pièces de manière aléatoire. Comme pour dans l'ordre, l'option --min-split-size est respectée. Si geom est donné, au début, aria2 sélectionne le morceau qui a l'index minimum comme dans l'ordre, mais il garde exponentiellement de plus en plus de place par rapport au morceau précédemment sélectionné. Cela réduira le nombre de connexions à établir et, en même temps, téléchargera d'abord le début du fichier. Cela permet de visionner un film tout en le téléchargeant.
timeout.name=Timeout
timeout.description=
uri-selector.name=Algorithme de sélection des URI
uri-selector.description=Spécifie l'algorithme de sélection des URI. Les valeurs possibles sont dans l'ordre, feedback et adaptif. Si dans l'ordre est donné, l'URI est essayé dans l'ordre où il apparaît dans la liste des URI. Si feedback est donné, aria2 utilise la vitesse de téléchargement observée lors des téléchargements précédents et choisit le serveur le plus rapide dans la liste des URI. Cela permet également d'éviter les miroirs morts. La vitesse de téléchargement observée fait partie du profil de performance des serveurs mentionnés dans --server-stat-of et --server-stat-if Si adaptif est indiqué, aria2 sélectionne l'un des meilleurs miroirs pour la première connexion et les connexions réservées. Pour les connexions supplémentaires, il renvoie les miroirs qui n'ont pas encore été testés, et si chacun d'entre eux a déjà été testé, il renvoie les miroirs qui doivent être testés à nouveau. Dans le cas contraire, il ne sélectionne plus de miroirs. Comme le feedback, il utilise un profil de performance des serveurs.
check-certificate.name=Vérifier le certificat
check-certificate.description=
http-accept-gzip.name=Accepter GZip
http-accept-gzip.description= Envoyer l'en-tête de requête Accepter : deflate, gzip et la réponse inflate si le serveur distant répond avec Content-Encoding : gzip ou Content-Encoding : deflate.
http-auth-challenge.name=Authentification défi-réponse
http-auth-challenge.description=Envoyer l'en-tête d'autorisation HTTP uniquement lorsque le serveur le demande. Si faux est défini, l'en-tête d'autorisation est toujours envoyé au serveur. Il existe une exception : si le nom d'utilisateur et le mot de passe sont intégrés dans l'URI, l'en-tête d'autorisation est toujours envoyé au serveur, indépendamment de cette option.
http-no-cache.name=Désactiver le cache
http-no-cache.description=Envoyer les en-têtes Cache-Control : no-cache et Pragma : no-cache pour éviter le contenu mis en cache. Si faux est donné, ces en-têtes ne sont pas envoyés et vous pouvez ajouter l'en-tête Cache-Control avec une directive de votre choix en utilisant l'option --header.
http-user.name=Nom d'utilisateur HTTP par défaut
http-user.description=
http-passwd.name=Mot de passe HTTP par défaut
http-passwd.description=
http-proxy.name=Serveur proxy HTTP
http-proxy.description=
http-proxy-user.name=Nom d'utilisateur serveur proxy HTTP
http-proxy-user.description=
http-proxy-passwd.name=Mot de passe serveur proxy HTTP
http-proxy-passwd.description=
https-proxy.name=Serveur proxy HTTPS
https-proxy.description=
https-proxy-user.name=Nom d'utilisateur serveur proxy HTTPS
https-proxy-user.description=
https-proxy-passwd.name=Mot de passe serveur proxy HTTPS
https-proxy-passwd.description=
referer.name=Référent
referer.description=Définir un référent http (Référent). Ceci affecte tous les téléchargements http/https. Si * est donné, l'URI de téléchargement est également utilisé comme référent. Cette option peut être utile lorsqu'elle est utilisée avec l'option --parameterized-uri.
enable-http-keep-alive.name=Activer la connexion permanente
enable-http-keep-alive.description=Activer la connexion persistante pour HTTP/1.1.
enable-http-pipelining.name=Activer le pipeline pour HTTP
enable-http-pipelining.description=Activer le pipeline pour HTTP/1.1.
header.name=En-tête personnalisée
header.description=Ajouter EN-TÊTE à l'en-tête de la requête HTTP. Mettre un élément par ligne, chaque élément contenant « nom de l'en-tête : valeur de l'en-tête ».
save-cookies.name=Chemin des cookies
save-cookies.description=Sauvegarde des cookies dans un FICHIER au format Mozilla/Firefox (1.x/2.x)/ Netscape. Si FICHIER existe déjà, il est écrasé. Les cookies de session sont également sauvegardés et leurs valeurs d'expiration sont traitées comme 0.
use-head.name=Utiliser la méthode HEAD
use-head.description=Utiliser la méthode HEAD pour la première requête adressée au serveur HTTP.
user-agent.name=Agent utilisateur personnalisé
user-agent.description=
ftp-user.name=Nom d'utilisateur FTP par défaut
ftp-user.description=
ftp-passwd.name=Mot de passe FTP par défaut
ftp-passwd.description=Si le nom d'utilisateur est intégré mais que le mot de passe est absent de l'URI, aria2 tente de résoudre le mot de passe à l'aide de .netrc. Si le mot de passe est trouvé dans .netrc, il est utilisé comme mot de passe. Sinon, il faut utiliser le mot de passe spécifié dans cette option.
ftp-pasv.name=Mode passif
ftp-pasv.description=Utiliser le mode passif dans FTP. Si faux est donné, le mode actif sera utilisé. Cette option est ignorée pour les transferts SFTP.
ftp-proxy.name=Serveur Proxy FTP
ftp-proxy.description=
ftp-proxy-user.name=Nom d'utilisateur serveur proxy FTP
ftp-proxy-user.description=
ftp-proxy-passwd.name=Mot de passe serveur proxy FTP
ftp-proxy-passwd.description=
ftp-type.name=Type de transfert
ftp-type.description=
ftp-reuse-connection.name=Réutiliser la connexion
ftp-reuse-connection.description=
ssh-host-key-md.name=Checksum de la clé publique SSH
ssh-host-key-md.description=Définir le checksum pour la clé publique de l'hôte SSH. Le format de la valeur de l'option est TYPE=DIGEST. TYPE est le type de hachage. Le type de hachage pris en charge est sha-1 ou md5. DIGEST est un condensé hexagonal. Par exemple : sha-1=b030503d4de4539dc7885e6f0f5e256704edf4c3. Cette option peut être utilisée pour valider la clé publique du serveur lorsque SFTP est utilisé. Si cette option n'est pas définie, ce qui est le cas par défaut, aucune validation n'a lieu.
bt-detach-seed-only.name=Séparer uniquement les tâches de seed
bt-detach-seed-only.description=Exclure les téléchargements en mode seed only lors du comptage des téléchargements actifs simultanés (voir l'option -j). Cela signifie que si -j3 est donné et que cette option est activée, que 3 téléchargements sont actifs et que l'un d'entre eux entre en mode semence, il est exclu du décompte des téléchargements actifs (il devient donc 2), et le téléchargement suivant qui attend dans la file d'attente démarre. Mais attention, l'élément en mode semence est toujours reconnu comme un téléchargement actif dans la méthode RPC.
bt-enable-hook-after-hash-check.name=Activer l'événement de fin de contrôle de Hash
bt-enable-hook-after-hash-check.description=Autorise l'invocation de la commande hook après la vérification du hachage (voir l'option -V) dans le téléchargement BitTorrent. Par défaut, lorsque la vérification du hachage réussit, la commande donnée par l'option --on-bt-download-complete est exécutée. Pour désactiver cette action, mettez faux à cette option.
bt-enable-lpd.name=Activer la découverte locale de peer (LPD)
bt-enable-lpd.description=Activer la découverte de peer locaux. Si un drapeau privé est défini dans un torrent, aria2 n'utilise pas cette fonctionnalité pour ce téléchargement, même si vrai est indiqué.
bt-exclude-tracker.name=Exclure les trackers BitTorrent.
bt-exclude-tracker.description=Liste séparée par des virgules des URI d'annonce des trackers BitTorrent à supprimer. Vous pouvez utiliser la valeur spéciale * qui correspond à tous les URI et supprime donc tous les URI d'annonce. Lorsque vous spécifiez * dans la ligne de commande du shell, n'oubliez pas de l'échapper ou de le mettre entre guillemets.
bt-external-ip.name=Adresse IP Externe
bt-external-ip.description=Spécifiez l'adresse IP externe à utiliser pour le téléchargement BitTorrent et le DHT. Elle peut être envoyée au tracker BitTorrent. Pour le DHT, cette option doit être définie pour signaler que le nœud local télécharge un torrent particulier. Cela est essentiel pour utiliser le DHT dans un réseau privé. Bien que cette fonction soit nommée externe, elle peut accepter n'importe quel type d'adresse IP.
bt-force-encryption.name=Forcer chiffrement
bt-force-encryption.description=Requiert le chiffrement de la charge utile des messages BitTorrent avec arc4. Il s'agit d'une abréviation de --bt-require-crypto --bt-min-crypto-level=arc4. Cette option ne modifie pas la valeur de ces options. Si vrai est donné, refuser l'ancienne poignée de main BitTorrent et n'utiliser que l'établissement d'une liaison Obfuscation et toujours crypter les données utiles du message.
bt-hash-check-seed.name=Vérifier le hash avant de seed
bt-hash-check-seed.description=Si vrai est donné, après la vérification du hachage en utilisant l'option --check-integrity et que le fichier est complet, continuer à ensemencer le fichier. Si vous souhaitez vérifier le fichier et le télécharger uniquement lorsqu'il est endommagé ou incomplet, mettez cette option sur faux. Cette option n'a d'effet que sur les téléchargements BitTorrent.
bt-load-saved-metadata.name=Charger le fichier de métadonnées enregistré
bt-load-saved-metadata.description=Avant d'obtenir les métadonnées du torrent à partir du DHT lors d'un téléchargement avec un lien magnétique, essayez d'abord de lire le fichier enregistré avec l'option --bt-save-metadata. Si l'opération réussit, ignorez le téléchargement des métadonnées à partir de la DHT.
bt-max-open-files.name=Nombre maximum de fichiers ouverts
bt-max-open-files.description=Spécifier le nombre maximum de fichiers à ouvrir dans un téléchargement BitTorrent/Metalink multi-fichiers de manière globale.
bt-max-peers.name=Peer maximum
bt-max-peers.description=Spécifiez le nombre maximum de peer par torrent. 0 signifie illimité.
bt-metadata-only.name=Télécharger les métadonnées uniquement
bt-metadata-only.description=Télécharger uniquement les métadonnées. Le(s) fichier(s) décrit(s) dans les métadonnées ne sera(ont) pas téléchargé(s). Cette option n'a d'effet que lorsque l'URI BitTorrent Magnet est utilisé.
bt-min-crypto-level.name=Niveau minimum de cryptage
bt-min-crypto-level.description=Définir le niveau minimum de la méthode de cryptage. Si plusieurs méthodes de chiffrement sont fournies par un peer, aria2 choisit la plus basse qui satisfait le niveau donné.
bt-prioritize-piece.name=Donner la priorité à une pièce
bt-prioritize-piece.description=Essayer de télécharger d'abord le premier et le dernier morceau de chaque fichier. Ceci est utile pour la prévisualisation des fichiers. L'argument peut contenir 2 mots-clés : head et tail. Pour inclure les deux mots-clés, ils doivent être séparés par une virgule. Ces mots-clés peuvent prendre un paramètre, POIDS. Par exemple, si head=POIDS est spécifié, les pièces situées dans la plage des premiers octets de taille de chaque fichier ont une priorité plus élevée. tail=POIDS signifie la plage des derniers octets de taille de chaque fichier. POIDS peut inclure K ou M (1K = 1024, 1M = 1024K).
bt-remove-unselected-file.name=Supprimer les fichiers non sélectionnés
bt-remove-unselected-file.description=Supprime les fichiers non sélectionnés lorsque le téléchargement est terminé dans BitTorrent. Pour sélectionner des fichiers, utilisez l'option --select-file. Si elle n'est pas utilisée, tous les fichiers sont supposés être sélectionnés. Veuillez utiliser cette option avec précaution car elle supprimera des fichiers de votre disque.
bt-require-crypto.name=Exiger cryptage
bt-require-crypto.description=Si vrai est donné, aria2 n'accepte pas et n'établit pas de connexion avec l'établissement d'une liaison BitTorrent (protocole BitTorrent). Ainsi, aria2 utilise toujours l'établissement d'une liaison Obfuscation.
bt-request-peer-speed-limit.name=Vitesse de téléchargement préférée
bt-request-peer-speed-limit.description=Si la vitesse de téléchargement totale de chaque torrent est inférieure à VITESSE, aria2 augmente temporairement le nombre de peer pour essayer d'augmenter la vitesse de téléchargement. Configurer cette option avec votre vitesse de téléchargement préférée peut augmenter votre vitesse de téléchargement dans certains cas. Vous pouvez ajouter K ou M (1K = 1024, 1M = 1024K).
bt-save-metadata.name=Sauvegarder Metadata
bt-save-metadata.description=Enregistrer les métadonnées dans un fichier « .torrent ». Cette option n'a d'effet que lorsque l'URI BitTorrent Magnet est utilisé. Le nom du fichier est un hachage d'informations codé en hexadécimal avec le suffixe « .torrent ». Le répertoire à enregistrer est le même que celui dans lequel le fichier de téléchargement est enregistré. Si le même fichier existe déjà, les métadonnées ne sont pas sauvegardées.
bt-seed-unverified.name=Ne pas vérifier les fichiers téléchargés.
bt-seed-unverified.description=Ne vérifie pas la valeur de hachage de chaque pièce des fichiers précédemment téléchargés.
bt-stop-timeout.name=Délai d'arrêt automatique si blocage à 0
bt-stop-timeout.description=Lorsque la vitesse de téléchargement d'une tâche BT reste à 0 pendant la durée définie par cette option, le téléchargement s'arrête. Si cette option est réglée sur 0, cette fonctionnalité est désactivée.
bt-tracker.name=Adresse du traqueur BitTorrent
bt-tracker.description=Adresses des serveurs BitTorrent séparées par des virgules. Ces adresses ne sont pas affectées par l'option --bt-exclude-tracker, car elles ne sont ajoutées qu'après que l'option --bt-exclude-tracker a exclu d'autres adresses.
bt-tracker-connect-timeout.name=Délai de connexion au serveur BitTorrent.
bt-tracker-connect-timeout.description=Définit le délai de connexion au serveur BT en secondes. Une fois la connexion établie, cette option n'a plus d'effet, utilisez l'option --bt-tracker-timeout.
bt-tracker-interval.name=Intervalle de connexion au serveur BT.
bt-tracker-interval.description=Définit l'intervalle de requête vers le serveur BT en secondes. Cette option remplace complètement l'intervalle minimum et l'intervalle renvoyé par le serveur, Aria2 n'utilisera que la valeur de cette option. Si elle est fixée à 0, aria2 décidera de l'intervalle en fonction de la réponse du serveur et de la progression du téléchargement.
bt-tracker-timeout.name=Délai d'attente pour le serveur BT.
bt-tracker-timeout.description=
dht-file-path.name=Fichier DHT (IPv4)
dht-file-path.description=Modifier le chemin d'accès au fichier de la table de routage du DHT IPv4.
dht-file-path6.name=Fichier DHT (IPv6)
dht-file-path6.description=Modifier le chemin d'accès au fichier de la table de routage du DHT IPv6.
dht-listen-port.name=Port d'écoute DHT
dht-listen-port.description=Définit le port UDP utilisé par la DHT (IPv4, IPv6) et le serveur UDP. Plusieurs ports peuvent être séparés par des virgules ',', par exemple : 6881,6885. Vous pouvez également utiliser un tiret '-' pour indiquer une plage : 6881-6999, ou les deux ensemble : 6881-6889, 6999.
dht-message-timeout.name=Délai d'attente pour les messages DHT.
dht-message-timeout.description=
enable-dht.name=Activer DHT (IPv4)
enable-dht.description=Activer la fonction DHT IPv4. Cette option active également la prise en charge du serveur UDP. Si le torrent est marqué comme privé, aria2 n'activera pas la fonction DHT même si cette option est réglée sur vrai.
enable-dht6.name=Activer DHT (IPv6)
enable-dht6.description=Activer la fonction DHT IPv4. Cette option active également la prise en charge du serveur UDP. Si le torrent est marqué comme privé, aria2 n'activera pas la fonction DHT même si cette option est réglée sur vrai. Utilisez l'option --dht-listen-port pour spécifier le numéro de port sur lequel écouter.
enable-peer-exchange.name=Activer l'échange entre peer
enable-peer-exchange.description=Activer l'extension d'échange entre peer. Si le torrent est marqué comme privé, Aria2 n'activera pas cette fonction même si cette option est réglée sur vrai.
follow-torrent.name=Télécharger des fichiers .torrent
follow-torrent.description=Si vrai ou mémoire seulement est spécifié, lorsqu'un fichier dont le suffixe est .torrent ou le type de contenu est application/x-bittorrent est téléchargé, Aria2 l'analyse comme un fichier torrent et télécharge les fichiers qui y sont mentionnés. Si mémoire seulement est spécifié, un fichier torrent n'est pas écrit sur le disque, mais est simplement conservé en mémoire. Si faux est spécifié, le fichier .torrent est téléchargé sur le disque, mais n'est pas analysé comme un torrent et son contenu n'est pas téléchargé.
listen-port.name=Port d'écoute
listen-port.description=Définit le numéro de port TCP pour les téléchargements BitTorrent. Plusieurs ports peuvent être spécifiés en utilisant « , », par exemple : 6881,6885. Vous pouvez également utiliser - pour spécifier une plage : 6881-6999. Les caractères , et - peuvent être utilisées ensemble : 6881-6889,6999.
max-overall-upload-limit.name=Vitesse maximale de téléversement global
max-overall-upload-limit.description=Définit la vitesse maximale de téléversement global en octets/seconde. 0 indique qu'il n'y a pas de limite. Vous pouvez augmenter la valeur en ajoutant des unités K ou M (1K=1024, 1M=1024K).
max-upload-limit.name=Vitesse maximale de téléversement
max-upload-limit.description=Définit la vitesse maximale de téléversement pour chaque tâche en octets/seconde. 0 indique qu'il n'y a pas de limite. Vous pouvez augmenter la valeur en ajoutant des unités K ou M (1K=1024, 1M=1024K).
peer-id-prefix.name=Préfixe de l'ID du nœud
peer-id-prefix.description=Spécifie le préfixe de l'ID du nœud. L'ID de nœud dans BitTorrent a une longueur de 20 octets. S'il est plus long que 20 octets, seuls les 20 premiers octets seront utilisés. S'il est plus court que 20 octets, des données aléatoires seront ajoutées pour atteindre 20 octets.
peer-agent.name=Agent de peer
peer-agent.description=Spécifie la chaîne utilisée pour la version du client du nœud lors de l'établissement de la liaison étendue de BT.
seed-ratio.name=Ratio de partage minimum
seed-ratio.description=Spécifie le ratio de partage. Le partage se termine lorsque le ratio de partage atteint la valeur définie dans cette option. Il est fortement recommandé de fixer cette option à une valeur supérieure ou égale à 1,0. Si vous ne souhaitez pas limiter le ratio de partage, vous pouvez le fixer à 0.0. Si vous définissez également l'option --seed-time, le partage se terminera lorsque l'une ou l'autre de ces conditions sera remplie.
seed-time.name=Temps de partage minimum.
seed-time.description=Spécifie le temps de partage en minutes (au format décimal). Si cette option a la valeur 0, le partage n'aura pas lieu après l'achèvement du téléchargement de la tâche BT.
follow-metalink.name=Télécharger des fichiers dans Metalink.
follow-metalink.description=S'il est réglé sur vrai ou mémoire seulement, lorsqu'un fichier avec un suffixe .meta4 ou .metalink ou un contenu de type application/metalink4+xml ou application/metalink+xml est terminé, Aria2 lira et téléchargera les fichiers mentionnés dans le fichier Metalink. Si le réglage est mémoire seulement, le fichier Metalink ne sera pas écrit sur le disque, mais seulement stocké en mémoire. S'il est réglé sur faux, le fichier .metalink sera téléchargé sur le disque, mais ne sera pas lu et les fichiers qu'il contient ne seront pas téléchargés.
metalink-base-uri.name=URI de base.
metalink-base-uri.description=Spécifier l'URI de base pour résoudre l'URI relatif dans les éléments metalink:url et metalink:metaurl d'un fichier metalink stocké sur le disque local. Si l'URI pointe vers un répertoire, il doit se terminer par /.
metalink-language.name=Langage
metalink-language.description=
metalink-location.name=Localisation préféré du serveur
metalink-location.description=Localisation préféré du serveur. Vous pouvez utiliser une liste séparée par des virgules, par exemple : jp,us.
metalink-os.name=Système d'exploitation
metalink-os.descriptionURI di base=Le système d'exploitation du fichier à télécharger.
metalink-version.name=Numéro de version
metalink-version.description=Le numéro de version du fichier à télécharger.
metalink-preferred-protocol.name=Protocole préféré
metalink-preferred-protocol.description=Spécifie le protocole préféré à utiliser. Il peut s'agir de http, https, ftp ou aucun. Si la valeur est aucun, cette option est désactivée.
metalink-enable-unique-protocol.name=Utiliser un protocole unique.
metalink-enable-unique-protocol.description=Si un fichier Metalink est disponible sur plusieurs protocoles et que cette option est réglée sur vrai, aria2 n'utilisera que l'un d'entre eux. Utilisez le paramètre --metalink-preferred-protocol pour spécifier le protocole préféré.
enable-rpc.name=Active le serveur JSON-RPC/XML-RPC
enable-rpc.description=
pause-metadata.name=Pause après le téléchargement des métadonnées
pause-metadata.description=Interrompre les téléchargements créés à la suite d'un téléchargement de métadonnées. Il existe 3 types de téléchargements de métadonnées dans aria2 : (1) téléchargement d'un fichier .torrent. (2) téléchargement de métadonnées de torrent à l'aide d'un lien magnet. (3) téléchargement d'un fichier metalink. Ces téléchargements de métadonnées génèrent des téléchargements à partir de leurs métadonnées. Cette option met en pause ces téléchargements ultérieurs. Cette option n'est efficace que si l'option --enable-rpc=true est activée.
rpc-allow-origin-all.name=Accepte toutes les requêtes à distance.
rpc-allow-origin-all.description=Ajouter le champ d'en-tête Access-Control-Allow-Origin avec la valeur * à la réponse RPC.
rpc-listen-all.name=Écoute sur toutes les interfaces réseau.
rpc-listen-all.description=Écoute les requêtes JSON-RPC/XML-RPC entrantes sur toutes les interfaces réseau. Si faux est donné, l'écoute ne se fera que sur l'interface loopback locale.
rpc-listen-port.name=Port d'écoute.
rpc-listen-port.description=
rpc-max-request-size.name=Taille maximale de la requête.
rpc-max-request-size.description=Définit la taille maximale d'une requête JSON-RPC/XML-RPC. Si aria2 détecte que la requête est supérieure à POIDS bytes, il interrompt la connexion.
rpc-save-upload-metadata.name=Sauvegarder les fichiers torrent téléchargés.
rpc-save-upload-metadata.description=Enregistrer les métadonnées du torrent ou du métalink téléchargés dans le répertoire spécifié par l'option --dir. Le nom du fichier se compose de la chaîne hexagonale de hachage SHA-1 des métadonnées et de l'extension. Pour le torrent, l'extension est \'.torrent\'. Pour metalink, il s'agit de \'.meta4\'. Si faux est donné à cette option, les téléchargements ajoutés par aria2.addTorrent() ou aria2.addMetalink() ne seront pas sauvegardés par l'option --save-session.
rpc-secure.name=Activer SSL/TLS.
rpc-secure.description=Le RPC sera transmis via un cryptage SSL/TLS. Le client RPC doit utiliser le protocole https pour se connecter au serveur. Pour les clients WebSocket, utilisez le protocole wss. Utilisez les options --rpc-certificate et --rpc-private-key pour définir le certificat et la clé privée du serveur.
allow-overwrite.name=Autoriser l'écrasement
allow-overwrite.description=Recharge le fichier depuis le début si le fichier de contrôle correspondant n'existe pas. Voir l'option --auto-file-renaming.
allow-piece-length-change.name=Autoriser la modification de la longueur des pièces.
allow-piece-length-change.description=Si faux est donné, Aria2 interrompt le téléchargement lorsque la longueur d'un morceau est différente de celle d'un fichier de contrôle. Si vrai est donné, vous pouvez continuer mais une partie de la progression du téléchargement sera perdue.
always-resume.name=Toujours reprendre le téléchargement.
always-resume.description=Toujours reprendre le téléchargement. Si vrai est donné, aria2 essaie toujours de reprendre le téléchargement et si la reprise n'est pas possible, abandonne le téléchargement. Si faux est donné, lorsque tous les URIs donnés ne supportent pas la reprise ou que aria2 rencontre N URIs qui ne supportent pas la reprise (N est la valeur spécifiée avec l'option --max-resume-failure-tries), aria2 télécharge le fichier à partir de zéro. Voir l'option --max-resume-failure-tries.
async-dns.name=DNS asynchrone
async-dns.description=
auto-file-renaming.name=Rennomage automatique de fichier
auto-file-renaming.description=Renommer le nom du fichier si le même fichier existe déjà. Cette option ne fonctionne que pour les téléchargements HTTP(S)/FTP. Le nouveau nom de fichier est suivi d'un point et d'un nombre (1..9999), mais pas de l'extension du fichier, le cas échéant.
auto-save-interval.name=Intervalle de sauvegarde automatique.
auto-save-interval.description=Enregistre automatiquement le fichier de contrôle (*.aria2) toutes les secondes spécifiées. Si la valeur est 0, le fichier de contrôle n'est pas sauvegardé automatiquement pendant le téléchargement. Quelle que soit la valeur définie, aria2 enregistre le fichier de contrôle à la fin de la tâche. La valeur peut être comprise entre 0 et 600.
conditional-get.name=Téléchargement conditionnel
conditional-get.description=Télécharger le fichier uniquement s'il est plus ancien que le fichier local. Cette fonction ne fonctionne que pour les téléchargements HTTP(S). Si la taille du fichier a déjà été spécifiée dans Metalink, la fonction n'aura aucun effet. En outre, cette fonction ignore l'en-tête de réponse Content-Disposition. Si un fichier de contrôle existe, il sera ignoré. Cette fonction utilise l'en-tête de requête If-Modified-Since pour récupérer le fichier le plus récent. Lorsque l'heure de modification du fichier local est récupérée, cette fonction utilise le nom de fichier fourni par l'utilisateur (voir l'option --out), ou le nom de fichier dans l'URI si l'option --out n'est pas spécifiée. Pour écraser un fichier existant, le paramètre --allow-overwrite doit être utilisé.
conf-path.name=Chemin d'accès au fichier de configuration.
conf-path.description=
console-log-level.name=Niveau de log de la console
console-log-level.description=
content-disposition-default-utf8.name=Utiliser UTF-8 pour gérer Content-Disposition.
content-disposition-default-utf8.description=Traite les chaînes entre guillemets dans l'en-tête Content-Disposition en UTF-8 au lieu d'ISO-8859-1, par exemple le paramètre nom de fichier, mais pas le nom de fichier de la version étendue.
daemon.name=Activer le processus d'arrière-plan.
daemon.description=
deferred-input.name=Charge différée
deferred-input.description=Si vrai est donné, aria2 ne lit pas tous les URIs et options du fichier spécifié par l'option --input-file au démarrage, mais il les lit un par un quand il en a besoin plus tard. Cela peut réduire l'utilisation de la mémoire si le fichier d'entrée contient beaucoup d'URIs à télécharger. Si faux est donné, aria2 lit tous les URIs et toutes les options au démarrage. L'option --deferred-input sera désactivée si l'option --save-session est utilisée en même temps.
disable-ipv6.name=Désactiver IPv6
disable-ipv6.description=
disk-cache.name=Cache du disque
disk-cache.description=Active la mémoire cache du disque. Si POIDS est égal à 0, la mémoire cache du disque est désactivée. Cette fonction permet de mettre en cache les données téléchargées dans la mémoire, dont la taille maximale est de POIDS octets. La mémoire cache est créée pour l'instance aria2 et partagée par tous les téléchargements. L'avantage du cache disque est de réduire les entrées/sorties du disque, car les données sont écrites dans une unité plus grande et sont réordonnées en fonction du décalage du fichier. Si la vérification du hachage est impliquée et que les données sont mises en cache dans la mémoire, il n'est pas nécessaire de les lire sur le disque. Le poids peut être K ou M (1K = 1024, 1M = 1024K).
download-result.name=Résultat du téléchargement
download-result.description=Cette option modifie le format du résultat du téléchargement. Si elle est réglée sur Défaut, elle imprimera le GID, l'état, la vitesse moyenne de téléchargement et le chemin/URI. Si plusieurs fichiers sont concernés, seul le chemin/URI du premier fichier demandé sera imprimé, les autres seront ignorés. S'il est défini sur Complet, il imprimera le GID, l'état, la vitesse moyenne de téléchargement, la progression du téléchargement et le chemin/URI. Dans ce cas, la progression du téléchargement et le chemin/URI seront imprimés sur une ligne pour chaque fichier. S'il est défini sur Caché, le résultat du téléchargement sera caché.
dscp.name=DSCP
dscp.description=Définir la valeur DSCP dans les paquets IP sortants du trafic BitTorrent pour la qualité de service. Ce paramètre définit uniquement les bits DSCP dans le champ TOS des paquets IP, et non l'ensemble du champ. Si vous prenez des valeurs dans /usr/include/netinet/ip.h, divisez-les par 4 (sinon les valeurs seraient incorrectes, par exemple votre classe CS1 deviendrait CS4). Si vous prenez des valeurs couramment utilisées dans les RFC, la documentation des vendeurs de réseaux, Wikipedia ou toute autre source, utilisez-les telles quelles.
rlimit-nofile.name=Nombre maximal de fichiers descripteurs ouverts
rlimit-nofile.description=Définir la limite souple du nombre de fichiers descripteurs ouverts. Cette option n'est efficace que si : a. Le système la supporte (POSIX). b. La limite ne dépasse pas la limite stricte. c. La limite spécifiée est supérieure à la limite souple actuelle. Cette option est équivalente à l'option ulimit, sauf qu'elle ne peut pas abaisser la limite. Cette option n'est efficace que si le système prend en charge l'API rlimit.
enable-color.name=Utiliser les couleurs dans les sorties terminales.
enable-color.description=
enable-mmap.name=Activer MMap
enable-mmap.description=Stocke les fichiers mappés en mémoire. Si l'espace de fichier n'est pas pré-alloué, cette option n'est pas valide. Voir --file-allocation.
event-poll.name=Méthode d'interrogation des événements.
event-poll.description=Définit la méthode d'interrogation des événements. Les valeurs possibles sont epoll, kqueue, port, poll et sélectionner. Les valeurs epoll, kqueue, port et poll ne sont disponibles que si le système les prend en charge. La plupart des distributions Linux supportent epoll. Plusieurs systèmes *BSD, y compris Mac OS X, supportent kqueue. Open Solaris supporte port. La valeur par défaut varie en fonction du système d'exploitation utilisé.
file-allocation.name=Méthode d'allocation des fichiers
file-allocation.description=Spécifie la méthode d'allocation des fichiers. Aucune n'alloue pas d'espace à l'avance.  Pré-allouer alloue l'espace avant le début du téléchargement. Cela prendra un certain temps en fonction de la taille du fichier. Si un système de fichiers plus récent est utilisé, tel que ext4 (avec un support étendu), btrfs, xfs ou NTFS (versions MinGW uniquement), falloc est le meilleur choix. Il peut allouer de gros fichiers (plusieurs GiB) presque instantanément. N'utilisez pas 'falloc' sur les anciens systèmes de fichiers, tels que ext3 et FAT32, car il prend le même temps que pré-allouer et bloque aria2 jusqu'à ce que l'allocation soit terminée. falloc peut ne pas être disponible si votre système ne supporte pas la fonction posix_fallocate(3). Trunc utilise l'appel système ftruncate(2) ou une implémentation spécifique à la plate-forme pour tronquer un fichier à une longueur spécifique. Dans les téléchargements BitTorrent avec plusieurs fichiers, si un fichier partage les mêmes sections avec un fichier adjacent, les fichiers adjacents seront également alloués.
force-save.name=Forcer la sauvegarde
force-save.description=Sauvegarde la tâche même si elle a été terminée ou supprimée lors de l'utilisation de l'option --save-session. Dans ce cas, cette option enregistre également le fichier de contrôle. Cette option permet de sauvegarder les tâches de BT qui sont considérées comme achevées mais qui sont toujours en seed.
save-not-found.name=Enregistrer un fichier introuvable
save-not-found.description=Lorsque vous utilisez l'option --save-session, la tâche de téléchargement est sauvegardée même si les fichiers de la tâche n'existent pas. Cette option enregistre également cette situation dans le fichier de contrôle.
hash-check-only.name=Vérification du hachage uniquement
hash-check-only.description=S'il a la valeur vrai, il met fin au téléchargement en fonction de la fin du téléchargement après l'exécution de la vérification du hachage à l'aide de l'option --check-integrity.
human-readable.name=Sortie lisible par la console
human-readable.description=Imprimer le poids et la vitesse dans un format lisible par la console (par exemple, 1.2Ki, 3.4Mi).
keep-unfinished-download-result.name=Conserver les résultats des tâches inachevées
keep-unfinished-download-result.description=Conserve tous les résultats des tâches de téléchargement non terminées, même s'ils dépassent le nombre défini par l'option --max-download-result. Cela peut aider à préserver tous les téléchargements inachevés dans le fichier de session (voir l'option --save-session). Il est important de noter qu'il n'y a pas de limite au nombre de tâches inachevées. Si vous ne le souhaitez pas, désactivez cette option.
max-download-result.name=Nombre maximum de téléchargements terminés
max-download-result.description=Définit le nombre maximum de résultats de téléchargement conservés en mémoire. Les résultats des téléchargements sont les téléchargements terminés, les erreurs et les téléchargements supprimés. Les résultats de téléchargement sont stockés dans une file d'attente FIFO qui peut contenir au maximum NUM résultats de téléchargement. Lorsque la file d'attente est pleine et qu'un nouveau résultat de téléchargement est créé, le résultat de téléchargement le plus ancien est supprimé de l'avant de la file d'attente et le nouveau est repoussé à l'arrière. La définition d'un nombre élevé dans cette option peut entraîner une forte consommation de mémoire après des milliers de téléchargements. La valeur 0 signifie qu'aucun résultat de téléchargement n'est conservé. Notez que les téléchargements inachevés sont conservés en mémoire quelle que soit la valeur de cette option. Voir l'option --keep-unfinished-download-result.
max-mmap-limit.name=Limite maximale de MMap
max-mmap-limit.description=Définissez le poids maximal du fichier pour activer le MMap (voir l'option --enable-mmap). Le poids du fichier est déterminé par la somme de tout les poids de fichiers dans une tâche de téléchargement. Par exemple, si un téléchargement contient 5 fichiers, le poids du fichier sera le poids total de ces fichiers. Si le poids du fichier dépasse le poids défini pour cette option, MMap sera désactivé.
max-resume-failure-tries.name=Nombre maximal de tentatives de reprise des téléchargements interrompus
max-resume-failure-tries.description=Lorsque l'option --always-resume est définie sur vrai, si aria2 détecte que N URIs ne supportent pas la reprise des téléchargements interrompus, le téléchargement du fichier commencera depuis le début. Si N est fixé à 0, le téléchargement du fichier ne commencera au début que si tous les URI ne prennent pas en charge la reprise des téléchargements interrompus. Voir l'option --always-resume.
min-tls-version.name=Version minimum de TLS
min-tls-version.description=Spécifie la version minimum de SSL/TLS activée.
log-level.name=Niveau de journalisation
log-level.description=
optimize-concurrent-downloads.name=Optimiser les téléchargements simultanés
optimize-concurrent-downloads.description=Optimise le nombre de téléchargements simultanés en fonction de la bande passante disponible. aria2 utilise la vitesse de téléchargement précédemment mesurée pour obtenir le nombre de téléchargements simultanés via la règle N=A + B Log10 (la vitesse est en Mbps). Les coefficients A et B peuvent être personnalisés en les séparant par deux points dans le paramètre. La valeur par défaut (A=5, B=25) permet d'utiliser 5 téléchargements simultanés sur un réseau de 1 Mbps et 50 sur un réseau de 100 Mbps. Le nombre de téléchargements simultanés est limité au maximum défini par le paramètre --max-concurrent-downloads.
piece-length.name=Poids des blocs de fichiers
piece-length.description=Définir le poids d'allocation pour les téléchargements HTTP/FTP. aria2 divise les fichiers en fonction de cette limite. Toutes les sections seront des multiples de cette longueur. Cette option n'est pas valable pour les téléchargements BitTorrent.
show-console-readout.name=Afficher la sortie de la console
show-console-readout.description=
summary-interval.name=Télécharger le résumé de l'intervalle de sortie
summary-interval.description=Définit l'intervalle de sortie du résumé de la progression du téléchargement (en secondes). La valeur 0 désactive la sortie.
max-overall-download-limit.name=Vitesse maximale de téléchargement global
max-overall-download-limit.description=Définit la vitesse maximale de téléchargement global (octets/seconde). 0 indique qu'il n'y a pas de restriction. La valeur peut être augmentée en unités K ou M (1K=1024, 1M=1024K).
max-download-limit.name=Vitesse de téléchargement maximale
max-download-limit.description=Définit la vitesse de téléchargement maximale pour chaque tâche (octets/seconde). 0 indique qu'il n'y a pas de restriction. Vous pouvez augmenter la valeur avec des unités K ou M (1K=1024, 1M=1024K).
no-conf.name=Désactiver le fichier de configuration
no-conf.description=
no-file-allocation-limit.name=Pas de limite d'allocation de fichiers
no-file-allocation-limit.description=Aucune allocation de fichier n'est effectuée pour les fichiers dont le poids est inférieur à POIDS. Vous pouvez ajouter K ou M (1K = 1024, 1M = 1024K).
parameterized-uri.name=Activer la prise en charge des URI paramétrés
parameterized-uri.description=Active la prise en charge des URI paramétrés. Vous pouvez spécifier un ensemble de parties : http://{sv1,sv2,sv3}/foo.iso. Il est également possible d'utiliser un compteur de pas pour spécifier des séquences numérotées : http://host/image[000-100:2].img. Le compteur d'étapes est facultatif. Si tous les URI ne pointent pas vers le même fichier, comme dans le deuxième exemple ci-dessus, l'option -Z doit être utilisée.
quiet.name=Désactiver la sortie de la console
quiet.description=
realtime-chunk-checksum.name=Contrôle de l'intégrité des données en temps réel
realtime-chunk-checksum.description=Si la somme de contrôle des blocs de données est fournie, vérifiez les blocs de données avec la somme de contrôle pendant le téléchargement.
remove-control-file.name=Supprimer le fichier de contrôle
remove-control-file.description=Supprime le fichier de contrôle avant le téléchargement. En combinaison avec l'option --allow-overwrite=true, le téléchargement du fichier commencera toujours au début. Cette option peut s'avérer utile pour les utilisateurs de proxys qui ne permettent pas de reprendre un téléchargement interrompu.
save-session.name=Fichier de sauvegarde de la session
save-session.description=Il enregistre les téléchargements avec des erreurs et les téléchargements non terminés dans un fichier spécifié à la sortie. Vous pouvez les recharger en utilisant l'option --input-file lorsque vous redémarrez aria2. Si vous souhaitez compresser la sortie avec GZip, vous pouvez ajouter l'extension .gz au nom du fichier. Notez que les métadonnées des téléchargements ajoutés via les méthodes RPC aria2.addTorrent() et aria2.addMetalink() ne seront pas sauvegardées si elles ne sont pas sauvegardées dans le fichier. Les téléchargements supprimés avec aria2.remove() et aria2.forceRemove() ne seront pas sauvegardés.
save-session-interval.name=Intervalle de sauvegarde de la session
save-session-interval.description=Sauvegarde les téléchargements échoués ou non terminés dans le fichier spécifié par l'option --save-session toutes les quelques secondes. Si cette option vaut 0, la session ne sera sauvegardée que lorsque aria2 quittera le système.
socket-recv-buffer-size.name=Poids du tampon de réception du socket
socket-recv-buffer-size.description=Définit le poids maximum du tampon de réception du socket en octets. Si il vaut 0, cette option est désactivée. La valeur de cette option est définie dans le descripteur de fichier de la socket lors de l'appel à setsockopt() avec l'option SO_RCVBUF.
stop.name=Temps d'arrêt automatique
stop.description=Arrête l'application au bout de SEC secondes. Si la valeur 0 est donnée, cette fonction est désactivée.
truncate-console-readout.name=Raccourcit la sortie de la console
truncate-console-readout.description=Raccourcit la sortie de la console à une seule ligne.
