[global]
AriaNg Version=Versión de AriaNg
Operation Result=Resultado de la operación
Operation Succeeded=Operación exitosa
is connected=está conectado
Error=Error
OK=OK
Confirm=Confirmar
Cancel=Cancelar
Close=Cerrar
True=Verdadero
False=Falso
DEBUG=Depurar
INFO=Info
WARN=Advertir
ERROR=Error
Connecting=Conectando
Connected=Conectado
Disconnected=Desconectado
Reconnecting=Desconectando
Waiting to reconnect=Esperando para reconectar
Global=Global
New=Nuevo
Start=Iniciar
Pause=Pausa
Retry=Reintentar
Retry Selected Tasks=Reintentar tareas seleccionadas
Delete=Eliminar
Select All=Seleccionar todo
Select None=Seleccionar Ninguno
Select Invert=Seleccionar Invertir
Select All Failed Tasks=Seleccionar todas las tareas fallidas
Select All Completed Tasks=Seleccionar todas las tareas completadas
Select All Tasks=Seleccionar todas las tareas
Display Order=Orden de visualización
Copy Download Url=Copiar URL de descarga
Copy Magnet Link=Copiar enlace magnético
Help=Ayuda
Search=Buscar
Default=PRedeterminado
Expand=Expandir
Collapse=Colapsar
Expand All=Expandir todo
Collapse All=Colapsar todo
Open=Abir
Save=Guardar
Import=Importar
Remove Task=Eliminar tarea
Remove Selected Task=Eliminar tarea seleccionada
Clear Stopped Tasks=Limpiar tareas detenidas
Click to view task detail=Haga clic para ver los detalles de la tarea
By File Name=Por nombre de archivo
By File Size=Por tamaño de archivo
By Progress=Por progreso
By Selected Status=Por estado seleccionado
By Remaining=Por restante
By Download Speed=Por velocidad de descarga
By Upload Speed=Por velocidad de carga
By Peer Address=Por dirección de pares
By Client Name=Por nombre del cliente
Filters=Filtros
Download=Descargar
Upload=Cargar
Downloading=Descargando
Pending Verification=Pendiente de verificación
Verifying=Verificando
Seeding=Sembrando
Waiting=Esperando
Paused=Pausado
Completed=Completado
Error Occurred=Ocurrió un error
Removed=Eliminado
Finished / Stopped=Finalizado / Detenido
Uncompleted=Incompleto
Click to pin=Haga clic para fijar
Settings=Ajustes
AriaNg Settings=Ajustes de AriaNg
Aria2 Settings=Ajustes de Aria2
Basic Settings=Ajustes básicos
HTTP/FTP/SFTP Settings=Ajustes de HTTP/FTP/SFTP
HTTP Settings=Ajustes de HTTP
FTP/SFTP Settings=Ajustes de FTP/SFTP
BitTorrent Settings=Ajustes de BitTorrent
Metalink Settings=Ajustes de Metalink
RPC Settings=Ajustes de RPC
Advanced Settings=Ajustes avanzados
AriaNg Debug Console=Consola de depuración AriaNg
Aria2 Status=Estado de Aria2
File Name=Nombre del archivo
File Size=Tamaño del archivo
Progress=Progreso
Share Ratio=Proporción de compartición
Remaining=Restante
Download Speed=Velocidad de descarga
Upload Speed=Velocidad de carga
Links=Enlaces
Torrent File=Archivo torrent
Metalink File=Archivo metalink
File Name:=Nombre del archivo:
Options=Opciones
Overview=Descripción general
Pieces=Piezas
Files=Archivos
Peers=Pares
Task Name=Nombre de la tarea
Task Size=Tamaño de la tarea
Task Status=Estado de la tarea
Error Description=Descripción del error
Health Percentage=Porcentaje de salud
Info Hash=Hash de info
Seeders=Sembradores
Connections=Conexiones
Seed Creation Time=Tiempo de creación de semillas
Download Url=Descargar Url
Download Dir=Dir de descarga
BT Tracker Servers=Servidores de seguimiento BT
Copy=CoCopiarpy
(Choose Files)=(Elegir archivos)
Videos=Videos
Audios=Audios
Pictures=Imágenes
Documents=Documentos
Applications=Aplicaciones
Archives=Archivos
Other=Otro
Custom=Personalizado
Custom Choose File=Elegir archivo personalizado
Address=Dirección
Client=Cliente
Status=Estado
Speed=Velocidad
(local)=(local)
No Data=Sin datos
No connected peers=Sin pares conectados
Failed to change some tasks state.=No se pudo cambiar el estado de algunas tareas.
Confirm Retry=Confirmar reintento
Are you sure you want to retry the selected task? AriaNg will create same task after clicking OK.=¿Está seguro de querer volver a intentar la tarea seleccionada? AriaNg va a crear la misma tarea después de hacer clic en Aceptar.
Failed to retry this task.=No se pudo volver a intentar esta tarea.
{successCount} tasks have been retried and {failedCount} tasks are failed.=Se reintentó {{successCount}} tareas y se produjeron errores en {{failedCount}} tareas.
Confirm Remove=Confirmar Eliminar
Are you sure you want to remove the selected task?=¿Está seguro de querer eliminar la tarea seleccionada?
Failed to remove some task(s).=No se pudo eliminar alguna(s) tarea(s).
Confirm Clear=Confirmar Limpiar
Are you sure you want to clear stopped tasks?=¿Está seguro de querer eliminar las tareas detenidas?
Download Links:=Enlaces de descarga:
Download Now=Descargar ahora
Download Later=Descargar mas tarde
Open Torrent File=Abrir archivo torrent
Open Metalink File=Abrir archivo metalink
Support multiple URLs, one URL per line.=Admite múltiples URL, una URL por línea.
Your browser does not support loading file!=¡Tu navegador no soporta la carga de archivos!
The selected file type is invalid!=¡El tipo de archivo seleccionado no es válido!
Failed to load file!=¡Error al cargar el archivo!
Download Completed=Descarga completada
BT Download Completed=Descarga de BT completada
Download Error=Error de descarga
AriaNg Url=URL de AriaNg
Command API Url=URL de API de comando
Export Command API=API de comando de exportación
Export=Exportar
Copied=Copiado
Pause After Task Created=Pausa después de crear la tarea
Language=Idioma
Theme=Tema
Light=Claro
Dark=Oscuro
Follow system settings=Seguir la configuración del sistema
Debug Mode=Modo de depuración
Page Title=Título de la página
Preview=Vista previa
Tips: You can use the "noprefix" tag to ignore the prefix, "nosuffix" tag to ignore the suffix, and "scale\=n" tag to set the decimal precision.=Consejos: puede utilizar la etiqueta "noprefix" para ignorar el prefijo, la etiqueta "nosuffix" para ignorar el sufijo y la etiqueta "scale\=n" para establecer la precisión decimal.
Example: ${downspeed:noprefix:nosuffix:scale\=1}=Ejemplo: ${downspeed:noprefix:nosuffix:scale\=1}
Updating Page Title Interval=Intervalo de actualización del título de la página
Enable Browser Notification=Habilitar las notificaciones del navegador
Browser Notification Sound=Sonido de notificación del navegador
Browser Notification Frequency=Frecuencia de notificación del navegador
Unlimited=Ilimitado
High (Up to 10 Notifications / 1 Minute)=Alto (hasta 10 notificaciones/1 minuto)
Middle (Up to 1 Notification / 1 Minute)=Medio (hasta 1 notificación/1 minuto)
Low (Up to 1 Notification / 5 Minutes)=Bajo (hasta 1 notificación/5 minutos)
WebSocket Auto Reconnect Interval=Intervalo de reconexión automática de WebSocket
Aria2 RPC Alias=Alias RPC Aria2
Aria2 RPC Address=Dirección RPC de Aria2
Aria2 RPC Protocol=Protocolo RPC Aria2
Aria2 RPC Http Request Method=Método de solicitud HTTP RPC Aria2
POST method only supports aria2 v1.15.2 and above.=El método POST solo es compatible con aria2 v1.15.2 y superiores.
Aria2 RPC Request Headers=Encabezados de solicitud RPC de Aria2
Support multiple request headers, one header per line, each line containing "header name: header value".=Admite múltiples encabezados de solicitud, un encabezado por línea, cada línea conteniendo "nombre del encabezado: valor del encabezado".
Aria2 RPC Secret Token=Token secreto RPC de Aria2
Activate=Activar
Reset Settings=Restablecer configuración
Confirm Reset=Confirmar Restablecer
Are you sure you want to reset all settings?=¿Está seguro de querer restablecer todos los ajustes?
Clear Settings History=Limpiar historial de configuraciones
Are you sure you want to clear all settings history?=¿Está seguro de querer eliminar todo el historial de configuraciones?
Delete RPC Setting=Eliminar configuración RPC
Add New RPC Setting=Agregar nueva configuración RPC
Are you sure you want to remove rpc setting "{rpcName}"?=¿Está seguro de querer eliminar la configuración de rpc "{{rpcName}}"?
Updating Global Stat Interval=Actualización del intervalo de estadísticas globales
Updating Task Information Interval=Intervalo de actualización de información de tareas
Keyboard Shortcuts=Atajos de teclado
Supported Keyboard Shortcuts=Atajos de teclado compatibles
Set Focus On Search Box=Establecer el foco en el cuadro de búsqueda
Swipe Gesture=Gesto de deslizar
Change Tasks Order by Drag-and-drop=Cambiar el orden de las tareas mediante arrastrar y soltar
Action After Creating New Tasks=Acción después de crear nuevas tareas
Navigate to Task List Page=Ir a la página de lista de tareas
Navigate to Task Detail Page=Ir a la página de detalles de la tarea
Action After Retrying Task=Acción después de volver a intentar la tarea
Navigate to Downloading Tasks Page=Navegar a la página de tareas de descargas
Stay on Current Page=Mantener en la página actual
Remove Old Tasks After Retrying=Eliminar tareas antiguas después de volver a intentarlo
Confirm Task Removal=Confirmar la eliminación de la tarea
Include Prefix When Copying From Task Details=Incluir prefijo al copiar desde los detalles de la tarea
Show Pieces Info In Task Detail Page=Mostrar información de las piezas en la página de detalles de la tarea
Pieces Amount is Less than or Equal to {value}=La cantidad de piezas es menor o igual a {{value}}
RPC List Display Order=Orden de visualización de la lista RPC
Each Task List Page Uses Independent Display Order=Cada página de lista de tareas utiliza un orden de visualización independiente
Recently Used=Usado recientemente
RPC Alias=Alias RPC
Import / Export AriaNg Settings=Importar / Exportar configuraciones de AriaNg
Import Settings=Importar configuraciones
Export Settings=Exportar configuraciones
AriaNg settings data=Datos de configuración de AriaNg
Confirm Import=Confirmar Importar
Are you sure you want to import all settings?=¿Está seguro de querer importar todos los ajustes?
Invalid settings data format!=¡Formato de datos de configuración no válido!
Data has been copied to clipboard.=Los datos se copiaron al portapapeles. ###
Supported Placeholder=Marcador de posición compatible
AriaNg Title=Título de AriaNg
Current RPC Alias=Alias ​​de RPC actual
Downloading Count=Conteo de descargas
Waiting Count=Conteo de espera
Stopped Count=Conteo detenido
You have disabled notification in your browser. You should change your browser's settings before you enable this function.=Has deshabilitado las notificaciones en tu navegador. Debes cambiar la configuración de tu navegador antes de activar esta función.
Language resource has been updated, please reload the page for the changes to take effect.=El recurso de idioma se ha actualizado, vuelva a cargar la página para que los cambios surtan efecto.
Configuration has been modified, please reload the page for the changes to take effect.=Se ha modificado la configuración, vuelva a cargar la página para que los cambios surtan efecto.
Reload AriaNg=Recargar AriaNg
Show Secret=Mostrar secreto
Hide Secret=Ocultar secreto
Aria2 Version=Versión de Aria2
Enabled Features=Funciones habilitadas
Operations=Operaciones
Reconnect=Reconectar
Save Session=Guardar sesión
Shutdown Aria2=Apagar Aria2
Confirm Shutdown=Confirmar apagado
Are you sure you want to shutdown aria2?=¿Estás seguro de querer apagar aria2?
Session has been saved successfully.=La sesión se ha guardado correctamente.
Aria2 has been shutdown successfully.=Aria2 se ha apagado correctamente.
Toggle Navigation=Cambiar navegación
Shortcut=Atajo
Global Rate Limit=Límite de proporción global
Loading=Cargando...
More Than One Day=Más de 1 día
Unknown=Desconocido
Bytes=Bytes
Hours=Horas
Minutes=Minutos
Seconds=Segundos
Milliseconds=Milisegundos
Http=Http
Http (Disabled)=Http (Deshabilitado)
Https=Https
WebSocket=WebSocket
WebSocket (Disabled)=WebSocket (Deshabilitado)
WebSocket (Security)=WebSocket (Seguridad)
Http and WebSocket would be disabled when accessing AriaNg via Https.=Http y WebSocket se deshabilitarían al acceder a AriaNg a través de Https.
POST=POST
GET=GET
Enabled=Habilitado
Disabled=Deshabilitado
Always=Siempre
Never=Nunca
BitTorrent=BitTorrent
Changes to the settings take effect after refreshing page.=Los cambios en la configuración surten efecto después de actualizar la página.
Logging Time=Tiempo de registro
Log Level=Nivel de registro
Auto Refresh=Actualización automática
Refresh Now=Actualizar ahora
Clear Logs=Borrar registros
Are you sure you want to clear debug logs?=¿Estás seguro de querer borrar los registros de depuración?
Show Detail=Mostrar detalles
Log Detail=Detalle del registro
Aria2 RPC Debug=Depuración RPC de Aria2
Aria2 RPC Request Method=Método de solicitud RPC de Aria2
Aria2 RPC Request Parameters=Parámetros de solicitud RPC de Aria2
Aria2 RPC Response=Respuesta RPC de Aria2
Execute=Ejecutar
RPC method is illegal!=¡El método RPC es ilegal!
AriaNg does not support this RPC method!=¡AriaNg no admite este método RPC!
RPC request parameters are invalid!=¡Los parámetros de solicitud RPC no son válidos!
Type is illegal!=¡El tipo es ilegal!
Parameter is invalid!=¡El parámetro no es válido!
Option value cannot be empty!=¡El valor de la opción no puede estar vacío!
Input number is invalid!=¡El número de entrada no es válido!
Input number is below min value!=¡El número de entrada es menor que el valor mínimo {{value}}!
Input number is above max value!=¡El número de entrada es mayor que el valor máximo {{value}}!
Input value is invalid!=¡El valor de entrada no es válido!
Protocol is invalid!=¡El protocolo no es válido!
RPC host cannot be empty!=¡El host RPC no puede estar vacío!
RPC secret is not base64 encoded!=¡El secreto RPC no está codificado en base64!
URL is not base64 encoded!=¡La URL no está codificada en base64!
Tap to configure and get started with AriaNg.=Toque para configurar y comenzar a utilizar AriaNg.
Cannot initialize WebSocket!=¡No se puede inicializar WebSocket!
Cannot connect to aria2!=¡No se puede conectar a aria2!
Access Denied!=¡Acceso denegado!
You cannot use AriaNg because this browser does not meet the minimum requirements for data storage.=No puedes utilizar AriaNg porque este navegador no cumple con los requisitos mínimos de almacenamiento de datos.

[error]
unknown=Se produjo un error desconocido.
operation.timeout=Se agotó el tiempo de operación.
resource.notfound=No se encontró el recurso.
resource.notfound.max-file-not-found=No se encontró el recurso. Consulte la opción --max-file-not-found.
download.aborted.lowest-speed-limit=La descarga se interrumpió porque la velocidad de descarga era demasiado lenta. Consulte la opción --lowest-speed-limit.
network.problem=Se produjo un problema de red.
resume.notsupported=El servidor remoto no admite la reanudación.
space.notenough=No había suficiente espacio disponible en el disco.
piece.length.different=La longitud de la pieza era diferente a la del archivo de control .aria2. Consulte la opción --allow-piece-length-change.
download.sametime=aria2 estaba descargando el mismo archivo en ese momento.
download.torrent.sametime=aria2 estaba descargando el mismo archivo en ese momento.
file.exists=El archivo ya existía. Consulte la opción --allow-overwrite.
file.rename.failed=No se pudo cambiar el nombre del archivo. Consulte la opción --auto-file-renaming.
file.open.failed=No se pudo abrir el archivo existente.
file.create.failed=No se pudo crear un nuevo archivo ni truncar un archivo existente.
io.error=Se produjo un error en el sistema de archivos.
directory.create.failed=Error al crear el directorio.
name.resolution.failed=No se pudo resolver el nombre de dominio.
metalink.file.parse.failed=No se pudo analizar el documento Metalink.
ftp.command.failed=El comando FTP falló.
http.response.header.bad=El encabezado de respuesta HTTP era incorrecto o inesperado.
redirects.toomany=Se produjeron demasiadas redirecciones.
http.authorization.failed=La autorización HTTP falló.
bencoded.file.parse.failed=No se pudo analizar el archivo bencoded (normalmente el archivo ".torrent").
torrent.file.corrupted=El archivo ".torrent" estaba dañado o faltaba información que aria2 necesitaba.
magnet.uri.bad=Magnet URI estaba mal.
option.bad=Se proporcionó una opción incorrecta o no reconocida o se proporcionó un argumento de opción inesperado.
server.overload=El servidor remoto no pudo manejar la solicitud debido a una sobrecarga temporal o mantenimiento.
rpc.request.parse.failed=No se pudo analizar la solicitud JSON-RPC.
checksum.failed=La validación de la suma de comprobación falló.

[languages]
Czech=Checo
German=Alemán
English=Inglés
Spanish=Español
French=Francés
Italian=Italiano
Polish=Polaco
Russian=Ruso
Simplified Chinese=Chino Simplificado
Traditional Chinese=Chino Tradicional

[format]
longdate=MM/DD/YYYY HH:mm:ss
time.millisecond={{value}} milisegundo
time.milliseconds={{value}} milisegundos
time.second={{value}} segundo
time.seconds={{value}} segundos
time.minute={{value}} minuto
time.minutes={{value}} minutos
time.hour={{value}} hora
time.hours={{value}} horas
requires.aria2-version=Requiere aria2 v{{version}} o superior
task.new.download-links=Enlaces de descarga ({{count}} Links):
task.pieceinfo=Completado: {{completed}}, Total: {{total}}
task.error-occurred=Se produjo un error ({{errorcode}})
task.verifying-percent=Verificando ({{verifiedPercent}}%)
settings.file-count=({{count}} archivos)
settings.total-count=(Recuento total: {{count}})
debug.latest-logs=Latest {{count}} registros

[rpc.error]
unauthorized=¡Autorización fallida!

[option]
true=Verdadero
false=Falso
default=Predeterminado
none=Ninguno
hide=Ocultar
full=Completo
http=Http
https=Https
ftp=Ftp
mem=Sólo memoria
get=OBTENER
tunnel=TÚNEL
plain=Plano
arc4=ARC4
binary=Binario
ascii=ASCII
debug=Depurar
info=Info
notice=Aviso
warn=Advertir
error=Error
adaptive=adaptado
epoll=epoll
falloc=falloc
feedback=comentario
geom=geom
inorder=enorden
kqueue=kqueue
poll=encuesta
port=puerto
prealloc=preasignar
random=aleatorio
select=seleccionar
trunc=truncamiento
SSLv3=SSLv3
TLSv1=TLSv1
TLSv1.1=TLSv1.1
TLSv1.2=TLSv1.2

[options]
dir.name=Ruta de descarga
dir.description=
log.name=Archivo de registro
log.description=El nombre del archivo de registro. Si se especifica -, el registro se escribe en la salida estándar. Si se especifica una cadena vacía ("") o se omite esta opción, no se escribe ningún registro en el disco.
max-concurrent-downloads.name=Descargas simultáneas máximas
max-concurrent-downloads.description=
check-integrity.name=Comprobar integridad
check-integrity.description=Compruebar la integridad del archivo mediante la validación de hashes parciales o de un hash del archivo completo. Esta opción solo tiene efecto en descargas de BitTorrent y Metalink con sumas de comprobación o descargas HTTP(S)/FTP con la opción --checksum.
continue.name=Continuar descarga
continue.description=Continuar descargando un archivo parcialmente descargado. Utilice esta opción para reanudar una descarga iniciada por un navegador web u otro programa que descargue archivos secuencialmente desde el principio. Actualmente, esta opción solo se aplica a descargas HTTP(S)/FTP.
all-proxy.name=Servidor proxy
all-proxy.description=Utilizar un servidor proxy para todos los protocolos. También puede anular esta configuración y especificar un servidor proxy para un protocolo en particular mediante --http-proxy, --https-proxy y --ftp-proxy. Esto afecta a todas las descargas. El formato de PROXY es [http://][USER:PASSWORD@]HOST[:PORT].
all-proxy-user.name=Nombre de usuario proxy
all-proxy-user.description=
all-proxy-passwd.name=Contraseña de proxy
all-proxy-passwd.description=
checksum.name=Suma de comprobación
checksum.description=Establecer la suma de comprobación. El formato del valor de la opción es TYPE\=DIGEST. TYPE es el tipo de hash. El tipo de hash admitido se incluye en Algoritmos de hash en aria2c -v. DIGEST es el resumen hexadecimal. Por ejemplo, la configuración del resumen sha-1 se ve así: sha-1=0192ba11326fe2298c8cb4de616f4d4140213838 Esta opción se aplica solo a descargas HTTP(S)/FTP.
connect-timeout.name=Tiempo de espera de conexión
connect-timeout.description=Ingresar el tiempo de espera de conexión en segundos para establecer la conexión con el servidor HTTP/FTP/proxy. Una vez establecida la conexión, esta opción no tiene efecto y se utiliza la opción --timeout en su lugar.
dry-run.name=Ejecución en seco
dry-run.description=Si se especifica Verdadero, aria2 solo verifica si el archivo remoto está disponible y no descarga los datos. Esta opción tiene efecto en las descargas HTTP/FTP. Las descargas de BitTorrent se cancelan si se especifica Verdadero.
lowest-speed-limit.name=Límite de velocidad mínimo
lowest-speed-limit.description=Cerrar la conexión si la velocidad de descarga es inferior o igual a este valor (bytes por segundo). 0 significa que aria2 no tiene un límite de velocidad mínimo. Puedes añadir K o M (1K = 1024, 1M = 1024K). Esta opción no afecta a las descargas de BitTorrent.
max-connection-per-server.name=Máximo de conexiones por servidor
max-connection-per-server.description=
max-file-not-found.name=Máximo de intentos de archivo no encontrado
max-file-not-found.description=Si aria2 recibe el estado "archivo no encontrado" de los servidores HTTP/FTP remotos NUM veces sin obtener un solo byte, entonces fuerza la descarga a que falle. Especifique 0 para deshabilitar esta opción. Esta opción es efectiva solo cuando se utilizan servidores HTTP/FTP. La cantidad de reintentos se cuenta para --max-tries, por lo que también debe configurarse.
max-tries.name=Máximo número de intentos
max-tries.description=Establecer número de intentos. 0 significa ilimitado.
min-split-size.name=Tamaño mínimo de división
min-split-size.description=aria2 no divide un rango de bytes menor a 2*SIZE. Por ejemplo, consideremos la descarga de un archivo de 20 MiB. Si SIZE es 10 M, aria2 puede dividir el archivo en 2 rangos [0-10 MiB) y [10 MiB-20 MiB) y descargarlo usando 2 fuentes (si --split >= 2, por supuesto). Si SIZE es 15 M, ya que 2*15 M > 20 MiB, aria2 no divide el archivo y lo descarga usando 1 fuente. Puede agregar K o M (1 K = 1024, 1 M = 1024 K). Valores posibles: 1 M-1024 M.
netrc-path.name=.Ruta netrc
netrc-path.description=
no-netrc.name=Deshabilitar netrc
no-netrc.description=
no-proxy.name=Sin lista de proxys
no-proxy.description=Especifique una lista separada por comas de nombres de host, dominios y direcciones de red con o sin una máscara de subred donde no se debe utilizar ningún proxy.
out.name=Nombre del archivo
out.description=El nombre del archivo descargado. Siempre es relativo al directorio indicado en la opción --dir. Cuando se utiliza la opción --force-sequential, esta opción se ignora.
proxy-method.name=Método proxy
proxy-method.description=Establezca el método que se utilizará en la solicitud de proxy. MÉTODO puede ser OBTENER o TÚNEL. Las descargas HTTPS siempre utilizan TÚNEL independientemente de esta opción.
remote-time.name=Marca de tiempo del archivo remoto
remote-time.description=Recupere la marca de tiempo del archivo remoto del servidor HTTP/FTP remoto y, si está disponible, aplíquela al archivo local.
reuse-uri.name=Reutilizar Uri
reuse-uri.description=Reutilice las URI ya utilizadas si no quedan URIs sin usar.
retry-wait.name=Espera de reintento
retry-wait.description=Establezca los segundos que se deben esperar entre reintentos. Cuando SEC > 0, aria2 volverá a intentar realizar descargas cuando el servidor HTTP devuelva una respuesta 503.
server-stat-of.name=Salida de estadísticas del servidor
server-stat-of.description=Especifique el nombre del archivo en el que se guardará el perfil de rendimiento de los servidores. Puede cargar los datos guardados mediante la opción --server-stat-if.
server-stat-timeout.name=Tiempo de espera de las estadísticas del servidor
server-stat-timeout.description=Especifique el tiempo de espera en segundos para invalidar el perfil de rendimiento de los servidores desde el último contacto con ellos.
split.name=Conteo dividido
split.description=Descargar un archivo usando N conexiones. Si se proporcionan más de N URIs, se utilizan primero N URIs y las URIs restantes se utilizan para la copia de seguridad. Si se proporcionan menos de N URIs, esas URIs se utilizan más de una vez para que se realicen N conexiones en total simultáneamente. La cantidad de conexiones al mismo host está restringida por la opción --max-connection-per-server.
stream-piece-selector.name=Algoritmo de selección de piezas
stream-piece-selector.description=Especifique el algoritmo de selección de piezas utilizado en la descarga HTTP/FTP. Pieza significa segmento de longitud fija que se descarga en paralelo en una descarga segmentada. Si se proporciona el valor predeterminado, aria2 selecciona la pieza de modo que reduzca el número de conexiones establecidas. Este es un comportamiento predeterminado razonable porque establecer una conexión es una operación costosa. Si se proporciona enorden, aria2 selecciona la pieza que tiene el índice mínimo. Index=0 significa el primero del archivo. Esto será útil para ver una película mientras se descarga. La opción --enable-http-pipelining puede ser útil para reducir la sobrecarga de reconexión. Tenga en cuenta que aria2 respeta la opción --min-split-size, por lo que será necesario especificar un valor razonable para la opción --min-split-size. Si se proporciona aleatorio, aria2 selecciona la pieza aleatoriamente. Al igual que enorden, se respeta la opción --min-split-size. Si se proporciona geom, al principio aria2 selecciona la pieza que tiene el índice mínimo como enorden, pero conserva espacio de la pieza seleccionada anteriormente de forma exponencial. Esto reducirá la cantidad de conexiones que se deben establecer y, al mismo tiempo, descargará primero la parte inicial del archivo. Esto será útil para ver películas mientras se descargan.
timeout.name=Tiempo de espera
timeout.description=
uri-selector.name=Algoritmo de selección de URI
uri-selector.description=Especifique el algoritmo de selección de URI. Los valores posibles son enorden, comentario y adaptado. Si se proporciona enorden, se prueba la URI en el orden en que aparece en la lista de URI. Si se proporciona comentario, aria2 utiliza la velocidad de descarga observada en las descargas anteriores y elige el servidor más rápido en la lista de URI. Esto también omite de manera efectiva los espejos inactivos. La velocidad de descarga observada es parte del perfil de rendimiento de los servidores mencionados en --server-stat-of y --server-stat-if. Si se proporciona adaptado, selecciona uno de los mejores espejos para la primera conexión y las reservadas. Para los complementarios, devuelve espejos que aún no se han probado y, si cada uno de ellos ya se ha probado, devuelve espejos que se deben probar nuevamente. De lo contrario, no selecciona más espejos. Al igual que comentario, utiliza un perfil de rendimiento de servidores.
check-certificate.name=Verificar certificado
check-certificate.description=
http-accept-gzip.name=Aceptar GZip
http-accept-gzip.description=Enviar Aceptar: desinflar, encabezado de solicitud gzip y respuesta inflada si el servidor remoto responde con Content-Encoding: gzip o Content-Encoding: desinflar.
http-auth-challenge.name=Desafío de autenticación
http-auth-challenge.description=Envía el encabezado de autorización HTTP solo cuando lo solicita el servidor. Si se establece como falso, el encabezado de autorización siempre se envía al servidor. Hay una excepción: si el nombre de usuario y la contraseña están integrados en la URI, el encabezado de autorización siempre se envía al servidor independientemente de esta opción.
http-no-cache.name=Sin caché
http-no-cache.description=Enviar Cache-Control: sin caché y Pragma: encabezado sin caché para evitar el almacenamiento en caché de contenido. Si se proporciona falso, estos encabezados no se envían y puede agregar el encabezado Cache-Control con la directiva que desee utilizando la opción --header.
http-user.name=Nombre de usuario predeterminado HTTP
http-user.description=
http-passwd.name=Contraseña predeterminada HTTP
http-passwd.description=
http-proxy.name=Servidor proxy HTTP
http-proxy.description=
http-proxy-user.name=Nombre de usuario del proxy HTTP
http-proxy-user.description=
http-proxy-passwd.name=Contraseña de proxy HTTP
http-proxy-passwd.description=
https-proxy.name=Servidor proxy HTTPS
https-proxy.description=
https-proxy-user.name=Nombre de usuario del proxy HTTPS
https-proxy-user.description=
https-proxy-passwd.name=Contraseña de proxy HTTPS
https-proxy-passwd.description=
referer.name=Referente
referer.description=Establezca un referente http (Referer). Esto afecta a todas las descargas http/https. Si se proporciona *, la URI de descarga también se utiliza como referente. Esto puede resultar útil cuando se utiliza junto con la opción --parameterized-uri.
enable-http-keep-alive.name=Habilitar conexión persistente
enable-http-keep-alive.description=Habilitar conexión persistente HTTP/1.1.
enable-http-pipelining.name=Habilitar canalización HTTP
enable-http-pipelining.description=Habilitar la canalización HTTP/1.1.
header.name=Encabezado personalizado
header.description=Añade HEADER al encabezado de la solicitud HTTP. Coloca un elemento por línea, cada elemento conteniendo "nombre del encabezado: valor del encabezado".
save-cookies.name=Ruta de las cookies
save-cookies.description=Guardar las cookies en un ARCHIVO en formato Mozilla/Firefox(1.x/2.x)/Netscape. Si el ARCHIVO ya existe, se sobrescribe. Las cookies de sesión también se guardan y sus valores de caducidad se tratan como 0.
use-head.name=Utilizar el método HEAD
use-head.description=Utilizar el método HEAD para la primera solicitud al servidor HTTP.
user-agent.name=Agente de usuario personalizado
user-agent.description=
ftp-user.name=Nombre de usuario predeterminado de FTP
ftp-user.description=
ftp-passwd.name=Contraseña predeterminada de FTP
ftp-passwd.description=Si el nombre de usuario está incrustado pero la contraseña no está en la URI, aria2 intenta resolver la contraseña usando .netrc. Si la contraseña se encuentra en .netrc, úsela como contraseña. Si no, use la contraseña especificada en esta opción.
ftp-pasv.name=Modo pasivo
ftp-pasv.description=Utilice el modo pasivo en FTP. Si se proporciona el valor falso, se utilizará el modo activo. Esta opción se ignora para la transferencia SFTP.
ftp-proxy.name=Servidor proxy FTP
ftp-proxy.description=
ftp-proxy-user.name=Nombre de usuario del proxy FTP
ftp-proxy-user.description=
ftp-proxy-passwd.name=Contraseña de proxy FTP
ftp-proxy-passwd.description=
ftp-type.name=Tipo de transferencia
ftp-type.description=
ftp-reuse-connection.name=Reutilizar conexión
ftp-reuse-connection.description=
ssh-host-key-md.name=Suma de comprobación de clave pública SSH
ssh-host-key-md.description=Establezca la suma de comprobación para la clave pública del host SSH. El formato del valor de la opción es TYPE=DIGEST. TIPO es el tipo de hash. El tipo de hash admitido es sha-1 o md5. DIGERIR es un resumen hexadecimal. Por ejemplo: sha-1=b030503d4de4539dc7885e6f0f5e256704edf4c3. Esta opción se puede utilizar para validar la clave pública del servidor cuando se utiliza SFTP. Si esta opción no está configurada, que es la predeterminada, no se realiza ninguna validación.
bt-detach-seed-only.name=Separar solo las semillas
bt-detach-seed-only.description=Excluir solo las descargas de semillas al contar las descargas activas simultáneas (ver la opción -j). Esto significa que si se proporciona -j3 y esta opción está activada y hay 3 descargas activas y una de ellas ingresa al modo de semilla, entonces se excluye del recuento de descargas activas (por lo tanto, se convierte en 2) y se inicia la siguiente descarga que espera en la cola. Pero tenga en cuenta que el elemento de semilla aún se reconoce como descarga activa en el método RPC.
bt-enable-hook-after-hash-check.name=Habilitar Hook después de la comprobación de hash
bt-enable-hook-after-hash-check.description=Permitir la invocación del comando hook después de la comprobación de hash (ver la opción -V) en la descarga de BitTorrent. De forma predeterminada, cuando la comprobación de hash tiene éxito, se ejecuta el comando dado por --on-bt-download-complete. Para deshabilitar esta acción, asigne false a esta opción.
bt-enable-lpd.name=Habilitar el descubrimiento de pares locales (LPD)
bt-enable-lpd.description=Habilitar Local Peer Discovery. Si se establece una bandera privada en un torrent, aria2 no utiliza esta función para esa descarga, incluso si se especifica el valor verdadero.
bt-exclude-tracker.name=Exclusión de rastreadores de BitTorrent
bt-exclude-tracker.description=Lista separada por comas de las URL de anuncio del rastreador de BitTorrent que se eliminarán. Puede utilizar el valor especial * que coincide con todas las URL y, por lo tanto, elimina todas las URL de anuncio. Al especificar * en la línea de comandos del shell, no olvide escaparlo o entrecomillarlo.
bt-external-ip.name=IP externa
bt-external-ip.description=Especifique la dirección IP externa que se utilizará en la descarga de BitTorrent y DHT. Puede enviarse al rastreador de BitTorrent. Para DHT, esta opción debe configurarse para informar que el nodo local está descargando un torrent en particular. Esto es fundamental para usar DHT en una red privada. Aunque esta función se denomina externa, puede aceptar cualquier tipo de dirección IP.
bt-force-encryption.name=Forzar cifrado
bt-force-encryption.description=Requiere cifrado de la carga útil del mensaje de BitTorrent con arc4. Esta es una abreviatura de --bt-require-crypto --bt-min-crypto-level=arc4. Esta opción no cambia el valor de esas opciones. Si se proporciona el valor verdadero, se deniega el protocolo de enlace de BitTorrent heredado y solo se utiliza el protocolo de enlace de Ofuscación y siempre se cifra la carga útil del mensaje.
bt-hash-check-seed.name=Comprobación de hash antes de la siembra
bt-hash-check-seed.description=Si se proporciona verdadero, después de la comprobación del hash con la opción --check-integrity y de que el archivo esté completo, se continúa con la carga del archivo. Si desea comprobar el archivo y descargarlo solo cuando esté dañado o incompleto, configure esta opción como falso. Esta opción solo tiene efecto en las descargas de BitTorrent.
bt-load-saved-metadata.name=Cargar archivo de metadatos guardado
bt-load-saved-metadata.description=Antes de obtener los metadatos del torrent desde DHT al descargar con un enlace magnético, primero intente leer el archivo guardado con la opción --bt-save-metadata. Si tiene éxito, omita la descarga de metadatos desde DHT.
bt-max-open-files.name=Máximo de archivos abiertos
bt-max-open-files.description=Especifique el número máximo de archivos a abrir en una descarga multiarchivo de BitTorrent/Metalink a nivel global.
bt-max-peers.name=Máximo de pares
bt-max-peers.description=Especifique el número máximo de pares por torrent. 0 significa ilimitado.
bt-metadata-only.name=Descargar solo metadatos
bt-metadata-only.description=Descargar solo metadatos. No se descargarán los archivos descritos en los metadatos. Esta opción solo tiene efecto cuando se utiliza la URI de BitTorrent Magnet.
bt-min-crypto-level.name=Nivel mínimo de criptografía
bt-min-crypto-level.description=Establezca el nivel mínimo del método de cifrado. Si un par proporciona varios métodos de cifrado, aria2 elige el más bajo que satisfaga el nivel indicado.
bt-prioritize-piece.name=Priorizar pieza
bt-prioritize-piece.description=Intenta descargar primero la primera y la última parte de cada archivo. Esto es útil para obtener una vista previa de los archivos. El argumento puede contener 2 palabras clave: head y tail. Para incluir ambas palabras clave, deben estar separadas por comas. Estas palabras clave pueden tomar un parámetro, TAMAÑO. Por ejemplo, si se especifica head=TAMAÑO, las partes en el rango de los primeros bytes TAMAÑO de cada archivo tienen mayor prioridad. tail=TAMAÑO significa el rango de los últimos bytes TAMAÑO de cada archivo. TAMAÑO puede incluir K o M (1K = 1024, 1M = 1024K).
bt-remove-unselected-file.name=Eliminar archivo no seleccionado
bt-remove-unselected-file.description=Elimina los archivos no seleccionados cuando se completa la descarga en BitTorrent. Para seleccionar archivos, utilice la opción --select-file. Si no se utiliza, se supone que todos los archivos están seleccionados. Utilice esta opción con cuidado porque eliminará archivos de su disco.
bt-require-crypto.name=Requiere cripto
bt-require-crypto.description=Si se proporciona el valor verdadero, aria2 no acepta ni establece conexión con el protocolo de enlace de BitTorrent heredado (protocolo \19BitTorrent). Por lo tanto, aria2 siempre utiliza el protocolo de enlace de Ofuscación.
bt-request-peer-speed-limit.name=Velocidad de descarga preferida
bt-request-peer-speed-limit.description=Si la velocidad total de descarga de cada torrent es inferior a VELOCIDAD, aria2 aumenta temporalmente la cantidad de pares para intentar obtener una mayor velocidad de descarga. Configurar esta opción con la velocidad de descarga que prefiera puede aumentar la velocidad de descarga en algunos casos. Puede agregar K o M (1K = 1024, 1M = 1024K).
bt-save-metadata.name=Guardar metadatos
bt-save-metadata.description=Guardar los metadatos como archivo ".torrent". Esta opción solo tiene efecto cuando se utiliza la URI de BitTorrent Magnet. El nombre del archivo es un hash de información codificado en hexadecimal con el sufijo ".torrent". El directorio que se guardará es el mismo directorio en el que se guardó el archivo de descarga. Si ya existe el mismo archivo, los metadatos no se guardan.
bt-seed-unverified.name=No verificar archivos descargados
bt-seed-unverified.description=Semilla de archivos descargados previamente sin verificar los hashes de las piezas.
bt-stop-timeout.name=Detener tiempo de espera
bt-stop-timeout.description=Detener la descarga de BitTorrent si la velocidad de descarga es 0 en segundos SEC consecutivos. Si se indica 0, esta función se desactiva.
bt-tracker.name=Rastreadores de BitTorrent
bt-tracker.description=Lista separada por comas de URI de anuncios de rastreadores de BitTorrent adicionales. Estas URIs no se ven afectadas por la opción --bt-exclude-tracker porque se agregan después de que se eliminan las URIs incluidas en la opción --bt-exclude-tracker.
bt-tracker-connect-timeout.name=Tiempo de espera de conexión del rastreador BitTorrent
bt-tracker-connect-timeout.description=Establezca el tiempo de espera de conexión en segundos para establecer la conexión con el rastreador. Una vez establecida la conexión, esta opción no tiene efecto y se utiliza en su lugar la opción --bt-tracker-timeout.
bt-tracker-interval.name=Intervalo de conexión del rastreador BitTorrent
bt-tracker-interval.description=Establezca el intervalo en segundos entre las solicitudes del rastreador. Esto anula por completo el valor del intervalo y aria2 solo usa este valor e ignora el intervalo mínimo y el valor del intervalo en la respuesta del rastreador. Si se establece en 0, aria2 determina el intervalo en función de la respuesta del rastreador y el progreso de la descarga.
bt-tracker-timeout.name=Tiempo de espera del rastreador de BitTorrent
bt-tracker-timeout.description=
dht-file-path.name=Archivo DHT (IPv4)
dht-file-path.description=Cambie el archivo de la tabla de enrutamiento DHT de IPv4 a RUTA.
dht-file-path6.name=Archivo DHT (IPv6)
dht-file-path6.description=Cambie el archivo de la tabla de enrutamiento DHT de IPv6 a RUTA.
dht-listen-port.name=Puerto de escucha DHT
dht-listen-port.description=Establezca el puerto de escucha UDP utilizado por DHT (IPv4, IPv6) y el rastreador UDP. Se pueden especificar varios puertos utilizando ",", por ejemplo: 6881,6885. También puede utilizar - para especificar un rango: 6881-6999. , y - se pueden utilizar juntos.
dht-message-timeout.name=Tiempo de espera del mensaje DHT
dht-message-timeout.description=
enable-dht.name=Habilitar DHT (IPv4)
enable-dht.description=Habilita la funcionalidad DHT de IPv4. También habilita la compatibilidad con el rastreador UDP. Si se establece una bandera privada en un torrent, aria2 no usa DHT para esa descarga incluso si se proporciona el valor verdadero.
enable-dht6.name=Habilitar DHT (IPv6)
enable-dht6.description=Habilite la funcionalidad DHT de IPv6. Si se establece una bandera privada en un torrent, aria2 no usa DHT para esa descarga, incluso si se especifica el valor verdadero. Use la opción --dht-listen-port para especificar el número de puerto en el que se escuchará.
enable-peer-exchange.name=Habilitar el intercambio entre pares
enable-peer-exchange.description=Habilitar la extensión de intercambio entre pares. Si se establece una bandera privada en un torrent, esta función se deshabilita para esa descarga incluso si se especifica el valor verdadero.
follow-torrent.name=Seguir Torrent
follow-torrent.description=Si se especifica verdadero o mem, cuando se descarga un archivo cuyo sufijo es .torrent o el tipo de contenido es application/x-bittorrent, aria2 lo analiza como un archivo torrent y descarga los archivos que se mencionan en él. Si se especifica mem, no se escribe un archivo torrent en el disco, sino que se guarda en la memoria. Si se especifica falso, el archivo .torrent se descarga en el disco, pero no se analiza como un torrent y no se descarga su contenido.
listen-port.name=Puerto de escucha
listen-port.description=Establezca el número de puerto TCP para las descargas de BitTorrent. Se pueden especificar varios puertos utilizando ",", por ejemplo: 6881,6885. También puede utilizar - para especificar un rango: 6881-6999. , y - se pueden utilizar juntos: 6881-6889,6999.
max-overall-upload-limit.name=Límite máximo de carga global
max-overall-upload-limit.description=Establezca la velocidad máxima de carga general en bytes/seg. 0 significa sin restricciones. Puede agregar K o M (1K = 1024, 1M = 1024K).
max-upload-limit.name=Límite máximo de carga
max-upload-limit.description=Establezca la velocidad máxima de carga de cada torrent en bytes por segundo. 0 significa sin restricciones. Puede agregar K o M (1K = 1024, 1M = 1024K).
peer-id-prefix.name=Prefijo de identificación de pares
peer-id-prefix.description=Especifique el prefijo del identificador de par. El identificador de par en BitTorrent tiene una longitud de 20 bytes. Si se especifican más de 20 bytes, solo se utilizan los primeros 20 bytes. Si se especifican menos de 20 bytes, se agregan datos de bytes aleatorios para que su longitud sea de 20 bytes.
peer-agent.name=Agente par
peer-agent.description=Especifique la cadena utilizada durante el protocolo de enlace extendido de bitorrent para la versión del cliente del par.
seed-ratio.name=Proporción mínima de compartición
seed-ratio.description=Especifique la proporción de compartición. Sembrar torrents completados hasta que la proporción de compartición alcance PROPORCIÓN. Se recomienda encarecidamente especificar aquí un valor igual o superior a 1,0. Especifique 0,0 si pretende realizar la siembra independientemente de la proporción de compartición. Si se especifica la opción --seed-time junto con esta opción, la siembra finaliza cuando se cumple al menos una de las condiciones.
seed-time.name=Tiempo mínimo de siembra
seed-time.description=Especifique el tiempo de siembra en minutos (fraccionales). Si especifica --seed-time=0, se deshabilita la siembra una vez finalizada la descarga.
follow-metalink.name=Seguir Metalink
follow-metalink.description=Si se especifica verdadero o mem, cuando se descarga un archivo cuyo sufijo es .meta4 o .metalink o el tipo de contenido application/metalink4+xml o application/metalink+xml, aria2 lo analiza como un archivo metalink y descarga los archivos que se mencionan en él. Si se especifica mem, no se escribe un archivo metalink en el disco, sino que se guarda en la memoria. Si se especifica falso, el archivo .metalink se descarga en el disco, pero no se analiza como un archivo metalink y no se descarga su contenido.
metalink-base-uri.name=URI base
metalink-base-uri.description=Especifique la URI base para resolver la URI relativa en los elementos metalink:url y metalink:metaurl de un archivo metalink almacenado en el disco local. Si la URI apunta a un directorio, debe terminar con /.
metalink-language.name=Idioma
metalink-language.description=
metalink-location.name=Ubicación preferida del servidor
metalink-location.description=La ubicación del servidor preferida. Se acepta una lista de ubicaciones delimitada por comas, por ejemplo, jp, us.
metalink-os.name=Sistema operativo
metalink-os.description=El sistema operativo del archivo a descargar.
metalink-version.name=Versión
metalink-version.description=La versión del archivo a descargar.
metalink-preferred-protocol.name=Protocolo preferido
metalink-preferred-protocol.description=Especifique el protocolo preferido. Los valores posibles son http, https, ftp y ninguno. Especifique ninguno para deshabilitar esta función.
metalink-enable-unique-protocol.name=Habilitar protocolo único
metalink-enable-unique-protocol.description=Si se proporciona el valor verdadero y hay varios protocolos disponibles para un espejo en un archivo metalink, aria2 utiliza uno de ellos. Utilice la opción --metalink-preferred-protocol para especificar la preferencia del protocolo.
enable-rpc.name=Habilitar el servidor JSON-RPC/XML-RPC
enable-rpc.description=
pause-metadata.name=Pausar después de descargar los metadatos
pause-metadata.description=Pausar las descargas creadas como resultado de la descarga de metadatos. Hay 3 tipos de descargas de metadatos en aria2: (1) descarga de archivo .torrent. (2) descarga de metadatos de torrent mediante un enlace magnet. (3) descarga de archivo metalink. Estas descargas de metadatos generarán descargas utilizando sus metadatos. Esta opción pausa estas descargas posteriores. Esta opción es efectiva solo cuando se proporciona --enable-rpc=true.
rpc-allow-origin-all.name=Permitir todas las solicitudes de origen
rpc-allow-origin-all.description=Agregar el campo de encabezado Access-Control-Allow-Origin con el valor * a la respuesta RPC.
rpc-listen-all.name=Escuchar en todas las interfaces de red
rpc-listen-all.description=Escuchar las solicitudes JSON-RPC/XML-RPC entrantes en todas las interfaces de red. Si se proporciona el valor falso, escuchar solo en la interfaz de bucle local.
rpc-listen-port.name=Puerto de escucha
rpc-listen-port.description=
rpc-max-request-size.name=Tamaño máximo de solicitud
rpc-max-request-size.description=Establezca el tamaño máximo de la solicitud JSON-RPC/XML-RPC. Si aria2 detecta que la solicitud tiene más de TAMAÑO bytes, cancela la conexión.
rpc-save-upload-metadata.name=Guardar metadatos de carga
rpc-save-upload-metadata.description=Guardar los metadatos de torrent o metalink cargados en el directorio especificado por la opción --dir. El nombre del archivo consta de una cadena hexadecimal de metadatos con hash SHA-1 más la extensión. Para torrent, la extensión es '.torrent'. Para metalink, es '.meta4'. Si se asigna el valor falso a esta opción, las descargas agregadas por aria2.addTorrent() o aria2.addMetalink() no se guardarán con la opción --save-session.
rpc-secure.name=Habilitar SSL/TLS
rpc-secure.description=El transporte RPC se cifrará mediante SSL/TLS. Los clientes RPC deben utilizar el esquema https para acceder al servidor. Para el cliente WebSocket, utilice el esquema wss. Utilice las opciones --rpc-certificate y --rpc-private-key para especificar el certificado del servidor y la clave privada.
allow-overwrite.name=Permitir sobrescritura
allow-overwrite.description=Reiniciar la descarga desde cero si no existe el archivo de control correspondiente. Ver también la opción --auto-file-renaming.
allow-piece-length-change.name=Permitir cambio de longitud de pieza
allow-piece-length-change.description=Si se proporciona el valor falso, aria2 cancela la descarga cuando la longitud de un fragmento es diferente a la de un archivo de control. Si se proporciona el valor verdadero, puede continuar, pero se perderá parte del progreso de la descarga.
always-resume.name=Siempre reanudar descarga
always-resume.description=Reanudar siempre la descarga. Si se proporciona verdadero, aria2 siempre intenta reanudar la descarga y, si no es posible, cancela la descarga. Si se proporciona falso, cuando ninguna de las URI proporcionadas admite la reanudación o aria2 encuentra N URI que no admiten la reanudación (N es el valor especificado con la opción --max-resume-failure-tries), aria2 descarga el archivo desde cero. Consulte la opción --max-resume-failure-tries.
async-dns.name=DNS asincrónico
async-dns.description=
auto-file-renaming.name=Cambio automático de nombre de archivo
auto-file-renaming.description=Cambiar el nombre del archivo si ya existe. Esta opción funciona solo en descargas HTTP(S)/FTP. El nuevo nombre del archivo tiene un punto y un número (1..9999) adjuntos después del nombre, pero antes de la extensión del archivo, si la hay.
auto-save-interval.name=Intervalo de guardado automático
auto-save-interval.description=Guardar un archivo de control (*.aria2) cada SEC segundos. Si se proporciona 0, no se guarda ningún archivo de control durante la descarga. aria2 guarda un archivo de control cuando se detiene, independientemente del valor. Los valores posibles están entre 0 y 600.
conditional-get.name=Descarga condicional
conditional-get.description=Descargar archivo solo cuando el archivo local sea más antiguo que el archivo remoto. Esta función solo funciona con descargas HTTP(S). No funciona si el tamaño del archivo está especificado en Metalink. También ignora el encabezado Content-Disposition. Si existe un archivo de control, se ignorará esta opción. Esta función usa el encabezado If-Modified-Since para obtener solo el archivo más nuevo de manera condicional. Al obtener la hora de modificación del archivo local, usa el nombre de archivo proporcionado por el usuario (consulte la opción --out) o la parte del nombre del archivo en la URI si no se especifica --out. Para sobrescribir el archivo existente, se requiere --allow-overwrite.
conf-path.name=Archivo de configuración
conf-path.description=
console-log-level.name=Nivel de registro de la consola
console-log-level.description=
content-disposition-default-utf8.name=Utilice UTF-8 para gestionar la disposición del contenido
content-disposition-default-utf8.description=Manejar la cadena citada en el encabezado Content-Disposition como UTF-8 en lugar de ISO-8859-1, por ejemplo, el parámetro de nombre de archivo, pero no el nombre de archivo de la versión extendida.
daemon.name=Habilitar demonio
daemon.description=
deferred-input.name=Carga diferida
deferred-input.description=Si se proporciona verdadero, aria2 no lee todos los URI y opciones del archivo especificado por la opción --input-file al inicio, sino que lee uno por uno cuando lo necesita más adelante. Esto puede reducir el uso de memoria si el archivo de entrada contiene muchos URI para descargar. Si se proporciona falso, aria2 lee todos los URI y opciones al inicio. La opción --deferred-input se deshabilitará cuando se use --save-session en conjunto.
disable-ipv6.name=Deshabilitar IPv6
disable-ipv6.description=
disk-cache.name=Caché de disco
disk-cache.description=Habilitar caché de disco. Si TAMAÑO es 0, la caché de disco está deshabilitada. Esta función almacena en caché los datos descargados en la memoria, que crece hasta TAMAÑO bytes como máximo. El almacenamiento en caché se crea para la instancia de aria2 y lo comparten todas las descargas. La única ventaja de la caché de disco es que reduce la E/S del disco porque los datos se escriben en unidades más grandes y se reordenan según el desplazamiento del archivo. Si se utiliza una comprobación de hash y los datos se almacenan en caché en la memoria, no necesitamos leerlos desde el disco. TAMAÑO puede incluir K o M (1K = 1024, 1M = 1024K).
download-result.name=Descargar resultado
download-result.description=Esta opción cambia la forma en que se formatea el resultado de la descarga. Si OPT es la opción predeterminada, se imprime el GID, el estado, la velocidad de descarga promedio y la ruta/URI. Si hay varios archivos involucrados, se imprime la ruta/URI del primer archivo solicitado y se omiten los restantes. Si OPT está lleno, se imprime el GID, el estado, la velocidad de descarga promedio, el porcentaje de progreso y la ruta/URI. El porcentaje de progreso y la ruta/URI se imprimen para cada archivo solicitado en cada fila. Si OPT está oculto, se ocultan los resultados de la descarga.
dscp.name=DSCP
dscp.description=Establezca el valor DSCP en los paquetes IP salientes del tráfico BitTorrent para QoS. Este parámetro establece solo los bits DSCP en el campo TOS de los paquetes IP, no el campo completo. Si toma valores de /usr/include/netinet/ip.h, divídalos por 4 (de lo contrario, los valores serían incorrectos, por ejemplo, su clase CS1 se convertiría en CS4). Si toma valores de uso común de RFC, documentación de proveedores de red, Wikipedia o cualquier otra fuente, úselos tal como están.
rlimit-nofile.name=Límite flexible de descriptores de archivos abiertos
rlimit-nofile.description=Establezca el límite flexible de descriptores de archivos abiertos. Esta apertura solo tendrá efecto cuando: a. El sistema lo admita (posix). b. El límite no exceda el límite estricto. c. El límite especificado sea mayor que el límite flexible actual. Esto es equivalente a configurar nofile mediante ulimit, excepto que nunca reducirá el límite. Esta opción solo está disponible en sistemas que admitan la API rlimit.
enable-color.name=Habilitar color en la terminal
enable-color.description=
enable-mmap.name=Habilitar MMap
enable-mmap.description=Asignar archivos a la memoria. Esta opción puede no funcionar si el espacio de archivo no está asignado previamente. Consulte --file-allocation.
event-poll.name=Método de sondeo de eventos
event-poll.description=Especifique el método para sondear eventos. Los valores posibles son epoll, kqueue, port, poll y select. Para cada epoll, kqueue, port y poll, está disponible si el sistema lo admite. epoll está disponible en Linux reciente. kqueue está disponible en varios sistemas *BSD, incluido Mac OS X. port está disponible en Open Solaris. El valor predeterminado puede variar según el sistema que utilice.
file-allocation.name=Método de asignación de archivos
file-allocation.description=Especifique el método de asignación de archivos. none no preasigna espacio de archivo. prealloc preasigna espacio de archivo antes de que comience la descarga. Esto puede tardar un tiempo dependiendo del tamaño del archivo. Si está utilizando sistemas de archivos más nuevos como ext4 (con soporte de extensiones), btrfs, xfs o NTFS (solo compilación MinGW), falloc es su mejor opción. Asigna archivos grandes (pocos GiB) casi instantáneamente. No use falloc con sistemas de archivos heredados como ext3 y FAT32 porque toma casi el mismo tiempo que prealloc y bloquea aria2 por completo hasta que finaliza la asignación. falloc puede no estar disponible si su sistema no tiene la función posix_fallocate(3). trunc usa la llamada al sistema ftruncate(2) o la contraparte específica de la plataforma para truncar un archivo a una longitud especificada. En descargas de torrent de múltiples archivos, los archivos adyacentes a los archivos especificados también se asignan si comparten la misma pieza.
force-save.name=Forzar guardado
force-save.description=Guardar la descarga con la opción --save-session incluso si la descarga se completa o se elimina. Esta opción también guarda el archivo de control en esas situaciones. Esto puede resultar útil para guardar la propagación de BitTorrent que se reconoce como estado completado.
save-not-found.name=Guardar archivo no encontrado
save-not-found.description=Guardar la descarga con la opción --save-session incluso si no se encontró el archivo en el servidor. Esta opción también guarda el archivo de control en esas situaciones.
hash-check-only.name=Solo comprobación de hash
hash-check-only.description=Si se da verdadero, después de la verificación de hash usando la opción --check-integrity, se cancela la descarga independientemente de que la descarga esté completa o no.
human-readable.name=Salida legible por humanos de la consola
human-readable.description=Imprimir tamaños y velocidades en formato legible para humanos (por ejemplo, 1,2 Ki, 3,4 Mi) en la lectura de la consola.
keep-unfinished-download-result.name=Mantener el resultado de la descarga sin terminar
keep-unfinished-download-result.description=Conservar los resultados de descargas no finalizadas incluso si al hacerlo se excede el valor --max-download-result. Esto resulta útil si todas las descargas no finalizadas deben guardarse en un archivo de sesión (consulte la opción --save-session). Tenga en cuenta que no existe un límite superior para la cantidad de resultados de descargas no finalizadas que se deben conservar. Si esto no es deseable, desactive esta opción.
max-download-result.name=Resultado de descarga máxima
max-download-result.description=Establezca el número máximo de resultados de descarga que se guardan en la memoria. Los resultados de la descarga son descargas completadas/con error/eliminadas. Los resultados de la descarga se almacenan en la cola FIFO y puede almacenar como máximo NUM resultados de descarga. Cuando la cola está llena y se crea un nuevo resultado de descarga, el resultado de descarga más antiguo se elimina del frente de la cola y el nuevo se coloca al final. Establecer un número grande en esta opción puede resultar en un alto consumo de memoria después de miles de descargas. Especificar 0 significa que no se guarda ningún resultado de descarga. Tenga en cuenta que las descargas sin terminar se mantienen en la memoria independientemente del valor de esta opción. Consulte la opción --keep-unfinished-download-result.
max-mmap-limit.name=Límite máximo de MMap
max-mmap-limit.description=Establezca el tamaño máximo de archivo para habilitar mmap (consulte la opción --enable-mmap). El tamaño de archivo se determina por la suma de todos los archivos incluidos en una descarga. Por ejemplo, si una descarga contiene 5 archivos, el tamaño del archivo será el tamaño total de esos archivos. Si el tamaño del archivo es estrictamente mayor que el tamaño especificado en esta opción, se deshabilitará mmap.
max-resume-failure-tries.name=Máximo número de intentos de error de reanudación
max-resume-failure-tries.description=Cuando se utiliza con --always-resume=false, aria2 descarga el archivo desde cero cuando detecta N cantidad de URI que no admiten la reanudación. Si N es 0, aria2 descarga el archivo desde cero cuando ninguna de las URI proporcionadas admite la reanudación. Consulte la opción --always-resume.
min-tls-version.name=Versión mínima de TLS
min-tls-version.description=Especifique la versión mínima de SSL/TLS para habilitar.
log-level.name=Nivel de registro
log-level.description=
optimize-concurrent-downloads.name=Optimizar descargas simultáneas
optimize-concurrent-downloads.description=Optimizar la cantidad de descargas simultáneas según el ancho de banda disponible. aria2 utiliza la velocidad de descarga observada en las descargas anteriores para adaptar la cantidad de descargas lanzadas en paralelo según la regla N = A + B Log10 (velocidad en Mbps). Los coeficientes A y B se pueden personalizar en los argumentos de la opción con A y B separados por dos puntos. Los valores predeterminados (A=5, B=25) llevan a usar típicamente 5 descargas paralelas en redes de 1Mbps y más de 50 en redes de 100Mbps. La cantidad de descargas paralelas permanece restringida por debajo del máximo definido por el parámetro --max-concurrent-downloads.
piece-length.name=Longitud de la pieza
piece-length.description=Establezca una longitud de fragmento para las descargas HTTP/FTP. Este es el límite cuando aria2 divide un archivo. Todas las divisiones se producen en múltiplos de esta longitud. Esta opción se ignorará en las descargas de BitTorrent. También se ignorará si el archivo Metalink contiene hashes de fragmentos.
show-console-readout.name=Mostrar salida de consola
show-console-readout.description=
summary-interval.name=Descargar resumen del intervalo de salida
summary-interval.description=Establezca el intervalo en segundos para mostrar el resumen del progreso de la descarga. Si se configura en 0, se suprime la visualización.
max-overall-download-limit.name=Límite máximo de descarga global
max-overall-download-limit.description=Establezca la velocidad máxima de descarga general en bytes/seg. 0 significa sin restricciones. Puede agregar K o M (1K = 1024, 1M = 1024K).
max-download-limit.name=Límite máximo de descarga
max-download-limit.description=Establezca la velocidad máxima de descarga por cada descarga en bytes por segundo. 0 significa sin restricciones. Puede agregar K o M (1K = 1024, 1M = 1024K).
no-conf.name=Deshabilitar archivo de configuración
no-conf.description=
no-file-allocation-limit.name=Sin límite de asignación de archivos
no-file-allocation-limit.description=No se realiza ninguna asignación de archivos para archivos cuyo tamaño sea menor que TAMAÑO. Puede agregar K o M (1K = 1024, 1M = 1024K).
parameterized-uri.name=Habilitar URI parametrizada
parameterized-uri.description=Habilitar la compatibilidad con URI parametrizada. Puede especificar un conjunto de partes: http://{sv1,sv2,sv3}/foo.iso. También puede especificar secuencias numéricas con contador de pasos: http://host/image[000-100:2].img. Se puede omitir un contador de pasos. Si todas las URI no apuntan al mismo archivo, como en el segundo ejemplo anterior, se requiere la opción -Z.
quiet.name=Deshabilitar la salida de la consola
quiet.description=
realtime-chunk-checksum.name=Validación de fragmentos de datos en tiempo real
realtime-chunk-checksum.description=Validar un fragmento de datos calculando la suma de comprobación al descargar un archivo si se proporcionan sumas de comprobación de fragmentos.
remove-control-file.name=Eliminar archivo de control
remove-control-file.description=Eliminar el archivo de control antes de la descarga. Si se utiliza con --allow-overwrite=true, la descarga siempre comienza desde cero. Esto será útil para los usuarios que estén detrás de un servidor proxy que deshabilita la reanudación.
save-session.name=Archivo de guardado de sesión
save-session.description=Guardar las descargas con errores o sin terminar en un ARCHIVO al salir. Puedes pasar este archivo de salida a aria2c con la opción --input-file al reiniciar. Si deseas que la salida esté comprimida en gzip, agrega una extensión .gz al nombre del archivo. Ten en cuenta que las descargas agregadas por los métodos RPC aria2.addTorrent() y aria2.addMetalink() y cuyos metadatos no se pudieron guardar como archivo no se guardan. Las descargas eliminadas con aria2.remove() y aria2.forceRemove() no se guardarán.
save-session-interval.name=Intervalo de guardar sesión
save-session-interval.description=Guardar las descargas con errores o sin finalizar en un archivo especificado con la opción --save-session cada SEC segundos. Si se especifica 0, el archivo se guardará solo cuando aria2 salga.
socket-recv-buffer-size.name=Tamaño del búfer de recepción del socket
socket-recv-buffer-size.description=Establezca el búfer de recepción de socket máximo en bytes. Si se especifica 0, se deshabilitará esta opción. Este valor se establecerá en el descriptor de archivo de socket mediante la opción de socket SO_RCVBUF con la llamada setsockopt().
stop.name=Tiempo de apagado automático
stop.description=Detener la aplicación después de que transcurran 10 segundos. Si se especifica 0, esta función se deshabilita.
truncate-console-readout.name=Truncar la salida de la consola
truncate-console-readout.description=Truncar la lectura de la consola para que quepa en una sola línea.
