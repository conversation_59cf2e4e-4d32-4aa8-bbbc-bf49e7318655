# 腾讯会议视频下载器

一个用于下载腾讯会议录制文件的Python工具，支持下载视频、音频、文档等文件。

## 功能特性

- 🎥 下载会议视频文件
- 🎵 下载会议音频文件  
- 📄 下载会议文档（Word、PDF、TXT）
- 📁 按会议时间自动创建目录
- 🔧 支持配置文件管理Cookie

## 快速开始

### 1. 配置Cookie

首次使用需要配置您的腾讯会议Cookie：

```bash
# 复制配置文件模板
cp config.example.json config.json
```

然后编辑 `config.json` 文件，将Cookie替换为您的实际Cookie。

### 2. 获取Cookie的方法

1. 打开浏览器，访问 https://meeting.tencent.com
2. 登录您的腾讯会议账号
3. 按F12打开开发者工具
4. 切换到Network(网络)标签页
5. 刷新页面或进行任意操作
6. 在请求列表中选择任意一个请求
7. 在Request Headers中找到Cookie字段
8. 复制Cookie的完整值
9. 粘贴到config.json的cookie字段中

### 3. 运行程序

```bash
python tencent_meeting_downloader.py
```

## 配置文件说明

`config.json` 文件格式：

```json
{
    "cookie": "您的腾讯会议Cookie",
    "description": "腾讯会议下载器配置文件",
    "note": "更新cookie时，请替换上面的cookie字段内容"
}
```

## 使用方法

### 方法1：使用配置文件（推荐）

```python
# 自动从config.json读取cookie
downloader = TencentMeetingDownloader()
```

### 方法2：直接传入Cookie

```python
# 直接传入cookie（不推荐，仅用于测试）
cookie = "您的Cookie字符串"
downloader = TencentMeetingDownloader(cookie=cookie)
```

### 下载指定会议

```python
# 下载指定开始时间的会议
target_start_time = "1748570479000"
downloader.download_meeting_files(target_start_time=target_start_time)
```

### 下载所有会议

```python
# 下载所有会议文件
downloader.download_meeting_files(download_all=True)
```

## 更新Cookie

当Cookie过期时，只需要：

1. 按照上述方法重新获取Cookie
2. 编辑 `config.json` 文件
3. 替换cookie字段的值
4. 重新运行程序

## 注意事项

- Cookie有时效性，过期后需要重新获取
- 请确保您有权限下载相关会议文件
- 下载的文件会按会议时间自动分类存储
- 建议定期备份重要的会议文件

## 文件结构

```
├── tencent_meeting_downloader.py  # 主程序文件
├── config.json                    # 配置文件（需要创建）
├── config.example.json           # 配置文件模板
└── README.md                     # 说明文档
```

## 故障排除

### 1. 配置文件不存在
```
❌ 配置文件 config.json 不存在
```
**解决方法**：复制 `config.example.json` 为 `config.json` 并配置Cookie

### 2. Cookie无效或过期
```
❌ 获取会议列表失败
```
**解决方法**：重新获取Cookie并更新配置文件

### 3. 网络连接问题
**解决方法**：检查网络连接，确保能正常访问腾讯会议网站
