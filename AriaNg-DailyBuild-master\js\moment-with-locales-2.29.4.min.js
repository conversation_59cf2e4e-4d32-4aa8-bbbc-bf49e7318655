!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.moment=t()}(this,function(){"use strict";var e;function g(){return e.apply(null,arguments)}function o(e){return e instanceof Array||"[object Array]"===Object.prototype.toString.call(e)}function u(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)}function w(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function l(e){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(e).length;for(var t in e)if(w(e,t))return;return 1}function a(e){return void 0===e}function d(e){return"number"==typeof e||"[object Number]"===Object.prototype.toString.call(e)}function h(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function c(e,t){for(var n=[],s=e.length,i=0;i<s;++i)n.push(t(e[i],i));return n}function f(e,t){for(var n in t)w(t,n)&&(e[n]=t[n]);return w(t,"toString")&&(e.toString=t.toString),w(t,"valueOf")&&(e.valueOf=t.valueOf),e}function m(e,t,n,s){return vt(e,t,n,s,!0).utc()}function p(e){return null==e._pf&&(e._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}),e._pf}function _(e){if(null==e._isValid){var t=p(e),n=s.call(t.parsedDateParts,function(e){return null!=e});n=!isNaN(e._d.getTime())&&t.overflow<0&&!t.empty&&!t.invalidEra&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&n);if(e._strict&&(n=n&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour),null!=Object.isFrozen&&Object.isFrozen(e))return n;e._isValid=n}return e._isValid}function y(e){var t=m(NaN);return null!=e?f(p(t),e):p(t).userInvalidated=!0,t}var s=Array.prototype.some||function(e){for(var t=Object(this),n=t.length>>>0,s=0;s<n;s++)if(s in t&&e.call(this,t[s],s,t))return!0;return!1},Y=g.momentProperties=[],t=!1;function M(e,t){var n,s,i,r=Y.length;if(a(t._isAMomentObject)||(e._isAMomentObject=t._isAMomentObject),a(t._i)||(e._i=t._i),a(t._f)||(e._f=t._f),a(t._l)||(e._l=t._l),a(t._strict)||(e._strict=t._strict),a(t._tzm)||(e._tzm=t._tzm),a(t._isUTC)||(e._isUTC=t._isUTC),a(t._offset)||(e._offset=t._offset),a(t._pf)||(e._pf=p(t)),a(t._locale)||(e._locale=t._locale),0<r)for(n=0;n<r;n++)a(i=t[s=Y[n]])||(e[s]=i);return e}function D(e){M(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===t&&(t=!0,g.updateOffset(this),t=!1)}function k(e){return e instanceof D||null!=e&&null!=e._isAMomentObject}function v(e){!1===g.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function n(r,a){var o=!0;return f(function(){if(null!=g.deprecationHandler&&g.deprecationHandler(null,r),o){for(var e,t,n=[],s=arguments.length,i=0;i<s;i++){if(e="","object"==typeof arguments[i]){for(t in e+="\n["+i+"] ",arguments[0])w(arguments[0],t)&&(e+=t+": "+arguments[0][t]+", ");e=e.slice(0,-2)}else e=arguments[i];n.push(e)}v(r+"\nArguments: "+Array.prototype.slice.call(n).join("")+"\n"+(new Error).stack),o=!1}return a.apply(this,arguments)},a)}var i={};function r(e,t){null!=g.deprecationHandler&&g.deprecationHandler(e,t),i[e]||(v(t),i[e]=!0)}function S(e){return"undefined"!=typeof Function&&e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}function O(e,t){var n,s=f({},e);for(n in t)w(t,n)&&(u(e[n])&&u(t[n])?(s[n]={},f(s[n],e[n]),f(s[n],t[n])):null!=t[n]?s[n]=t[n]:delete s[n]);for(n in e)w(e,n)&&!w(t,n)&&u(e[n])&&(s[n]=f({},s[n]));return s}function b(e){null!=e&&this.set(e)}g.suppressDeprecationWarnings=!1,g.deprecationHandler=null;var T=Object.keys||function(e){var t,n=[];for(t in e)w(e,t)&&n.push(t);return n};function x(e,t,n){var s=""+Math.abs(e);return(0<=e?n?"+":"":"-")+Math.pow(10,Math.max(0,t-s.length)).toString().substr(1)+s}var N=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,L=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,P={},W={};function R(e,t,n,s){var i="string"==typeof s?function(){return this[s]()}:s;e&&(W[e]=i),t&&(W[t[0]]=function(){return x(i.apply(this,arguments),t[1],t[2])}),n&&(W[n]=function(){return this.localeData().ordinal(i.apply(this,arguments),e)})}function H(e,t){return e.isValid()?(t=C(t,e.localeData()),P[t]=P[t]||function(s){for(var e,i=s.match(N),t=0,r=i.length;t<r;t++)W[i[t]]?i[t]=W[i[t]]:i[t]=(e=i[t]).match(/\[[\s\S]/)?e.replace(/^\[|\]$/g,""):e.replace(/\\/g,"");return function(e){for(var t="",n=0;n<r;n++)t+=S(i[n])?i[n].call(e,s):i[n];return t}}(t),P[t](e)):e.localeData().invalidDate()}function C(e,t){var n=5;function s(e){return t.longDateFormat(e)||e}for(L.lastIndex=0;0<=n&&L.test(e);)e=e.replace(L,s),L.lastIndex=0,--n;return e}var U={};function F(e,t){var n=e.toLowerCase();U[n]=U[n+"s"]=U[t]=e}function V(e){return"string"==typeof e?U[e]||U[e.toLowerCase()]:void 0}function E(e){var t,n,s={};for(n in e)w(e,n)&&(t=V(n))&&(s[t]=e[n]);return s}var G={};function A(e,t){G[e]=t}function j(e){return e%4==0&&e%100!=0||e%400==0}function I(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function z(e){var t=0;return 0!=(e=+e)&&isFinite(e)?I(e):t}function Z(t,n){return function(e){return null!=e?($(this,t,e),g.updateOffset(this,n),this):q(this,t)}}function q(e,t){return e.isValid()?e._d["get"+(e._isUTC?"UTC":"")+t]():NaN}function $(e,t,n){e.isValid()&&!isNaN(n)&&("FullYear"===t&&j(e.year())&&1===e.month()&&29===e.date()?(n=z(n),e._d["set"+(e._isUTC?"UTC":"")+t](n,e.month(),be(n,e.month()))):e._d["set"+(e._isUTC?"UTC":"")+t](n))}var B=/\d/,J=/\d\d/,Q=/\d{3}/,X=/\d{4}/,K=/[+-]?\d{6}/,ee=/\d\d?/,te=/\d\d\d\d?/,ne=/\d\d\d\d\d\d?/,se=/\d{1,3}/,ie=/\d{1,4}/,re=/[+-]?\d{1,6}/,ae=/\d+/,oe=/[+-]?\d+/,ue=/Z|[+-]\d\d:?\d\d/gi,le=/Z|[+-]\d\d(?::?\d\d)?/gi,de=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i;function he(e,n,s){fe[e]=S(n)?n:function(e,t){return e&&s?s:n}}function ce(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}var fe={},me={};function _e(e,n){var t,s,i=n;for("string"==typeof e&&(e=[e]),d(n)&&(i=function(e,t){t[n]=z(e)}),s=e.length,t=0;t<s;t++)me[e[t]]=i}function ye(e,i){_e(e,function(e,t,n,s){n._w=n._w||{},i(e,n._w,n,s)})}var ge,we=0,pe=1,Ye=2,Me=3,De=4,ke=5,ve=6,Se=7,Oe=8;function be(e,t){if(isNaN(e)||isNaN(t))return NaN;var n=(t%(n=12)+n)%n;return e+=(t-n)/12,1==n?j(e)?29:28:31-n%7%2}ge=Array.prototype.indexOf||function(e){for(var t=0;t<this.length;++t)if(this[t]===e)return t;return-1},R("M",["MM",2],"Mo",function(){return this.month()+1}),R("MMM",0,0,function(e){return this.localeData().monthsShort(this,e)}),R("MMMM",0,0,function(e){return this.localeData().months(this,e)}),F("month","M"),A("month",8),he("M",ee),he("MM",ee,J),he("MMM",function(e,t){return t.monthsShortRegex(e)}),he("MMMM",function(e,t){return t.monthsRegex(e)}),_e(["M","MM"],function(e,t){t[pe]=z(e)-1}),_e(["MMM","MMMM"],function(e,t,n,s){null!=(s=n._locale.monthsParse(e,s,n._strict))?t[pe]=s:p(n).invalidMonth=e});var Te="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),xe="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),Ne=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Le=de,Pe=de;function We(e,t){var n;if(e.isValid()){if("string"==typeof t)if(/^\d+$/.test(t))t=z(t);else if(!d(t=e.localeData().monthsParse(t)))return;n=Math.min(e.date(),be(e.year(),t)),e._d["set"+(e._isUTC?"UTC":"")+"Month"](t,n)}}function Re(e){return null!=e?(We(this,e),g.updateOffset(this,!0),this):q(this,"Month")}function He(){function e(e,t){return t.length-e.length}for(var t,n=[],s=[],i=[],r=0;r<12;r++)t=m([2e3,r]),n.push(this.monthsShort(t,"")),s.push(this.months(t,"")),i.push(this.months(t,"")),i.push(this.monthsShort(t,""));for(n.sort(e),s.sort(e),i.sort(e),r=0;r<12;r++)n[r]=ce(n[r]),s[r]=ce(s[r]);for(r=0;r<24;r++)i[r]=ce(i[r]);this._monthsRegex=new RegExp("^("+i.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+s.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+n.join("|")+")","i")}function Ce(e){return j(e)?366:365}R("Y",0,0,function(){var e=this.year();return e<=9999?x(e,4):"+"+e}),R(0,["YY",2],0,function(){return this.year()%100}),R(0,["YYYY",4],0,"year"),R(0,["YYYYY",5],0,"year"),R(0,["YYYYYY",6,!0],0,"year"),F("year","y"),A("year",1),he("Y",oe),he("YY",ee,J),he("YYYY",ie,X),he("YYYYY",re,K),he("YYYYYY",re,K),_e(["YYYYY","YYYYYY"],we),_e("YYYY",function(e,t){t[we]=2===e.length?g.parseTwoDigitYear(e):z(e)}),_e("YY",function(e,t){t[we]=g.parseTwoDigitYear(e)}),_e("Y",function(e,t){t[we]=parseInt(e,10)}),g.parseTwoDigitYear=function(e){return z(e)+(68<z(e)?1900:2e3)};var Ue=Z("FullYear",!0);function Fe(e){var t;return e<100&&0<=e?((t=Array.prototype.slice.call(arguments))[0]=e+400,t=new Date(Date.UTC.apply(null,t)),isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e)):t=new Date(Date.UTC.apply(null,arguments)),t}function Ve(e,t,n){return(n=7+t-n)-(7+Fe(e,0,n).getUTCDay()-t)%7-1}function Ee(e,t,n,s,i){var r;n=(t=1+7*(t-1)+(7+n-s)%7+Ve(e,s,i))<=0?Ce(r=e-1)+t:t>Ce(e)?(r=e+1,t-Ce(e)):(r=e,t);return{year:r,dayOfYear:n}}function Ge(e,t,n){var s,i,r=Ve(e.year(),t,n);return(r=Math.floor((e.dayOfYear()-r-1)/7)+1)<1?s=r+Ae(i=e.year()-1,t,n):r>Ae(e.year(),t,n)?(s=r-Ae(e.year(),t,n),i=e.year()+1):(i=e.year(),s=r),{week:s,year:i}}function Ae(e,t,n){var s=Ve(e,t,n);t=Ve(e+1,t,n);return(Ce(e)-s+t)/7}function je(e,t){return e.slice(t,7).concat(e.slice(0,t))}R("w",["ww",2],"wo","week"),R("W",["WW",2],"Wo","isoWeek"),F("week","w"),F("isoWeek","W"),A("week",5),A("isoWeek",5),he("w",ee),he("ww",ee,J),he("W",ee),he("WW",ee,J),ye(["w","ww","W","WW"],function(e,t,n,s){t[s.substr(0,1)]=z(e)}),R("d",0,"do","day"),R("dd",0,0,function(e){return this.localeData().weekdaysMin(this,e)}),R("ddd",0,0,function(e){return this.localeData().weekdaysShort(this,e)}),R("dddd",0,0,function(e){return this.localeData().weekdays(this,e)}),R("e",0,0,"weekday"),R("E",0,0,"isoWeekday"),F("day","d"),F("weekday","e"),F("isoWeekday","E"),A("day",11),A("weekday",11),A("isoWeekday",11),he("d",ee),he("e",ee),he("E",ee),he("dd",function(e,t){return t.weekdaysMinRegex(e)}),he("ddd",function(e,t){return t.weekdaysShortRegex(e)}),he("dddd",function(e,t){return t.weekdaysRegex(e)}),ye(["dd","ddd","dddd"],function(e,t,n,s){null!=(s=n._locale.weekdaysParse(e,s,n._strict))?t.d=s:p(n).invalidWeekday=e}),ye(["d","e","E"],function(e,t,n,s){t[s]=z(e)});var Ie="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),ze="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Ze="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),qe=de,$e=de,Be=de;function Je(){function e(e,t){return t.length-e.length}for(var t,n,s,i=[],r=[],a=[],o=[],u=0;u<7;u++)s=m([2e3,1]).day(u),t=ce(this.weekdaysMin(s,"")),n=ce(this.weekdaysShort(s,"")),s=ce(this.weekdays(s,"")),i.push(t),r.push(n),a.push(s),o.push(t),o.push(n),o.push(s);i.sort(e),r.sort(e),a.sort(e),o.sort(e),this._weekdaysRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+a.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+i.join("|")+")","i")}function Qe(){return this.hours()%12||12}function Xe(e,t){R(e,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)})}function Ke(e,t){return t._meridiemParse}R("H",["HH",2],0,"hour"),R("h",["hh",2],0,Qe),R("k",["kk",2],0,function(){return this.hours()||24}),R("hmm",0,0,function(){return""+Qe.apply(this)+x(this.minutes(),2)}),R("hmmss",0,0,function(){return""+Qe.apply(this)+x(this.minutes(),2)+x(this.seconds(),2)}),R("Hmm",0,0,function(){return""+this.hours()+x(this.minutes(),2)}),R("Hmmss",0,0,function(){return""+this.hours()+x(this.minutes(),2)+x(this.seconds(),2)}),Xe("a",!0),Xe("A",!1),F("hour","h"),A("hour",13),he("a",Ke),he("A",Ke),he("H",ee),he("h",ee),he("k",ee),he("HH",ee,J),he("hh",ee,J),he("kk",ee,J),he("hmm",te),he("hmmss",ne),he("Hmm",te),he("Hmmss",ne),_e(["H","HH"],Me),_e(["k","kk"],function(e,t,n){e=z(e),t[Me]=24===e?0:e}),_e(["a","A"],function(e,t,n){n._isPm=n._locale.isPM(e),n._meridiem=e}),_e(["h","hh"],function(e,t,n){t[Me]=z(e),p(n).bigHour=!0}),_e("hmm",function(e,t,n){var s=e.length-2;t[Me]=z(e.substr(0,s)),t[De]=z(e.substr(s)),p(n).bigHour=!0}),_e("hmmss",function(e,t,n){var s=e.length-4,i=e.length-2;t[Me]=z(e.substr(0,s)),t[De]=z(e.substr(s,2)),t[ke]=z(e.substr(i)),p(n).bigHour=!0}),_e("Hmm",function(e,t,n){var s=e.length-2;t[Me]=z(e.substr(0,s)),t[De]=z(e.substr(s))}),_e("Hmmss",function(e,t,n){var s=e.length-4,i=e.length-2;t[Me]=z(e.substr(0,s)),t[De]=z(e.substr(s,2)),t[ke]=z(e.substr(i))}),de=Z("Hours",!0);var et,tt={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:Te,monthsShort:xe,week:{dow:0,doy:6},weekdays:Ie,weekdaysMin:Ze,weekdaysShort:ze,meridiemParse:/[ap]\.?m?\.?/i},nt={},st={};function it(e){return e&&e.toLowerCase().replace("_","-")}function rt(e){var t;if(void 0===nt[e]&&"undefined"!=typeof module&&module&&module.exports&&null!=e.match("^[^/\\\\]*$"))try{t=et._abbr,require("./locale/"+e),at(t)}catch(t){nt[e]=null}return nt[e]}function at(e,t){return e&&((t=a(t)?ut(e):ot(e,t))?et=t:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+e+" not found. Did you forget to load it?")),et._abbr}function ot(e,t){if(null===t)return delete nt[e],null;var n,s=tt;if(t.abbr=e,null!=nt[e])r("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),s=nt[e]._config;else if(null!=t.parentLocale)if(null!=nt[t.parentLocale])s=nt[t.parentLocale]._config;else{if(null==(n=rt(t.parentLocale)))return st[t.parentLocale]||(st[t.parentLocale]=[]),st[t.parentLocale].push({name:e,config:t}),null;s=n._config}return nt[e]=new b(O(s,t)),st[e]&&st[e].forEach(function(e){ot(e.name,e.config)}),at(e),nt[e]}function ut(e){var t;if(!(e=e&&e._locale&&e._locale._abbr?e._locale._abbr:e))return et;if(!o(e)){if(t=rt(e))return t;e=[e]}return function(e){for(var t,n,s,i,r=0;r<e.length;){for(t=(i=it(e[r]).split("-")).length,n=(n=it(e[r+1]))?n.split("-"):null;0<t;){if(s=rt(i.slice(0,t).join("-")))return s;if(n&&n.length>=t&&function(e,t){for(var n=Math.min(e.length,t.length),s=0;s<n;s+=1)if(e[s]!==t[s])return s;return n}(i,n)>=t-1)break;t--}r++}return et}(e)}function lt(e){var t=e._a;return t&&-2===p(e).overflow&&(t=t[pe]<0||11<t[pe]?pe:t[Ye]<1||t[Ye]>be(t[we],t[pe])?Ye:t[Me]<0||24<t[Me]||24===t[Me]&&(0!==t[De]||0!==t[ke]||0!==t[ve])?Me:t[De]<0||59<t[De]?De:t[ke]<0||59<t[ke]?ke:t[ve]<0||999<t[ve]?ve:-1,p(e)._overflowDayOfYear&&(t<we||Ye<t)&&(t=Ye),p(e)._overflowWeeks&&-1===t&&(t=Se),p(e)._overflowWeekday&&-1===t&&(t=Oe),p(e).overflow=t),e}var dt=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ht=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ct=/Z|[+-]\d\d(?::?\d\d)?/,ft=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],mt=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],_t=/^\/?Date\((-?\d+)/i,yt=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,gt={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function wt(e){var t,n,s,i,r,a,o=e._i,u=dt.exec(o)||ht.exec(o),l=(o=ft.length,mt.length);if(u){for(p(e).iso=!0,t=0,n=o;t<n;t++)if(ft[t][1].exec(u[1])){i=ft[t][0],s=!1!==ft[t][2];break}if(null==i)e._isValid=!1;else{if(u[3]){for(t=0,n=l;t<n;t++)if(mt[t][1].exec(u[3])){r=(u[2]||" ")+mt[t][0];break}if(null==r)return void(e._isValid=!1)}if(s||null==r){if(u[4]){if(!ct.exec(u[4]))return void(e._isValid=!1);a="Z"}e._f=i+(r||"")+(a||""),Dt(e)}else e._isValid=!1}}else e._isValid=!1}function pt(e){var t,n,s,i,r,a,o,u,l,d,h,c=yt.exec(e._i.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));c?(r=c[4],a=c[3],o=c[2],u=c[5],l=c[6],d=c[7],r=[(h=r,(h=parseInt(h,10))<=49?2e3+h:h<=999?1900+h:h),xe.indexOf(a),parseInt(o,10),parseInt(u,10),parseInt(l,10)],d&&r.push(parseInt(d,10)),s=t=r,i=e,(n=c[1])&&ze.indexOf(n)!==new Date(s[0],s[1],s[2]).getDay()?(p(i).weekdayMismatch=!0,i._isValid=!1):(e._a=t,e._tzm=(n=c[8],s=c[9],i=c[10],n?gt[n]:s?0:((n=parseInt(i,10))-(s=n%100))/100*60+s),e._d=Fe.apply(null,e._a),e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),p(e).rfc2822=!0)):e._isValid=!1}function Yt(e,t,n){return null!=e?e:null!=t?t:n}function Mt(e){var t,n,s,i,r,a,o,u,l,d,h,c=[];if(!e._d){for(s=e,i=new Date(g.now()),n=s._useUTC?[i.getUTCFullYear(),i.getUTCMonth(),i.getUTCDate()]:[i.getFullYear(),i.getMonth(),i.getDate()],e._w&&null==e._a[Ye]&&null==e._a[pe]&&(null!=(i=(s=e)._w).GG||null!=i.W||null!=i.E?(u=1,l=4,r=Yt(i.GG,s._a[we],Ge(St(),1,4).year),a=Yt(i.W,1),((o=Yt(i.E,1))<1||7<o)&&(d=!0)):(u=s._locale._week.dow,l=s._locale._week.doy,h=Ge(St(),u,l),r=Yt(i.gg,s._a[we],h.year),a=Yt(i.w,h.week),null!=i.d?((o=i.d)<0||6<o)&&(d=!0):null!=i.e?(o=i.e+u,(i.e<0||6<i.e)&&(d=!0)):o=u),a<1||a>Ae(r,u,l)?p(s)._overflowWeeks=!0:null!=d?p(s)._overflowWeekday=!0:(h=Ee(r,a,o,u,l),s._a[we]=h.year,s._dayOfYear=h.dayOfYear)),null!=e._dayOfYear&&(i=Yt(e._a[we],n[we]),(e._dayOfYear>Ce(i)||0===e._dayOfYear)&&(p(e)._overflowDayOfYear=!0),d=Fe(i,0,e._dayOfYear),e._a[pe]=d.getUTCMonth(),e._a[Ye]=d.getUTCDate()),t=0;t<3&&null==e._a[t];++t)e._a[t]=c[t]=n[t];for(;t<7;t++)e._a[t]=c[t]=null==e._a[t]?2===t?1:0:e._a[t];24===e._a[Me]&&0===e._a[De]&&0===e._a[ke]&&0===e._a[ve]&&(e._nextDay=!0,e._a[Me]=0),e._d=(e._useUTC?Fe:function(e,t,n,s,i,r,a){var o;return e<100&&0<=e?(o=new Date(e+400,t,n,s,i,r,a),isFinite(o.getFullYear())&&o.setFullYear(e)):o=new Date(e,t,n,s,i,r,a),o}).apply(null,c),r=e._useUTC?e._d.getUTCDay():e._d.getDay(),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[Me]=24),e._w&&void 0!==e._w.d&&e._w.d!==r&&(p(e).weekdayMismatch=!0)}}function Dt(e){if(e._f===g.ISO_8601)wt(e);else if(e._f===g.RFC_2822)pt(e);else{e._a=[],p(e).empty=!0;for(var t,n,s,i,r,a=""+e._i,o=a.length,u=0,l=C(e._f,e._locale).match(N)||[],d=l.length,h=0;h<d;h++)n=l[h],(t=(a.match((_=n,y=e,w(fe,_)?fe[_](y._strict,y._locale):new RegExp(ce(_.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(e,t,n,s,i){return t||n||s||i})))))||[])[0])&&(0<(s=a.substr(0,a.indexOf(t))).length&&p(e).unusedInput.push(s),a=a.slice(a.indexOf(t)+t.length),u+=t.length),W[n]?(t?p(e).empty=!1:p(e).unusedTokens.push(n),s=n,r=e,null!=(i=t)&&w(me,s)&&me[s](i,r._a,r,s)):e._strict&&!t&&p(e).unusedTokens.push(n);p(e).charsLeftOver=o-u,0<a.length&&p(e).unusedInput.push(a),e._a[Me]<=12&&!0===p(e).bigHour&&0<e._a[Me]&&(p(e).bigHour=void 0),p(e).parsedDateParts=e._a.slice(0),p(e).meridiem=e._meridiem,e._a[Me]=(c=e._locale,f=e._a[Me],null==(m=e._meridiem)?f:null!=c.meridiemHour?c.meridiemHour(f,m):null!=c.isPM?((c=c.isPM(m))&&f<12&&(f+=12),f=c||12!==f?f:0):f),null!==(o=p(e).era)&&(e._a[we]=e._locale.erasConvertYear(o,e._a[we])),Mt(e),lt(e)}var c,f,m,_,y}function kt(e){var t,n,s,i=e._i,r=e._f;return e._locale=e._locale||ut(e._l),null===i||void 0===r&&""===i?y({nullInput:!0}):("string"==typeof i&&(e._i=i=e._locale.preparse(i)),k(i)?new D(lt(i)):(h(i)?e._d=i:o(r)?function(e){var t,n,s,i,r,a,o=!1,u=e._f.length;if(0===u)return p(e).invalidFormat=!0,e._d=new Date(NaN);for(i=0;i<u;i++)r=0,a=!1,t=M({},e),null!=e._useUTC&&(t._useUTC=e._useUTC),t._f=e._f[i],Dt(t),_(t)&&(a=!0),r=(r+=p(t).charsLeftOver)+10*p(t).unusedTokens.length,p(t).score=r,o?r<s&&(s=r,n=t):(null==s||r<s||a)&&(s=r,n=t,a&&(o=!0));f(e,n||t)}(e):r?Dt(e):a(r=(i=e)._i)?i._d=new Date(g.now()):h(r)?i._d=new Date(r.valueOf()):"string"==typeof r?(n=i,null!==(t=_t.exec(n._i))?n._d=new Date(+t[1]):(wt(n),!1===n._isValid&&(delete n._isValid,pt(n),!1===n._isValid&&(delete n._isValid,n._strict?n._isValid=!1:g.createFromInputFallback(n))))):o(r)?(i._a=c(r.slice(0),function(e){return parseInt(e,10)}),Mt(i)):u(r)?(t=i)._d||(s=void 0===(n=E(t._i)).day?n.date:n.day,t._a=c([n.year,n.month,s,n.hour,n.minute,n.second,n.millisecond],function(e){return e&&parseInt(e,10)}),Mt(t)):d(r)?i._d=new Date(r):g.createFromInputFallback(i),_(e)||(e._d=null),e))}function vt(e,t,n,s,i){var r={};return!0!==t&&!1!==t||(s=t,t=void 0),!0!==n&&!1!==n||(s=n,n=void 0),(u(e)&&l(e)||o(e)&&0===e.length)&&(e=void 0),r._isAMomentObject=!0,r._useUTC=r._isUTC=i,r._l=n,r._i=e,r._f=t,r._strict=s,(i=new D(lt(kt(i=r))))._nextDay&&(i.add(1,"d"),i._nextDay=void 0),i}function St(e,t,n,s){return vt(e,t,n,s,!1)}function Ot(e,t){var n,s;if(!(t=1===t.length&&o(t[0])?t[0]:t).length)return St();for(n=t[0],s=1;s<t.length;++s)t[s].isValid()&&!t[s][e](n)||(n=t[s]);return n}g.createFromInputFallback=n("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))}),g.ISO_8601=function(){},g.RFC_2822=function(){},te=n("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=St.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:y()}),ne=n("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=St.apply(null,arguments);return this.isValid()&&e.isValid()?this<e?this:e:y()});var bt=["year","quarter","month","week","day","hour","minute","second","millisecond"];function Tt(e){var t=(e=E(e)).year||0,n=e.quarter||0,s=e.month||0,i=e.week||e.isoWeek||0,r=e.day||0,a=e.hour||0,o=e.minute||0,u=e.second||0,l=e.millisecond||0;this._isValid=function(e){var t,n,s=!1,i=bt.length;for(t in e)if(w(e,t)&&(-1===ge.call(bt,t)||null!=e[t]&&isNaN(e[t])))return!1;for(n=0;n<i;++n)if(e[bt[n]]){if(s)return!1;parseFloat(e[bt[n]])!==z(e[bt[n]])&&(s=!0)}return!0}(e),this._milliseconds=+l+1e3*u+6e4*o+1e3*a*60*60,this._days=+r+7*i,this._months=+s+3*n+12*t,this._data={},this._locale=ut(),this._bubble()}function xt(e){return e instanceof Tt}function Nt(e){return e<0?-1*Math.round(-1*e):Math.round(e)}function Lt(e,n){R(e,0,0,function(){var e=this.utcOffset(),t="+";return e<0&&(e=-e,t="-"),t+x(~~(e/60),2)+n+x(~~e%60,2)})}Lt("Z",":"),Lt("ZZ",""),he("Z",le),he("ZZ",le),_e(["Z","ZZ"],function(e,t,n){n._useUTC=!0,n._tzm=Wt(le,e)});var Pt=/([\+\-]|\d\d)/gi;function Wt(e,t){return null===(t=(t||"").match(e))?null:0===(t=60*(e=((t[t.length-1]||[])+"").match(Pt)||["-",0,0])[1]+z(e[2]))?0:"+"===e[0]?t:-t}function Rt(e,t){var n;return t._isUTC?(t=t.clone(),n=(k(e)||h(e)?e:St(e)).valueOf()-t.valueOf(),t._d.setTime(t._d.valueOf()+n),g.updateOffset(t,!1),t):St(e).local()}function Ht(e){return-Math.round(e._d.getTimezoneOffset())}function Ct(){return!!this.isValid()&&this._isUTC&&0===this._offset}g.updateOffset=function(){};var Ut=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,Ft=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function Vt(e,t){var n,s,i,r,a=e,o=null;return xt(e)?a={ms:e._milliseconds,d:e._days,M:e._months}:d(e)||!isNaN(+e)?(a={},t?a[t]=+e:a.milliseconds=+e):(o=Ut.exec(e))?(n="-"===o[1]?-1:1,a={y:0,d:z(o[Ye])*n,h:z(o[Me])*n,m:z(o[De])*n,s:z(o[ke])*n,ms:z(Nt(1e3*o[ve]))*n}):(o=Ft.exec(e))?(n="-"===o[1]?-1:1,a={y:Et(o[2],n),M:Et(o[3],n),w:Et(o[4],n),d:Et(o[5],n),h:Et(o[6],n),m:Et(o[7],n),s:Et(o[8],n)}):null==a?a={}:"object"==typeof a&&("from"in a||"to"in a)&&(s=St(a.from),i=St(a.to),t=s.isValid()&&i.isValid()?(i=Rt(i,s),s.isBefore(i)?r=Gt(s,i):((r=Gt(i,s)).milliseconds=-r.milliseconds,r.months=-r.months),r):{milliseconds:0,months:0},(a={}).ms=t.milliseconds,a.M=t.months),o=new Tt(a),xt(e)&&w(e,"_locale")&&(o._locale=e._locale),xt(e)&&w(e,"_isValid")&&(o._isValid=e._isValid),o}function Et(e,t){return e=e&&parseFloat(e.replace(",",".")),(isNaN(e)?0:e)*t}function Gt(e,t){var n={};return n.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(n.months,"M").isAfter(t)&&--n.months,n.milliseconds=+t-+e.clone().add(n.months,"M"),n}function At(s,i){return function(e,t){var n;return null===t||isNaN(+t)||(r(i,"moment()."+i+"(period, number) is deprecated. Please use moment()."+i+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),n=e,e=t,t=n),jt(this,Vt(e,t),s),this}}function jt(e,t,n,s){var i=t._milliseconds,r=Nt(t._days);t=Nt(t._months);e.isValid()&&(s=null==s||s,t&&We(e,q(e,"Month")+t*n),r&&$(e,"Date",q(e,"Date")+r*n),i&&e._d.setTime(e._d.valueOf()+i*n),s&&g.updateOffset(e,r||t))}function It(e){return"string"==typeof e||e instanceof String}function zt(e,t){if(e.date()<t.date())return-zt(t,e);var n=12*(t.year()-e.year())+(t.month()-e.month()),s=e.clone().add(n,"months");return-(n+(t=t-s<0?(t-s)/(s-e.clone().add(n-1,"months")):(t-s)/(e.clone().add(1+n,"months")-s)))||0}function Zt(e){return void 0===e?this._locale._abbr:(null!=(e=ut(e))&&(this._locale=e),this)}function qt(){return this._locale}Vt.fn=Tt.prototype,Vt.invalid=function(){return Vt(NaN)},Te=At(1,"add"),Ie=At(-1,"subtract"),g.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",g.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]",Ze=n("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(e){return void 0===e?this.localeData():this.locale(e)});var $t=126227808e5;function Bt(e,t){return(e%t+t)%t}function Jt(e,t,n){return e<100&&0<=e?new Date(e+400,t,n)-$t:new Date(e,t,n).valueOf()}function Qt(e,t,n){return e<100&&0<=e?Date.UTC(e+400,t,n)-$t:Date.UTC(e,t,n)}function Xt(e,t){return t.erasAbbrRegex(e)}function Kt(){for(var e=[],t=[],n=[],s=[],i=this.eras(),r=0,a=i.length;r<a;++r)t.push(ce(i[r].name)),e.push(ce(i[r].abbr)),n.push(ce(i[r].narrow)),s.push(ce(i[r].name)),s.push(ce(i[r].abbr)),s.push(ce(i[r].narrow));this._erasRegex=new RegExp("^("+s.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+t.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+e.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+n.join("|")+")","i")}function en(e,t){R(0,[e,e.length],0,t)}function tn(e,t,n,s,i){var r;return null==e?Ge(this,s,i).year:(r=Ae(e,s,i),function(e,t,n,s,i){return t=Fe((e=Ee(e,t,n,s,i)).year,0,e.dayOfYear),this.year(t.getUTCFullYear()),this.month(t.getUTCMonth()),this.date(t.getUTCDate()),this}.call(this,e,t=r<t?r:t,n,s,i))}R("N",0,0,"eraAbbr"),R("NN",0,0,"eraAbbr"),R("NNN",0,0,"eraAbbr"),R("NNNN",0,0,"eraName"),R("NNNNN",0,0,"eraNarrow"),R("y",["y",1],"yo","eraYear"),R("y",["yy",2],0,"eraYear"),R("y",["yyy",3],0,"eraYear"),R("y",["yyyy",4],0,"eraYear"),he("N",Xt),he("NN",Xt),he("NNN",Xt),he("NNNN",function(e,t){return t.erasNameRegex(e)}),he("NNNNN",function(e,t){return t.erasNarrowRegex(e)}),_e(["N","NN","NNN","NNNN","NNNNN"],function(e,t,n,s){(s=n._locale.erasParse(e,s,n._strict))?p(n).era=s:p(n).invalidEra=e}),he("y",ae),he("yy",ae),he("yyy",ae),he("yyyy",ae),he("yo",function(e,t){return t._eraYearOrdinalRegex||ae}),_e(["y","yy","yyy","yyyy"],we),_e(["yo"],function(e,t,n,s){var i;n._locale._eraYearOrdinalRegex&&(i=e.match(n._locale._eraYearOrdinalRegex)),n._locale.eraYearOrdinalParse?t[we]=n._locale.eraYearOrdinalParse(e,i):t[we]=parseInt(e,10)}),R(0,["gg",2],0,function(){return this.weekYear()%100}),R(0,["GG",2],0,function(){return this.isoWeekYear()%100}),en("gggg","weekYear"),en("ggggg","weekYear"),en("GGGG","isoWeekYear"),en("GGGGG","isoWeekYear"),F("weekYear","gg"),F("isoWeekYear","GG"),A("weekYear",1),A("isoWeekYear",1),he("G",oe),he("g",oe),he("GG",ee,J),he("gg",ee,J),he("GGGG",ie,X),he("gggg",ie,X),he("GGGGG",re,K),he("ggggg",re,K),ye(["gggg","ggggg","GGGG","GGGGG"],function(e,t,n,s){t[s.substr(0,2)]=z(e)}),ye(["gg","GG"],function(e,t,n,s){t[s]=g.parseTwoDigitYear(e)}),R("Q",0,"Qo","quarter"),F("quarter","Q"),A("quarter",7),he("Q",B),_e("Q",function(e,t){t[pe]=3*(z(e)-1)}),R("D",["DD",2],"Do","date"),F("date","D"),A("date",9),he("D",ee),he("DD",ee,J),he("Do",function(e,t){return e?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient}),_e(["D","DD"],Ye),_e("Do",function(e,t){t[Ye]=z(e.match(ee)[0])}),ie=Z("Date",!0),R("DDD",["DDDD",3],"DDDo","dayOfYear"),F("dayOfYear","DDD"),A("dayOfYear",4),he("DDD",se),he("DDDD",Q),_e(["DDD","DDDD"],function(e,t,n){n._dayOfYear=z(e)}),R("m",["mm",2],0,"minute"),F("minute","m"),A("minute",14),he("m",ee),he("mm",ee,J),_e(["m","mm"],De);var nn;X=Z("Minutes",!1),R("s",["ss",2],0,"second"),F("second","s"),A("second",15),he("s",ee),he("ss",ee,J),_e(["s","ss"],ke),re=Z("Seconds",!1);for(R("S",0,0,function(){return~~(this.millisecond()/100)}),R(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),R(0,["SSS",3],0,"millisecond"),R(0,["SSSS",4],0,function(){return 10*this.millisecond()}),R(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),R(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),R(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),R(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),R(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),F("millisecond","ms"),A("millisecond",16),he("S",se,B),he("SS",se,J),he("SSS",se,Q),nn="SSSS";nn.length<=9;nn+="S")he(nn,ae);function sn(e,t){t[ve]=z(1e3*("0."+e))}for(nn="S";nn.length<=9;nn+="S")_e(nn,sn);function rn(e){return e}function an(e,t,n,s){var i=ut();s=m().set(s,t);return i[n](s,e)}function on(e,t,n){if(d(e)&&(t=e,e=void 0),e=e||"",null!=t)return an(e,t,n,"month");for(var s=[],i=0;i<12;i++)s[i]=an(e,i,n,"month");return s}function un(e,t,n,s){"boolean"==typeof e?d(t)&&(n=t,t=void 0):(t=e,e=!1,d(n=t)&&(n=t,t=void 0)),t=t||"";var i,r=ut(),a=e?r._week.dow:0,o=[];if(null!=n)return an(t,(n+a)%7,s,"day");for(i=0;i<7;i++)o[i]=an(t,(i+a)%7,s,"day");return o}K=Z("Milliseconds",!1),R("z",0,0,"zoneAbbr"),R("zz",0,0,"zoneName"),(B=D.prototype).add=Te,B.calendar=function(e,t){var n,s,i,r;1===arguments.length&&(arguments[0]?k(n=arguments[0])||h(n)||It(n)||d(n)||(i=o(s=n),r=!1,i&&(r=0===s.filter(function(e){return!d(e)&&It(s)}).length),i&&r)||function(e){var t,n=u(e)&&!l(e),s=!1,i=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],r=i.length;for(t=0;t<r;t+=1)s=s||w(e,i[t]);return n&&s}(n)||null==n?(e=arguments[0],t=void 0):function(e){for(var t=u(e)&&!l(e),n=!1,s=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"],i=0;i<s.length;i+=1)n=n||w(e,s[i]);return t&&n}(arguments[0])&&(t=arguments[0],e=void 0):t=e=void 0);var a=Rt(e=e||St(),this).startOf("day");a=g.calendarFormat(this,a)||"sameElse",t=t&&(S(t[a])?t[a].call(this,e):t[a]);return this.format(t||this.localeData().calendar(a,this,St(e)))},B.clone=function(){return new D(this)},B.diff=function(e,t,n){var s,i,r;if(!this.isValid())return NaN;if(!(s=Rt(e,this)).isValid())return NaN;switch(i=6e4*(s.utcOffset()-this.utcOffset()),t=V(t)){case"year":r=zt(this,s)/12;break;case"month":r=zt(this,s);break;case"quarter":r=zt(this,s)/3;break;case"second":r=(this-s)/1e3;break;case"minute":r=(this-s)/6e4;break;case"hour":r=(this-s)/36e5;break;case"day":r=(this-s-i)/864e5;break;case"week":r=(this-s-i)/6048e5;break;default:r=this-s}return n?r:I(r)},B.endOf=function(e){var t,n;if(void 0===(e=V(e))||"millisecond"===e||!this.isValid())return this;switch(n=this._isUTC?Qt:Jt,e){case"year":t=n(this.year()+1,0,1)-1;break;case"quarter":t=n(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":t=n(this.year(),this.month()+1,1)-1;break;case"week":t=n(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":t=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":t=n(this.year(),this.month(),this.date()+1)-1;break;case"hour":t=this._d.valueOf(),t+=36e5-Bt(t+(this._isUTC?0:6e4*this.utcOffset()),36e5)-1;break;case"minute":t=this._d.valueOf(),t+=6e4-Bt(t,6e4)-1;break;case"second":t=this._d.valueOf(),t+=1e3-Bt(t,1e3)-1}return this._d.setTime(t),g.updateOffset(this,!0),this},B.format=function(e){return e=e||(this.isUtc()?g.defaultFormatUtc:g.defaultFormat),e=H(this,e),this.localeData().postformat(e)},B.from=function(e,t){return this.isValid()&&(k(e)&&e.isValid()||St(e).isValid())?Vt({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},B.fromNow=function(e){return this.from(St(),e)},B.to=function(e,t){return this.isValid()&&(k(e)&&e.isValid()||St(e).isValid())?Vt({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},B.toNow=function(e){return this.to(St(),e)},B.get=function(e){return S(this[e=V(e)])?this[e]():this},B.invalidAt=function(){return p(this).overflow},B.isAfter=function(e,t){return e=k(e)?e:St(e),!(!this.isValid()||!e.isValid())&&("millisecond"===(t=V(t)||"millisecond")?this.valueOf()>e.valueOf():e.valueOf()<this.clone().startOf(t).valueOf())},B.isBefore=function(e,t){return e=k(e)?e:St(e),!(!this.isValid()||!e.isValid())&&("millisecond"===(t=V(t)||"millisecond")?this.valueOf()<e.valueOf():this.clone().endOf(t).valueOf()<e.valueOf())},B.isBetween=function(e,t,n,s){return e=k(e)?e:St(e),t=k(t)?t:St(t),!!(this.isValid()&&e.isValid()&&t.isValid())&&("("===(s=s||"()")[0]?this.isAfter(e,n):!this.isBefore(e,n))&&(")"===s[1]?this.isBefore(t,n):!this.isAfter(t,n))},B.isSame=function(e,t){e=k(e)?e:St(e);return!(!this.isValid()||!e.isValid())&&("millisecond"===(t=V(t)||"millisecond")?this.valueOf()===e.valueOf():(e=e.valueOf(),this.clone().startOf(t).valueOf()<=e&&e<=this.clone().endOf(t).valueOf()))},B.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)},B.isSameOrBefore=function(e,t){return this.isSame(e,t)||this.isBefore(e,t)},B.isValid=function(){return _(this)},B.lang=Ze,B.locale=Zt,B.localeData=qt,B.max=ne,B.min=te,B.parsingFlags=function(){return f({},p(this))},B.set=function(e,t){if("object"==typeof e)for(var n=function(e){var t,n=[];for(t in e)w(e,t)&&n.push({unit:t,priority:G[t]});return n.sort(function(e,t){return e.priority-t.priority}),n}(e=E(e)),s=n.length,i=0;i<s;i++)this[n[i].unit](e[n[i].unit]);else if(S(this[e=V(e)]))return this[e](t);return this},B.startOf=function(e){var t,n;if(void 0===(e=V(e))||"millisecond"===e||!this.isValid())return this;switch(n=this._isUTC?Qt:Jt,e){case"year":t=n(this.year(),0,1);break;case"quarter":t=n(this.year(),this.month()-this.month()%3,1);break;case"month":t=n(this.year(),this.month(),1);break;case"week":t=n(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":t=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":t=n(this.year(),this.month(),this.date());break;case"hour":t=this._d.valueOf(),t-=Bt(t+(this._isUTC?0:6e4*this.utcOffset()),36e5);break;case"minute":t=this._d.valueOf(),t-=Bt(t,6e4);break;case"second":t=this._d.valueOf(),t-=Bt(t,1e3)}return this._d.setTime(t),g.updateOffset(this,!0),this},B.subtract=Ie,B.toArray=function(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]},B.toObject=function(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}},B.toDate=function(){return new Date(this.valueOf())},B.toISOString=function(e){if(!this.isValid())return null;var t=(e=!0!==e)?this.clone().utc():this;return t.year()<0||9999<t.year()?H(t,e?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):S(Date.prototype.toISOString)?e?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",H(t,"Z")):H(t,e?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")},B.inspect=function(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var e,t="moment",n="";return this.isLocal()||(t=0===this.utcOffset()?"moment.utc":"moment.parseZone",n="Z"),t="["+t+'("]',e=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",this.format(t+e+"-MM-DD[T]HH:mm:ss.SSS"+n+'[")]')},"undefined"!=typeof Symbol&&null!=Symbol.for&&(B[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),B.toJSON=function(){return this.isValid()?this.toISOString():null},B.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},B.unix=function(){return Math.floor(this.valueOf()/1e3)},B.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},B.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},B.eraName=function(){for(var e,t=this.localeData().eras(),n=0,s=t.length;n<s;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].name;if(t[n].until<=e&&e<=t[n].since)return t[n].name}return""},B.eraNarrow=function(){for(var e,t=this.localeData().eras(),n=0,s=t.length;n<s;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].narrow;if(t[n].until<=e&&e<=t[n].since)return t[n].narrow}return""},B.eraAbbr=function(){for(var e,t=this.localeData().eras(),n=0,s=t.length;n<s;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].abbr;if(t[n].until<=e&&e<=t[n].since)return t[n].abbr}return""},B.eraYear=function(){for(var e,t,n=this.localeData().eras(),s=0,i=n.length;s<i;++s)if(e=n[s].since<=n[s].until?1:-1,t=this.clone().startOf("day").valueOf(),n[s].since<=t&&t<=n[s].until||n[s].until<=t&&t<=n[s].since)return(this.year()-g(n[s].since).year())*e+n[s].offset;return this.year()},B.year=Ue,B.isLeapYear=function(){return j(this.year())},B.weekYear=function(e){return tn.call(this,e,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)},B.isoWeekYear=function(e){return tn.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)},B.quarter=B.quarters=function(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)},B.month=Re,B.daysInMonth=function(){return be(this.year(),this.month())},B.week=B.weeks=function(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")},B.isoWeek=B.isoWeeks=function(e){var t=Ge(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")},B.weeksInYear=function(){var e=this.localeData()._week;return Ae(this.year(),e.dow,e.doy)},B.weeksInWeekYear=function(){var e=this.localeData()._week;return Ae(this.weekYear(),e.dow,e.doy)},B.isoWeeksInYear=function(){return Ae(this.year(),1,4)},B.isoWeeksInISOWeekYear=function(){return Ae(this.isoWeekYear(),1,4)},B.date=ie,B.day=B.days=function(e){if(!this.isValid())return null!=e?this:NaN;var t,n,s=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=e?(t=e,n=this.localeData(),e="string"!=typeof t?t:isNaN(t)?"number"==typeof(t=n.weekdaysParse(t))?t:null:parseInt(t,10),this.add(e-s,"d")):s},B.weekday=function(e){if(!this.isValid())return null!=e?this:NaN;var t=(this.day()+7-this.localeData()._week.dow)%7;return null==e?t:this.add(e-t,"d")},B.isoWeekday=function(e){return this.isValid()?null!=e?(t=e,n=this.localeData(),n="string"==typeof t?n.weekdaysParse(t)%7||7:isNaN(t)?null:t,this.day(this.day()%7?n:n-7)):this.day()||7:null!=e?this:NaN;var t,n},B.dayOfYear=function(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")},B.hour=B.hours=de,B.minute=B.minutes=X,B.second=B.seconds=re,B.millisecond=B.milliseconds=K,B.utcOffset=function(e,t,n){var s,i=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null==e)return this._isUTC?i:Ht(this);if("string"==typeof e){if(null===(e=Wt(le,e)))return this}else Math.abs(e)<16&&!n&&(e*=60);return!this._isUTC&&t&&(s=Ht(this)),this._offset=e,this._isUTC=!0,null!=s&&this.add(s,"m"),i!==e&&(!t||this._changeInProgress?jt(this,Vt(e-i,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,g.updateOffset(this,!0),this._changeInProgress=null)),this},B.utc=function(e){return this.utcOffset(0,e)},B.local=function(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e&&this.subtract(Ht(this),"m")),this},B.parseZone=function(){var e;return null!=this._tzm?this.utcOffset(this._tzm,!1,!0):"string"==typeof this._i&&(null!=(e=Wt(ue,this._i))?this.utcOffset(e):this.utcOffset(0,!0)),this},B.hasAlignedHourOffset=function(e){return!!this.isValid()&&(e=e?St(e).utcOffset():0,(this.utcOffset()-e)%60==0)},B.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},B.isLocal=function(){return!!this.isValid()&&!this._isUTC},B.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},B.isUtc=Ct,B.isUTC=Ct,B.zoneAbbr=function(){return this._isUTC?"UTC":""},B.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},B.dates=n("dates accessor is deprecated. Use date instead.",ie),B.months=n("months accessor is deprecated. Use month instead",Re),B.years=n("years accessor is deprecated. Use year instead",Ue),B.zone=n("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",function(e,t){return null!=e?(this.utcOffset(e="string"!=typeof e?-e:e,t),this):-this.utcOffset()}),B.isDSTShifted=n("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",function(){if(!a(this._isDSTShifted))return this._isDSTShifted;var e,t={};return M(t,this),(t=kt(t))._a?(e=(t._isUTC?m:St)(t._a),this._isDSTShifted=this.isValid()&&0<function(e,t,n){for(var s=Math.min(e.length,t.length),i=Math.abs(e.length-t.length),r=0,a=0;a<s;a++)z(e[a])!==z(t[a])&&r++;return r+i}(t._a,e.toArray())):this._isDSTShifted=!1,this._isDSTShifted}),(J=b.prototype).calendar=function(e,t,n){return S(e=this._calendar[e]||this._calendar.sameElse)?e.call(t,n):e},J.longDateFormat=function(e){var t=this._longDateFormat[e],n=this._longDateFormat[e.toUpperCase()];return t||!n?t:(this._longDateFormat[e]=n.match(N).map(function(e){return"MMMM"===e||"MM"===e||"DD"===e||"dddd"===e?e.slice(1):e}).join(""),this._longDateFormat[e])},J.invalidDate=function(){return this._invalidDate},J.ordinal=function(e){return this._ordinal.replace("%d",e)},J.preparse=rn,J.postformat=rn,J.relativeTime=function(e,t,n,s){var i=this._relativeTime[n];return S(i)?i(e,t,n,s):i.replace(/%d/i,e)},J.pastFuture=function(e,t){return S(e=this._relativeTime[0<e?"future":"past"])?e(t):e.replace(/%s/i,t)},J.set=function(e){var t,n;for(n in e)w(e,n)&&(S(t=e[n])?this[n]=t:this["_"+n]=t);this._config=e,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},J.eras=function(e,t){for(var n,s=this._eras||ut("en")._eras,i=0,r=s.length;i<r;++i){switch(typeof s[i].since){case"string":n=g(s[i].since).startOf("day"),s[i].since=n.valueOf()}switch(typeof s[i].until){case"undefined":s[i].until=1/0;break;case"string":n=g(s[i].until).startOf("day").valueOf(),s[i].until=n.valueOf()}}return s},J.erasParse=function(e,t,n){var s,i,r,a,o,u=this.eras();for(e=e.toUpperCase(),s=0,i=u.length;s<i;++s)if(r=u[s].name.toUpperCase(),a=u[s].abbr.toUpperCase(),o=u[s].narrow.toUpperCase(),n)switch(t){case"N":case"NN":case"NNN":if(a===e)return u[s];break;case"NNNN":if(r===e)return u[s];break;case"NNNNN":if(o===e)return u[s]}else if(0<=[r,a,o].indexOf(e))return u[s]},J.erasConvertYear=function(e,t){var n=e.since<=e.until?1:-1;return void 0===t?g(e.since).year():g(e.since).year()+(t-e.offset)*n},J.erasAbbrRegex=function(e){return w(this,"_erasAbbrRegex")||Kt.call(this),e?this._erasAbbrRegex:this._erasRegex},J.erasNameRegex=function(e){return w(this,"_erasNameRegex")||Kt.call(this),e?this._erasNameRegex:this._erasRegex},J.erasNarrowRegex=function(e){return w(this,"_erasNarrowRegex")||Kt.call(this),e?this._erasNarrowRegex:this._erasRegex},J.months=function(e,t){return e?(o(this._months)?this._months:this._months[(this._months.isFormat||Ne).test(t)?"format":"standalone"])[e.month()]:o(this._months)?this._months:this._months.standalone},J.monthsShort=function(e,t){return e?(o(this._monthsShort)?this._monthsShort:this._monthsShort[Ne.test(t)?"format":"standalone"])[e.month()]:o(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},J.monthsParse=function(e,t,n){var s,i;if(this._monthsParseExact)return function(e,t,n){var s,i,r;e=e.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],s=0;s<12;++s)r=m([2e3,s]),this._shortMonthsParse[s]=this.monthsShort(r,"").toLocaleLowerCase(),this._longMonthsParse[s]=this.months(r,"").toLocaleLowerCase();return n?"MMM"===t?-1!==(i=ge.call(this._shortMonthsParse,e))?i:null:-1!==(i=ge.call(this._longMonthsParse,e))?i:null:"MMM"===t?-1!==(i=ge.call(this._shortMonthsParse,e))||-1!==(i=ge.call(this._longMonthsParse,e))?i:null:-1!==(i=ge.call(this._longMonthsParse,e))||-1!==(i=ge.call(this._shortMonthsParse,e))?i:null}.call(this,e,t,n);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),s=0;s<12;s++){if(i=m([2e3,s]),n&&!this._longMonthsParse[s]&&(this._longMonthsParse[s]=new RegExp("^"+this.months(i,"").replace(".","")+"$","i"),this._shortMonthsParse[s]=new RegExp("^"+this.monthsShort(i,"").replace(".","")+"$","i")),n||this._monthsParse[s]||(i="^"+this.months(i,"")+"|^"+this.monthsShort(i,""),this._monthsParse[s]=new RegExp(i.replace(".",""),"i")),n&&"MMMM"===t&&this._longMonthsParse[s].test(e))return s;if(n&&"MMM"===t&&this._shortMonthsParse[s].test(e))return s;if(!n&&this._monthsParse[s].test(e))return s}},J.monthsRegex=function(e){return this._monthsParseExact?(w(this,"_monthsRegex")||He.call(this),e?this._monthsStrictRegex:this._monthsRegex):(w(this,"_monthsRegex")||(this._monthsRegex=Pe),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)},J.monthsShortRegex=function(e){return this._monthsParseExact?(w(this,"_monthsRegex")||He.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(w(this,"_monthsShortRegex")||(this._monthsShortRegex=Le),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)},J.week=function(e){return Ge(e,this._week.dow,this._week.doy).week},J.firstDayOfYear=function(){return this._week.doy},J.firstDayOfWeek=function(){return this._week.dow},J.weekdays=function(e,t){return t=o(this._weekdays)?this._weekdays:this._weekdays[e&&!0!==e&&this._weekdays.isFormat.test(t)?"format":"standalone"],!0===e?je(t,this._week.dow):e?t[e.day()]:t},J.weekdaysMin=function(e){return!0===e?je(this._weekdaysMin,this._week.dow):e?this._weekdaysMin[e.day()]:this._weekdaysMin},J.weekdaysShort=function(e){return!0===e?je(this._weekdaysShort,this._week.dow):e?this._weekdaysShort[e.day()]:this._weekdaysShort},J.weekdaysParse=function(e,t,n){var s,i;if(this._weekdaysParseExact)return function(e,t,n){var s,i,r;e=e.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],s=0;s<7;++s)r=m([2e3,1]).day(s),this._minWeekdaysParse[s]=this.weekdaysMin(r,"").toLocaleLowerCase(),this._shortWeekdaysParse[s]=this.weekdaysShort(r,"").toLocaleLowerCase(),this._weekdaysParse[s]=this.weekdays(r,"").toLocaleLowerCase();return n?"dddd"===t?-1!==(i=ge.call(this._weekdaysParse,e))?i:null:"ddd"===t?-1!==(i=ge.call(this._shortWeekdaysParse,e))?i:null:-1!==(i=ge.call(this._minWeekdaysParse,e))?i:null:"dddd"===t?-1!==(i=ge.call(this._weekdaysParse,e))||-1!==(i=ge.call(this._shortWeekdaysParse,e))||-1!==(i=ge.call(this._minWeekdaysParse,e))?i:null:"ddd"===t?-1!==(i=ge.call(this._shortWeekdaysParse,e))||-1!==(i=ge.call(this._weekdaysParse,e))||-1!==(i=ge.call(this._minWeekdaysParse,e))?i:null:-1!==(i=ge.call(this._minWeekdaysParse,e))||-1!==(i=ge.call(this._weekdaysParse,e))||-1!==(i=ge.call(this._shortWeekdaysParse,e))?i:null}.call(this,e,t,n);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),s=0;s<7;s++){if(i=m([2e3,1]).day(s),n&&!this._fullWeekdaysParse[s]&&(this._fullWeekdaysParse[s]=new RegExp("^"+this.weekdays(i,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[s]=new RegExp("^"+this.weekdaysShort(i,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[s]=new RegExp("^"+this.weekdaysMin(i,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[s]||(i="^"+this.weekdays(i,"")+"|^"+this.weekdaysShort(i,"")+"|^"+this.weekdaysMin(i,""),this._weekdaysParse[s]=new RegExp(i.replace(".",""),"i")),n&&"dddd"===t&&this._fullWeekdaysParse[s].test(e))return s;if(n&&"ddd"===t&&this._shortWeekdaysParse[s].test(e))return s;if(n&&"dd"===t&&this._minWeekdaysParse[s].test(e))return s;if(!n&&this._weekdaysParse[s].test(e))return s}},J.weekdaysRegex=function(e){return this._weekdaysParseExact?(w(this,"_weekdaysRegex")||Je.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(w(this,"_weekdaysRegex")||(this._weekdaysRegex=qe),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)},J.weekdaysShortRegex=function(e){return this._weekdaysParseExact?(w(this,"_weekdaysRegex")||Je.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(w(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=$e),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},J.weekdaysMinRegex=function(e){return this._weekdaysParseExact?(w(this,"_weekdaysRegex")||Je.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(w(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=Be),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},J.isPM=function(e){return"p"===(e+"").toLowerCase().charAt(0)},J.meridiem=function(e,t,n){return 11<e?n?"pm":"PM":n?"am":"AM"},at("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10;return e+(1===z(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")}}),g.lang=n("moment.lang is deprecated. Use moment.locale instead.",at),g.langData=n("moment.langData is deprecated. Use moment.localeData instead.",ut);var ln=Math.abs;function dn(e,t,n,s){return t=Vt(t,n),e._milliseconds+=s*t._milliseconds,e._days+=s*t._days,e._months+=s*t._months,e._bubble()}function hn(e){return e<0?Math.floor(e):Math.ceil(e)}function cn(e){return 4800*e/146097}function fn(e){return 146097*e/4800}function mn(e){return function(){return this.as(e)}}function _n(e){return function(){return this.isValid()?this._data[e]:NaN}}se=mn("ms"),Q=mn("s"),Te=mn("m"),ne=mn("h"),te=mn("d"),Ie=mn("w"),de=mn("M"),X=mn("Q"),re=mn("y");K=_n("milliseconds"),ie=_n("seconds"),Ue=_n("minutes"),J=_n("hours");var yn=_n("days"),gn=_n("months"),wn=_n("years"),pn=Math.round,Yn={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};var Mn=Math.abs;function Dn(e){return(0<e)-(e<0)||+e}function kn(){if(!this.isValid())return this.localeData().invalidDate();var e,t,n,s,i,r,a,o=Mn(this._milliseconds)/1e3,u=Mn(this._days),l=Mn(this._months),d=this.asSeconds();return d?(t=I((e=I(o/60))/60),o%=60,e%=60,n=I(l/12),l%=12,s=o?o.toFixed(3).replace(/\.?0+$/,""):"",i=Dn(this._months)!==Dn(d)?"-":"",r=Dn(this._days)!==Dn(d)?"-":"",a=Dn(this._milliseconds)!==Dn(d)?"-":"",(d<0?"-":"")+"P"+(n?i+n+"Y":"")+(l?i+l+"M":"")+(u?r+u+"D":"")+(t||e||o?"T":"")+(t?a+t+"H":"")+(e?a+e+"M":"")+(o?a+s+"S":"")):"P0D"}var vn=Tt.prototype;return vn.isValid=function(){return this._isValid},vn.abs=function(){var e=this._data;return this._milliseconds=ln(this._milliseconds),this._days=ln(this._days),this._months=ln(this._months),e.milliseconds=ln(e.milliseconds),e.seconds=ln(e.seconds),e.minutes=ln(e.minutes),e.hours=ln(e.hours),e.months=ln(e.months),e.years=ln(e.years),this},vn.add=function(e,t){return dn(this,e,t,1)},vn.subtract=function(e,t){return dn(this,e,t,-1)},vn.as=function(e){if(!this.isValid())return NaN;var t,n,s=this._milliseconds;if("month"===(e=V(e))||"quarter"===e||"year"===e)switch(t=this._days+s/864e5,n=this._months+cn(t),e){case"month":return n;case"quarter":return n/3;case"year":return n/12}else switch(t=this._days+Math.round(fn(this._months)),e){case"week":return t/7+s/6048e5;case"day":return t+s/864e5;case"hour":return 24*t+s/36e5;case"minute":return 1440*t+s/6e4;case"second":return 86400*t+s/1e3;case"millisecond":return Math.floor(864e5*t)+s;default:throw new Error("Unknown unit "+e)}},vn.asMilliseconds=se,vn.asSeconds=Q,vn.asMinutes=Te,vn.asHours=ne,vn.asDays=te,vn.asWeeks=Ie,vn.asMonths=de,vn.asQuarters=X,vn.asYears=re,vn.valueOf=function(){return this.isValid()?this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*z(this._months/12):NaN},vn._bubble=function(){var e=this._milliseconds,t=this._days,n=this._months,s=this._data;return 0<=e&&0<=t&&0<=n||e<=0&&t<=0&&n<=0||(e+=864e5*hn(fn(n)+t),n=t=0),s.milliseconds=e%1e3,e=I(e/1e3),s.seconds=e%60,e=I(e/60),s.minutes=e%60,e=I(e/60),s.hours=e%24,n+=e=I(cn(t+=I(e/24))),t-=hn(fn(e)),e=I(n/12),n%=12,s.days=t,s.months=n,s.years=e,this},vn.clone=function(){return Vt(this)},vn.get=function(e){return e=V(e),this.isValid()?this[e+"s"]():NaN},vn.milliseconds=K,vn.seconds=ie,vn.minutes=Ue,vn.hours=J,vn.days=yn,vn.weeks=function(){return I(this.days()/7)},vn.months=gn,vn.years=wn,vn.humanize=function(e,t){if(!this.isValid())return this.localeData().invalidDate();var n,s,i,r,a,o,u,l,d,h,c,f=!1,m=Yn;return"object"==typeof e&&(t=e,e=!1),"boolean"==typeof e&&(f=e),"object"==typeof t&&(m=Object.assign({},Yn,t),null!=t.s&&null==t.ss&&(m.ss=t.s-1)),e=this.localeData(),s=!f,i=m,r=e,a=Vt(n=this).abs(),o=pn(a.as("s")),u=pn(a.as("m")),l=pn(a.as("h")),d=pn(a.as("d")),h=pn(a.as("M")),c=pn(a.as("w")),a=pn(a.as("y")),o=(o<=i.ss?["s",o]:o<i.s&&["ss",o])||u<=1&&["m"]||u<i.m&&["mm",u]||l<=1&&["h"]||l<i.h&&["hh",l]||d<=1&&["d"]||d<i.d&&["dd",d],(o=(o=null!=i.w?o||c<=1&&["w"]||c<i.w&&["ww",c]:o)||h<=1&&["M"]||h<i.M&&["MM",h]||a<=1&&["y"]||["yy",a])[2]=s,o[3]=0<+n,o[4]=r,t=function(e,t,n,s,i){return i.relativeTime(t||1,!!n,e,s)}.apply(null,o),f&&(t=e.pastFuture(+this,t)),e.postformat(t)},vn.toISOString=kn,vn.toString=kn,vn.toJSON=kn,vn.locale=Zt,vn.localeData=qt,vn.toIsoString=n("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",kn),vn.lang=Ze,R("X",0,0,"unix"),R("x",0,0,"valueOf"),he("x",oe),he("X",/[+-]?\d+(\.\d{1,3})?/),_e("X",function(e,t,n){n._d=new Date(1e3*parseFloat(e))}),_e("x",function(e,t,n){n._d=new Date(z(e))}),g.version="2.29.4",e=St,g.fn=B,g.min=function(){return Ot("isBefore",[].slice.call(arguments,0))},g.max=function(){return Ot("isAfter",[].slice.call(arguments,0))},g.now=function(){return Date.now?Date.now():+new Date},g.utc=m,g.unix=function(e){return St(1e3*e)},g.months=function(e,t){return on(e,t,"months")},g.isDate=h,g.locale=at,g.invalid=y,g.duration=Vt,g.isMoment=k,g.weekdays=function(e,t,n){return un(e,t,n,"weekdays")},g.parseZone=function(){return St.apply(null,arguments).parseZone()},g.localeData=ut,g.isDuration=xt,g.monthsShort=function(e,t){return on(e,t,"monthsShort")},g.weekdaysMin=function(e,t,n){return un(e,t,n,"weekdaysMin")},g.defineLocale=ot,g.updateLocale=function(e,t){var n,s;return null!=t?(s=tt,null!=nt[e]&&null!=nt[e].parentLocale?nt[e].set(O(nt[e]._config,t)):(t=O(s=null!=(n=rt(e))?n._config:s,t),null==n&&(t.abbr=e),(s=new b(t)).parentLocale=nt[e],nt[e]=s),at(e)):null!=nt[e]&&(null!=nt[e].parentLocale?(nt[e]=nt[e].parentLocale,e===at()&&at(e)):null!=nt[e]&&delete nt[e]),nt[e]},g.locales=function(){return T(nt)},g.weekdaysShort=function(e,t,n){return un(e,t,n,"weekdaysShort")},g.normalizeUnits=V,g.relativeTimeRounding=function(e){return void 0===e?pn:"function"==typeof e&&(pn=e,!0)},g.relativeTimeThreshold=function(e,t){return void 0!==Yn[e]&&(void 0===t?Yn[e]:(Yn[e]=t,"s"===e&&(Yn.ss=t-1),!0))},g.calendarFormat=function(e,t){return(e=e.diff(t,"days",!0))<-6?"sameElse":e<-1?"lastWeek":e<0?"lastDay":e<1?"sameDay":e<2?"nextDay":e<7?"nextWeek":"sameElse"},g.prototype=B,g.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},g}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module&&"function"==typeof require?t(require("../moment")):"function"==typeof define&&define.amd?define(["../moment"],t):t(e.moment)}(this,function(e){"use strict";return e.defineLocale("zh-cn",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t?e:"下午"===t||"晚上"===t?e+12:11<=e?e:e+12},meridiem:function(e,t,n){var s=100*e+t;return s<600?"凌晨":s<900?"早上":s<1130?"上午":s<1230?"中午":s<1800?"下午":"晚上"},calendar:{sameDay:"[今天]LT",nextDay:"[明天]LT",nextWeek:function(e){return e.week()!==this.week()?"[下]dddLT":"[本]dddLT"},lastDay:"[昨天]LT",lastWeek:function(e){return this.week()!==e.week()?"[上]dddLT":"[本]dddLT"},sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|周)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"周";default:return e}},relativeTime:{future:"%s后",past:"%s前",s:"几秒",ss:"%d 秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",w:"1 周",ww:"%d 周",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},week:{dow:1,doy:4}})}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module&&"function"==typeof require?t(require("../moment")):"function"==typeof define&&define.amd?define(["../moment"],t):t(e.moment)}(this,function(e){"use strict";return e.defineLocale("zh-tw",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日dddd HH:mm",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t?e:"中午"===t?11<=e?e:e+12:"下午"===t||"晚上"===t?e+12:void 0},meridiem:function(e,t,n){var s=100*e+t;return s<600?"凌晨":s<900?"早上":s<1130?"上午":s<1230?"中午":s<1800?"下午":"晚上"},calendar:{sameDay:"[今天] LT",nextDay:"[明天] LT",nextWeek:"[下]dddd LT",lastDay:"[昨天] LT",lastWeek:"[上]dddd LT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|週)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"週";default:return e}},relativeTime:{future:"%s後",past:"%s前",s:"幾秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"}})});