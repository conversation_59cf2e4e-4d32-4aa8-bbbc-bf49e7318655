#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯会议文件下载器 - 命令行版本
简化版本，便于快速使用
"""

import sys
import os
from tencent_meeting_downloader import TencentMeetingDownloader
from aria2_downloader import TencentMeetingAria2Downloader, start_aria2_if_needed

def main():
    """
    命令行主函数
    """
    print("=" * 60)
    print("腾讯会议文件下载器")
    print("=" * 60)
    
    # 检查Cookie配置
    try:
        from config import TENCENT_MEETING_COOKIE
        if "请将您的Cookie粘贴到这里" in TENCENT_MEETING_COOKIE:
            print("❌ 错误：请先在 config.py 文件中配置您的Cookie")
            print("\n获取Cookie的步骤：")
            print("1. 打开浏览器，登录腾讯会议网页版")
            print("2. 进入会议记录页面：https://meeting.tencent.com/user-center/meeting-record")
            print("3. 按F12打开开发者工具")
            print("4. 切换到Network（网络）标签")
            print("5. 刷新页面，找到任意一个请求")
            print("6. 在请求头中找到Cookie字段，复制完整的Cookie值")
            print("7. 将Cookie值粘贴到 config.py 文件中的 TENCENT_MEETING_COOKIE 变量")
            return
        cookie = TENCENT_MEETING_COOKIE.strip()
    except ImportError:
        print("❌ 错误：找不到 config.py 文件")
        return
    
    # 检查aria2并创建下载器实例
    print("🚀 检查aria2下载器...")
    aria2_available = start_aria2_if_needed()

    if aria2_available:
        print("✅ 使用aria2高速下载模式")
        downloader = TencentMeetingAria2Downloader(cookie)
    else:
        print("⚠️ 使用普通下载模式")
        downloader = TencentMeetingDownloader(cookie)
    
    # 获取会议列表
    print("📋 正在获取会议列表...")
    meetings = downloader.get_meeting_list()
    
    if not meetings:
        print("❌ 未获取到会议列表，请检查Cookie是否正确")
        return
    
    print(f"✅ 获取到 {len(meetings)} 个会议记录\n")
    
    # 显示会议列表
    print("可用的会议记录：")
    print("-" * 80)
    for i, meeting in enumerate(meetings, 1):
        start_time = meeting.get("start_time", "")
        title = meeting.get("title", "未知会议")
        record_type = meeting.get("record_type", "")
        duration = meeting.get("duration", "0")
        
        # 转换时长为可读格式
        try:
            duration_ms = int(duration)
            duration_sec = duration_ms // 1000
            hours = duration_sec // 3600
            minutes = (duration_sec % 3600) // 60
            seconds = duration_sec % 60
            duration_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        except:
            duration_str = "未知"
        
        print(f"{i:2d}. 标题: {title}")
        print(f"    开始时间: {start_time}")
        print(f"    记录类型: {record_type}")
        print(f"    时长: {duration_str}")
        print()
    
    # 用户选择
    while True:
        print("请选择操作：")
        print("1. 下载指定会议（输入序号）")
        print("2. 下载指定开始时间的会议")
        print("3. 下载所有会议")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            # 下载指定序号的会议
            try:
                index = int(input("请输入会议序号: ")) - 1
                if 0 <= index < len(meetings):
                    meeting = meetings[index]
                    print(f"\n开始下载会议: {meeting.get('title')}")
                    downloader.download_single_meeting(meeting)
                    print("✅ 下载完成！")
                else:
                    print("❌ 无效的序号")
            except ValueError:
                print("❌ 请输入有效的数字")
                
        elif choice == "2":
            # 下载指定开始时间的会议
            start_time = input("请输入会议开始时间 (如: 1748570479000): ").strip()
            if start_time:
                print(f"\n开始下载开始时间为 {start_time} 的会议...")
                downloader.download_meeting_files(target_start_time=start_time)
                print("✅ 下载完成！")
            else:
                print("❌ 请输入有效的开始时间")
                
        elif choice == "3":
            # 下载所有会议
            confirm = input("确定要下载所有会议吗？这可能需要很长时间 (y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                print("\n开始下载所有会议...")
                downloader.download_meeting_files(download_all=True)
                print("✅ 所有会议下载完成！")
            else:
                print("已取消下载")
                
        elif choice == "4":
            print("👋 再见！")
            break
            
        else:
            print("❌ 无效的选择，请重新输入")
        
        print("\n" + "=" * 60)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序出现错误: {str(e)}")
        print("请检查网络连接和Cookie配置")
