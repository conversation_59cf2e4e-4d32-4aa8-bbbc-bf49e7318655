#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试aria2启动功能
"""

import os
from aria2_downloader import start_aria2_if_needed, Aria2Downloader

def test_aria2_path():
    """
    测试aria2路径是否正确
    """
    print("🔍 检查aria2路径...")
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    local_aria2_path = os.path.join(current_dir, "AriaNg-DailyBuild-master", "Aria2", "aria2.exe")
    
    print(f"📁 当前目录: {current_dir}")
    print(f"🎯 aria2路径: {local_aria2_path}")
    
    if os.path.exists(local_aria2_path):
        print("✅ aria2.exe文件存在")
        
        # 获取文件信息
        file_size = os.path.getsize(local_aria2_path)
        print(f"📊 文件大小: {file_size / (1024*1024):.1f} MB")
        
        return True
    else:
        print("❌ aria2.exe文件不存在")
        
        # 列出目录内容帮助调试
        aria2_dir = os.path.dirname(local_aria2_path)
        if os.path.exists(aria2_dir):
            print(f"📂 {aria2_dir} 目录内容:")
            for item in os.listdir(aria2_dir):
                print(f"   - {item}")
        else:
            print(f"❌ 目录不存在: {aria2_dir}")
        
        return False

def test_aria2_startup():
    """
    测试aria2启动功能
    """
    print("\n🚀 测试aria2启动功能...")
    
    # 首先检查aria2是否已经运行
    aria2_test = Aria2Downloader()
    if aria2_test.check_aria2_status():
        print("✅ aria2已在运行，无需启动")
        return True
    
    # 尝试启动aria2
    success = start_aria2_if_needed()
    
    if success:
        print("✅ aria2启动测试成功")
        
        # 再次检查状态
        if aria2_test.check_aria2_status():
            print("✅ aria2状态确认正常")
            return True
        else:
            print("❌ aria2启动后状态检查失败")
            return False
    else:
        print("❌ aria2启动测试失败")
        return False

def main():
    """
    主测试函数
    """
    print("🧪 aria2启动功能测试")
    print("=" * 50)
    
    # 测试1: 检查aria2路径
    path_ok = test_aria2_path()
    
    if not path_ok:
        print("\n❌ aria2路径测试失败，无法继续")
        return
    
    # 测试2: 测试aria2启动
    startup_ok = test_aria2_startup()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   路径检查: {'✅ 通过' if path_ok else '❌ 失败'}")
    print(f"   启动测试: {'✅ 通过' if startup_ok else '❌ 失败'}")
    
    if path_ok and startup_ok:
        print("\n🎉 所有测试通过！aria2功能正常")
    else:
        print("\n⚠️  部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
