Aria2 - CLI Metalink/BitTorrent Client
========================

使用说明
--------------------------------------------------
点击aria2.exe 、a2tray.exe、Start.vbs启动文件之一或Start.cmd，运行 aria2.exe
结合以下图形界面前端面板(Web UI)下载：
1. AriaNg
(1)AriaNg网页版：                                   
由 AriaNg 开发者提供的 Demo 页面（强烈推荐）：http://ariang.mayswind.net/latest  
由 Aria2 完美配置、Aria2 Pro 开发者维护的 AriaNg 网页版：
js.org 提供域名，GitHub Pages 提供网页服务：http://ariang.js.org
eu.org 提供域名，GitHub Pages 提供网页服务，Cloudflare 提供 CDN：http://ariang.eu.org
 Gitee Pages 提供网页服务：http://p3terx.gitee.io/ariang
(2) AriaNg:                                        https://github.com/mayswind/AriaNg/releases
(3)AriaNg-Native：                          https://github.com/mayswind/AriaNg-Native/releases
2. WebUI-Aria2：                            https://ziahamza.github.io/webui-aria2
3. YAAW 中文版-Aria2 Web 控制台：http://aria2c.com/
下载后的文件保存在 “C:\下载” 文件夹中


文件说明
-------------------------------------------------------------------

Aria2 官方下载地址: https://github.com/aria2/aria2/releases/latest
    aria2.conf        # 配置文件 可以自己根据说明修改
    aria2.session    # 任务保存文件 未完成任务会保存在这里
    aria2c.exe        # 命令行主程序
    aria2.exe         # exe启动文件 aria2启动方式一 
    a2tray.exe       # exe启动文件 aria2启动方式二 
    Start.vbs         # vbs启动文件 aria2启动方式三
    Start.cmd       # cmd  临时启动文件 需要cmd窗口始终保持开启，方可临时启动aria2
    Boot.cmd       # cmd  设置或取消Aria2 开机启动
    README.md  # README使用说明文件
 
